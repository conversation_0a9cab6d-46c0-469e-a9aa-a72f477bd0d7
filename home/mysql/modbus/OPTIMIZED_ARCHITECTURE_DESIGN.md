# 基于高性能参考的Modbus协议服务优化设计

## 📋 设计目标

基于参考实现的性能优化策略，重新设计当前项目的业务流程，实现：
- **10x性能提升**：从2,000点/秒提升到20,000点/秒
- **低延时处理**：固定3ms循环周期
- **高并发支持**：支持1000+设备同时处理
- **内存优化**：预分配缓冲区，避免内存碎片

## 🏗️ 核心架构重设计

### 1. 主循环优化设计

#### 1.1 设备级主循环 (替代当前的单线程扫描)
```cpp
class OptimizedDeviceWorker {
private:
    static constexpr int CYCLE_TIME_MS = 3;  // 固定3ms循环周期
    
public:
    void WorkerMainLoop(int device_id) {
        PreallocatedBuffers buffers(device_id);  // 预分配缓冲区
        DeviceRoundRobin scheduler(device_id);   // 轮询调度器
        
        while (!shutdown_requested_) {
            auto cycle_start = std::chrono::high_resolution_clock::now();
            
            // 三阶段处理：接收→处理→发送
            ProcessReceive(device_id, buffers);
            ProcessData(device_id, buffers, scheduler);
            ProcessSend(device_id, buffers);
            
            // 固定周期控制
            SleepUntilNextCycle(cycle_start, CYCLE_TIME_MS);
        }
    }
    
private:
    void SleepUntilNextCycle(auto start_time, int cycle_ms) {
        auto elapsed = std::chrono::high_resolution_clock::now() - start_time;
        auto sleep_time = std::chrono::milliseconds(cycle_ms) - elapsed;
        if (sleep_time > std::chrono::milliseconds(0)) {
            std::this_thread::sleep_for(sleep_time);
        }
    }
};
```

#### 1.2 预分配内存管理
```cpp
class PreallocatedBuffers {
private:
    // 端口级缓冲区 (每设备)
    alignas(64) uint8_t recv_buffer_[4096];     // 4KB接收缓冲区
    alignas(64) uint8_t send_buffer_[1550];     // 发送缓冲区
    alignas(64) uint8_t process_buffer_[2048];  // 处理缓冲区
    
    // 设备控制结构
    struct DeviceControlBlock {
        uint16_t query_index;           // 轮询索引
        uint16_t query_type;            // 查询类型索引
        uint32_t last_poll_time;        // 最后轮询时间
        uint16_t recv_state;            // 接收状态机
        uint16_t recv_length;           // 接收长度
        uint8_t  device_status;         // 设备状态
    } __attribute__((packed)) control_block_;
    
public:
    PreallocatedBuffers(int device_id) {
        // 初始化缓冲区，避免运行时分配
        std::memset(recv_buffer_, 0, sizeof(recv_buffer_));
        std::memset(send_buffer_, 0, sizeof(send_buffer_));
        std::memset(&control_block_, 0, sizeof(control_block_));
    }
    
    uint8_t* GetReceiveBuffer() { return recv_buffer_; }
    uint8_t* GetSendBuffer() { return send_buffer_; }
    DeviceControlBlock* GetControlBlock() { return &control_block_; }
};
```

### 2. 设备轮询优化

#### 2.1 高效轮询调度器
```cpp
class DeviceRoundRobin {
private:
    struct PollConfiguration {
        uint16_t yc_count;              // 遥测点数量
        uint16_t yx_count;              // 遥信点数量
        uint16_t poll_interval_ms;      // 轮询间隔
        std::vector<ModbusRegisterGroup> yc_groups;  // 遥测分组
        std::vector<ModbusRegisterGroup> yx_groups;  // 遥信分组
    };
    
    PollConfiguration config_;
    uint16_t current_query_index_;
    uint16_t current_query_type_;
    
public:
    // O(1)复杂度的设备选择
    ModbusRegisterGroup* GetNextPollGroup() {
        // 轮询类型切换 (遥测/遥信分离)
        if (current_query_type_ >= config_.yc_count + config_.yx_count) {
            current_query_type_ = 0;
        }
        
        ModbusRegisterGroup* group = nullptr;
        if (current_query_type_ < config_.yc_count) {
            // 遥测处理
            group = &config_.yc_groups[current_query_type_];
        } else {
            // 遥信处理
            group = &config_.yx_groups[current_query_type_ - config_.yc_count];
        }
        
        current_query_type_++;
        return group;
    }
    
    bool ShouldPoll() const {
        // 检查轮询间隔
        return Timer::CheckInterval(config_.poll_interval_ms);
    }
};

// 寄存器分组优化 (批量读取)
struct ModbusRegisterGroup {
    uint16_t start_address;
    uint16_t register_count;
    DataType data_type;
    ByteOrder byte_order;
    std::vector<uint16_t> point_mappings;  // 点号映射
    
    // 批量读取优化
    bool CanMergeWith(const ModbusRegisterGroup& other) const {
        return (start_address + register_count == other.start_address) &&
               (data_type == other.data_type) &&
               (byte_order == other.byte_order);
    }
};
```

#### 2.2 智能分组算法
```cpp
class RegisterGroupOptimizer {
public:
    static std::vector<ModbusRegisterGroup> OptimizeGroups(
        const std::vector<DataPoint>& points) {
        
        std::vector<ModbusRegisterGroup> groups;
        
        // 按地址排序
        auto sorted_points = points;
        std::sort(sorted_points.begin(), sorted_points.end(),
                 [](const DataPoint& a, const DataPoint& b) {
                     return a.address < b.address;
                 });
        
        // 合并连续地址
        ModbusRegisterGroup current_group;
        for (const auto& point : sorted_points) {
            if (CanAddToGroup(current_group, point)) {
                AddPointToGroup(current_group, point);
            } else {
                if (!current_group.point_mappings.empty()) {
                    groups.push_back(current_group);
                }
                current_group = CreateNewGroup(point);
            }
        }
        
        if (!current_group.point_mappings.empty()) {
            groups.push_back(current_group);
        }
        
        return groups;
    }
    
private:
    static constexpr int MAX_REGISTERS_PER_GROUP = 125;  // Modbus限制
    
    static bool CanAddToGroup(const ModbusRegisterGroup& group, const DataPoint& point) {
        if (group.point_mappings.empty()) return true;
        
        return (point.address == group.start_address + group.register_count) &&
               (group.register_count < MAX_REGISTERS_PER_GROUP) &&
               (point.data_type == group.data_type);
    }
};
```

### 3. 报文处理状态机优化

#### 3.1 零拷贝接收状态机
```cpp
class OptimizedReceiveStateMachine {
public:
    enum class ReceiveState : uint8_t {
        WAIT_START = 0,
        RECV_ADDRESS = 1,
        RECV_FUNCTION = 2,
        RECV_LENGTH = 3,
        RECV_DATA = 4,
        RECV_CRC = 5,
        RECV_COMPLETE = 6
    };
    
    ProcessResult ProcessReceiveData(uint8_t* buffer, size_t& buffer_pos, 
                                   uint8_t new_byte) {
        switch (current_state_) {
            case ReceiveState::WAIT_START:
                return ProcessWaitStart(buffer, buffer_pos, new_byte);
                
            case ReceiveState::RECV_ADDRESS:
                return ProcessAddress(buffer, buffer_pos, new_byte);
                
            case ReceiveState::RECV_FUNCTION:
                return ProcessFunction(buffer, buffer_pos, new_byte);
                
            case ReceiveState::RECV_LENGTH:
                return ProcessLength(buffer, buffer_pos, new_byte);
                
            case ReceiveState::RECV_DATA:
                return ProcessData(buffer, buffer_pos, new_byte);
                
            case ReceiveState::RECV_CRC:
                return ProcessCRC(buffer, buffer_pos, new_byte);
                
            case ReceiveState::RECV_COMPLETE:
                return ProcessResult::COMPLETE;
        }
        return ProcessResult::CONTINUE;
    }
    
private:
    ReceiveState current_state_ = ReceiveState::WAIT_START;
    uint16_t expected_length_ = 0;
    uint16_t received_length_ = 0;
    uint16_t calculated_crc_ = 0;
    
    // 增量CRC计算 (避免重复计算)
    void UpdateCRC(uint8_t byte) {
        calculated_crc_ = CRC16::UpdateCRC(calculated_crc_, byte);
    }
};
```

#### 3.2 多态数据处理优化
```cpp
class DataTypeProcessor {
private:
    // 函数指针表 (避免大型switch)
    using ProcessorFunc = std::function<double(const uint8_t*, ByteOrder)>;
    static const std::array<ProcessorFunc, 12> processors_;
    
public:
    static double ProcessValue(DataType type, const uint8_t* data, ByteOrder order) {
        auto type_index = static_cast<size_t>(type);
        if (type_index < processors_.size()) {
            return processors_[type_index](data, order);
        }
        return 0.0;
    }
    
private:
    // 特化处理函数
    static double ProcessUInt16(const uint8_t* data, ByteOrder order) {
        uint16_t value = ByteOrderConverter::ToUInt16(data, order);
        return static_cast<double>(value);
    }
    
    static double ProcessFloat32(const uint8_t* data, ByteOrder order) {
        float value = ByteOrderConverter::ToFloat32(data, order);
        return static_cast<double>(value);
    }
    
    // ... 其他类型处理函数
};

// 编译时初始化处理器表
const std::array<DataTypeProcessor::ProcessorFunc, 12> 
DataTypeProcessor::processors_ = {{
    ProcessUInt16,   // Type 0
    ProcessInt16,    // Type 1
    ProcessFloat32,  // Type 2
    ProcessUInt32,   // Type 3
    // ... 其他类型
}};
```

### 4. 并发控制优化

#### 4.1 无锁队列实现
```cpp
template<typename T, size_t Size>
class LockFreeRingBuffer {
private:
    alignas(64) std::array<T, Size> buffer_;
    alignas(64) std::atomic<size_t> head_{0};
    alignas(64) std::atomic<size_t> tail_{0};
    
public:
    bool TryPush(const T& item) {
        size_t current_tail = tail_.load(std::memory_order_relaxed);
        size_t next_tail = (current_tail + 1) % Size;
        
        if (next_tail == head_.load(std::memory_order_acquire)) {
            return false;  // 队列满
        }
        
        buffer_[current_tail] = item;
        tail_.store(next_tail, std::memory_order_release);
        return true;
    }
    
    bool TryPop(T& item) {
        size_t current_head = head_.load(std::memory_order_relaxed);
        
        if (current_head == tail_.load(std::memory_order_acquire)) {
            return false;  // 队列空
        }
        
        item = buffer_[current_head];
        head_.store((current_head + 1) % Size, std::memory_order_release);
        return true;
    }
};

// 命令队列管理
class CommandQueueManager {
private:
    LockFreeRingBuffer<ModbusCommand, 1024> command_queue_;
    LockFreeRingBuffer<ModbusResponse, 1024> response_queue_;
    LockFreeRingBuffer<MonitorData, 2048> monitor_queue_;
    
public:
    bool SendCommand(const ModbusCommand& cmd) {
        return command_queue_.TryPush(cmd);
    }
    
    bool GetResponse(ModbusResponse& resp) {
        return response_queue_.TryPop(resp);
    }
    
    bool SaveMonitorData(const MonitorData& data) {
        return monitor_queue_.TryPush(data);
    }
};
```

#### 4.2 原子操作状态管理
```cpp
class AtomicDeviceState {
private:
    std::atomic<uint32_t> state_word_{0};
    
    // 状态位定义
    static constexpr uint32_t CONNECTED_BIT = 0x01;
    static constexpr uint32_t SCANNING_BIT = 0x02;
    static constexpr uint32_t ERROR_BIT = 0x04;
    static constexpr uint32_t QUERY_INDEX_MASK = 0xFFFF0000;
    static constexpr uint32_t QUERY_INDEX_SHIFT = 16;
    
public:
    void SetConnected(bool connected) {
        if (connected) {
            state_word_.fetch_or(CONNECTED_BIT, std::memory_order_relaxed);
        } else {
            state_word_.fetch_and(~CONNECTED_BIT, std::memory_order_relaxed);
        }
    }
    
    bool IsConnected() const {
        return (state_word_.load(std::memory_order_relaxed) & CONNECTED_BIT) != 0;
    }
    
    void SetQueryIndex(uint16_t index) {
        uint32_t new_value = static_cast<uint32_t>(index) << QUERY_INDEX_SHIFT;
        uint32_t old_value = state_word_.load(std::memory_order_relaxed);
        uint32_t updated_value;
        
        do {
            updated_value = (old_value & ~QUERY_INDEX_MASK) | new_value;
        } while (!state_word_.compare_exchange_weak(old_value, updated_value,
                                                   std::memory_order_relaxed));
    }
    
    uint16_t GetQueryIndex() const {
        return static_cast<uint16_t>(
            (state_word_.load(std::memory_order_relaxed) & QUERY_INDEX_MASK) 
            >> QUERY_INDEX_SHIFT);
    }
};
```

### 5. 定时器系统优化

#### 5.1 层次化定时器管理
```cpp
class HierarchicalTimerSystem {
private:
    // 不同级别的定时器
    struct TimerLevel {
        std::chrono::milliseconds interval;
        std::chrono::steady_clock::time_point last_trigger;
        bool enabled;
    };
    
    TimerLevel port_timer_;     // 端口级定时器
    TimerLevel device_timer_;   // 设备级定时器
    TimerLevel command_timer_;  // 命令级定时器
    
public:
    bool CheckPortTimer() {
        return CheckTimer(port_timer_);
    }
    
    bool CheckDeviceTimer() {
        return CheckTimer(device_timer_);
    }
    
    bool CheckCommandTimer() {
        return CheckTimer(command_timer_);
    }
    
    void StartCommandTimer(std::chrono::milliseconds timeout) {
        command_timer_.interval = timeout;
        command_timer_.last_trigger = std::chrono::steady_clock::now();
        command_timer_.enabled = true;
    }
    
private:
    bool CheckTimer(TimerLevel& timer) {
        if (!timer.enabled) return false;
        
        auto now = std::chrono::steady_clock::now();
        if (now - timer.last_trigger >= timer.interval) {
            timer.last_trigger = now;
            return true;
        }
        return false;
    }
};
```

#### 5.2 批量定时器更新
```cpp
class BatchTimerUpdater {
private:
    struct TimerBatch {
        std::vector<std::chrono::steady_clock::time_point*> timers;
        std::chrono::steady_clock::time_point batch_time;
    };
    
    TimerBatch current_batch_;
    
public:
    void AddToCurrentBatch(std::chrono::steady_clock::time_point* timer) {
        current_batch_.timers.push_back(timer);
    }
    
    void FlushBatch() {
        auto now = std::chrono::steady_clock::now();
        for (auto* timer : current_batch_.timers) {
            *timer = now;
        }
        current_batch_.timers.clear();
        current_batch_.batch_time = now;
    }
};
```

## 🚀 实施计划

### 阶段1：核心架构重构 (3-4周)
1. 实现预分配内存管理
2. 重构主循环为固定周期模式
3. 实现设备轮询调度器

### 阶段2：通信优化 (2-3周)
1. 实现零拷贝接收状态机
2. 优化数据类型处理
3. 实现寄存器分组批量读取

### 阶段3：并发优化 (2-3周)
1. 实现无锁队列
2. 优化原子操作状态管理
3. 实现层次化定时器系统

### 阶段4：性能调优 (1-2周)
1. 性能基准测试
2. 内存对齐优化
3. CPU缓存优化

## 📊 预期性能提升

| 优化项目 | 当前性能 | 优化后性能 | 提升倍数 |
|----------|----------|------------|----------|
| **数据处理速度** | 2,000点/秒 | 20,000点/秒 | **10x** |
| **循环延时** | 200-1000ms | 3ms固定 | **67-333x** |
| **内存效率** | 动态分配 | 预分配零拷贝 | **5-10x** |
| **CPU使用率** | 50-70% | 20-30% | **2-3x** |
| **支持设备数** | 100台 | 1000台 | **10x** |

这个优化设计将显著提升系统的实时性、吞吐量和可扩展性，同时保持代码的可维护性。
