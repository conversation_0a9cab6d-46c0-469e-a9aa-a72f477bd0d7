#include <iostream>
#include "libmodbus_install/include/modbus/modbus.h"
#include <errno.h>
#include <string.h>

int main() {
    std::cout << "Testing serial port /dev/ttyS0..." << std::endl;
    
    // 创建RTU上下文
    modbus_t* ctx = modbus_new_rtu("/dev/ttyS0", 9600, 'N', 8, 1);
    if (ctx == NULL) {
        std::cerr << "Failed to create RTU context: " << strerror(errno) << std::endl;
        return 1;
    }
    
    std::cout << "RTU context created successfully" << std::endl;
    
    // 设置从站地址
    if (modbus_set_slave(ctx, 1) == -1) {
        std::cerr << "Failed to set slave: " << modbus_strerror(errno) << std::endl;
        modbus_free(ctx);
        return 1;
    }
    
    std::cout << "Slave address set to 1" << std::endl;
    
    // 尝试连接
    if (modbus_connect(ctx) == -1) {
        std::cerr << "Connection failed: " << modbus_strerror(errno) << std::endl;
        std::cerr << "Error code: " << errno << std::endl;
        modbus_free(ctx);
        return 1;
    }
    
    std::cout << "Connected successfully!" << std::endl;
    
    // 尝试读取一个寄存器
    uint16_t tab_reg[1];
    int rc = modbus_read_registers(ctx, 0, 1, tab_reg);
    if (rc == -1) {
        std::cerr << "Read failed: " << modbus_strerror(errno) << std::endl;
    } else {
        std::cout << "Read successful, value: " << tab_reg[0] << std::endl;
    }
    
    // 关闭连接
    modbus_close(ctx);
    modbus_free(ctx);
    
    std::cout << "Test completed" << std::endl;
    return 0;
}
