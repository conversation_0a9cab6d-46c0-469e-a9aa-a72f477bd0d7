#include "src/config/xml_config_parser.h"
#include <iostream>
#include <fstream>

using namespace modbus;

int main() {
    std::cout << "开始XML解析测试..." << std::endl;
    
    // 读取XML配置文件
    std::ifstream file("build/bin/modbus_config.xml");
    if (!file.is_open()) {
        std::cout << "无法打开配置文件" << std::endl;
        return 1;
    }
    
    std::stringstream buffer;
    buffer << file.rdbuf();
    std::string xml_content = buffer.str();
    file.close();
    
    std::cout << "XML文件内容长度: " << xml_content.length() << std::endl;
    
    // 解析XML
    XmlConfigParser parser;
    auto xml_result = parser.ParseXml(xml_content);
    
    if (!xml_result.IsSuccess()) {
        std::cout << "XML解析失败: " << xml_result.error_message << std::endl;
        return 1;
    }
    
    auto root = xml_result.data;
    std::cout << "根节点: " << root->name << std::endl;
    std::cout << "根节点子节点数量: " << root->children.size() << std::endl;
    
    // 查找devices节点
    auto devices_node = root->FindChild("devices");
    if (!devices_node) {
        std::cout << "未找到devices节点" << std::endl;
        return 1;
    }
    
    std::cout << "找到devices节点，子节点数量: " << devices_node->children.size() << std::endl;
    
    // 查找第一个device节点
    auto device_nodes = devices_node->FindChildren("device");
    if (device_nodes.empty()) {
        std::cout << "未找到device节点" << std::endl;
        return 1;
    }
    
    auto device_node = device_nodes[0];
    std::cout << "找到device节点，ID: " << device_node->GetIntAttribute("id", 0) << std::endl;
    std::cout << "device节点子节点数量: " << device_node->children.size() << std::endl;
    
    // 列出所有子节点
    for (const auto& child : device_node->children) {
        std::cout << "  子节点: " << child->name << std::endl;
    }
    
    // 查找data_points节点
    auto data_points_node = device_node->FindChild("data_points");
    if (!data_points_node) {
        std::cout << "未找到data_points节点" << std::endl;
        return 1;
    }
    
    std::cout << "找到data_points节点，子节点数量: " << data_points_node->children.size() << std::endl;
    
    // 查找YC节点
    auto yc_node = data_points_node->FindChild("YC");
    if (!yc_node) {
        std::cout << "未找到YC节点" << std::endl;
        return 1;
    }
    
    std::cout << "找到YC节点，子节点数量: " << yc_node->children.size() << std::endl;
    
    // 查找point节点
    auto point_nodes = yc_node->FindChildren("point");
    std::cout << "找到 " << point_nodes.size() << " 个point节点" << std::endl;
    
    for (const auto& point_node : point_nodes) {
        std::cout << "point节点文本内容: '" << point_node->text << "'" << std::endl;
    }
    
    std::cout << "XML解析测试完成" << std::endl;
    return 0;
}
