# libRTU_RlyModbus.so.1.0.0 库分析报告

## 概述

本报告基于IDA Pro MCP服务对 `libRTU_RlyModbus.so.1.0.0` 库进行的逆向分析，详细描述了该库的业务逻辑、报文发送与解析机制。

### 库基本信息
- **文件名**: libRTU_RlyModbus.so.1.0.0
- **文件大小**: 0x17300 (94976 字节)
- **基址**: 0x0
- **代码段大小**: 0xbee0
- **MD5**: aa54f58615fcb9dfa49156d132efaa63
- **SHA256**: 77141620eda63247a98e21eb220aaad66cab4e326de90e885858485bf8a554cb

## 核心架构

### 主要模块组成

1. **初始化模块** (`RlyModbus_Init*`)
2. **通信接收模块** (`RlyModbus_Recv`)
3. **报文处理模块** (`RlyModbus_Proc*`)
4. **通信发送模块** (`RlyModbus_Send*`)
5. **数据类型处理模块** (`RlyModbus_Proc_Mv*`, `RlyModbus_Proc_St*`)

### 主线程执行流程

```c
void *RlyModbus(void *arg) {
    // 1. 更新任务信息
    Daemon_UpdateTaskInfo(2, (unsigned __int16)arg, 10);
    
    // 2. 初始化Modbus
    if (RlyModbus_Init((unsigned __int16)arg)) {
        // 3. 主循环
        while (!IfAppExit()) {
            Daemon_UpdateTaskInfo(2, (unsigned __int16)arg, 10);
            ct_sMsSleep(3);                    // 3ms延时
            RlyModbus_Recv((unsigned __int16)arg);   // 接收处理
            RlyModbus_Proc((unsigned __int16)arg);   // 报文处理
            RlyModbus_Send((unsigned __int16)arg);   // 发送处理
        }
    }
    return (void *)-1;
}
```

## 初始化流程

### RlyModbus_Init 函数分析

初始化过程包括以下关键步骤：

1. **端口验证**
   - 检查端口运行时控制结构
   - 验证端口类型为1（Modbus RTU）
   - 验证协议类型为2

2. **配置加载**
   - 调用 `RlyModbus_Init_Model()` 初始化模型
   - 调用 `RlyModbus_Init_Cfg()` 初始化配置
   - 为每个IED分配6004字节的Modbus控制结构

3. **参数解析**
   - 解析ModbusParam配置参数
   - 支持6种参数类型（0-5）
   - 参数格式：`%x,%x,%x,%x,%x,%x,%x,%x`

4. **功能码配置**
   - 支持数据分片传输
   - 配置功能控制结构（20字节/项）
   - 最多支持299个功能项

## 报文接收机制

### 接收状态机

`RlyModbus_Recv` 实现了完整的Modbus RTU报文接收状态机：

```
状态0: 等待起始字节 (HIBYTE(ptPortRtCtl[3099]))
状态1: 接收设备地址和功能码
状态2: 接收数据长度
状态3: 接收数据内容
状态4: 接收CRC校验
状态5: 报文接收完成
```

### 关键特性

1. **循环缓冲区管理**
   - 4KB接收缓冲区
   - 读写指针自动回绕
   - 防止缓冲区溢出

2. **CRC校验**
   - 使用 `CRC_CalcCrc16` 进行校验
   - 校验失败自动重置状态机

3. **超时处理**
   - 600ms接收超时
   - 超时后自动重置接收状态

## 报文处理逻辑

### 主处理函数 RlyModbus_Proc

处理流程：

1. **报文验证**
   - 检查接收状态为5（完成）
   - 验证设备地址匹配
   - 查找对应的IED配置

2. **功能码分类处理**
   - **读取功能码** (1-4): 处理遥测(Yc)和遥信(Yx)数据
   - **写入功能码** (5,6,15,16): 处理命令确认
   - **异常响应** (0x80+): 错误处理

3. **数据类型处理**
   - 支持12种数据类型（0-11）
   - 包括各种整型、浮点型、BCD码等

### 数据类型支持

| 类型ID | 名称 | 描述 |
|--------|------|------|
| 0 | MvU16 | 无符号16位整数 |
| 1 | MvI16 | 有符号16位整数 |
| 2 | MvF32 | 32位浮点数 |
| 3 | MvU32 | 无符号32位整数 |
| 4 | MvI32 | 有符号32位整数 |
| 5 | MvS16 | 带符号位的16位整数 |
| 6 | MvS32 | 带符号位的32位整数 |
| 7 | MvF64 | 64位双精度浮点数 |
| 8 | MvBCD | BCD码 |
| 9 | MvU64 | 无符号64位整数 |
| 10 | MvX16 | 扩展16位 |
| 11 | MvY16 | Y型16位 |

## 遥测数据处理 (Yc)

### RlyModbus_Proc_Yc 函数分析

遥测数据处理支持多种数据格式：

1. **16位数据** (类型0,1)
   - 支持大端/小端字节序
   - 有符号/无符号处理

2. **32位数据** (类型2,3)
   - 4种字节序模式
   - 浮点数和整数支持

3. **64位双精度** (类型5)
   - 复杂的字节序处理
   - 8字节数据重组

### 字节序处理模式

- **模式0**: 标准大端 (AB CD)
- **模式2**: 字节交换 (DC BA)  
- **模式3**: 字交换 (CD AB)
- **模式4**: 混合模式 (BA DC)

## 发送机制

### RlyModbus_Send 函数

发送优先级：
1. 主站命令 (`RlyModbus_Send_HostCmd`)
2. 轮询命令 (`RlyModbus_Send_Polling`)

### 发送条件
- 硬件发送不忙
- 有待发送数据
- 发送标志置位

## 配置参数

### ModbusParam 参数类型

```c
// 参数类型0-4: 功能配置
// 格式: FuncCode,DataAddr,DataLen,StartInf,DataType,Param,Sign,Split
// 示例: 3,1000,10,0,0,1,0,0

// 参数类型5: 时间同步
// 格式: DataAddr,DataLen,TimeFormat
// 示例: 2000,6,013579BCD
```

## 错误处理

1. **通信超时**: 600ms接收超时
2. **CRC错误**: 自动重传机制
3. **设备不响应**: 设备通信状态更新
4. **缓冲区溢出**: 自动重置缓冲区指针

## 线程安全

- 使用互斥锁保护共享资源
- 原子操作更新状态变量
- 线程间通信使用队列机制

## 性能特性

- **轮询间隔**: 可配置的空闲间隔
- **并发处理**: 支持多端口并发
- **内存管理**: 动态分配Modbus控制结构
- **实时性**: 3ms主循环周期

## 报文格式详解

### Modbus RTU 报文结构

```
+----------+----------+----------+----------+----------+----------+
| 设备地址 | 功能码   | 数据域   | ...      | CRC低位  | CRC高位  |
| (1字节)  | (1字节)  | (N字节)  |          | (1字节)  | (1字节)  |
+----------+----------+----------+----------+----------+----------+
```

### 支持的功能码

| 功能码 | 名称 | 描述 | 数据域格式 |
|--------|------|------|------------|
| 0x01 | 读线圈状态 | 读取离散输出状态 | 起始地址(2) + 数量(2) |
| 0x02 | 读离散输入 | 读取离散输入状态 | 起始地址(2) + 数量(2) |
| 0x03 | 读保持寄存器 | 读取模拟输出 | 起始地址(2) + 数量(2) |
| 0x04 | 读输入寄存器 | 读取模拟输入 | 起始地址(2) + 数量(2) |
| 0x05 | 写单个线圈 | 写单个离散输出 | 地址(2) + 值(2) |
| 0x06 | 写单个寄存器 | 写单个模拟输出 | 地址(2) + 值(2) |
| 0x0F | 写多个线圈 | 写多个离散输出 | 地址(2) + 数量(2) + 字节数(1) + 数据(N) |
| 0x10 | 写多个寄存器 | 写多个模拟输出 | 地址(2) + 数量(2) + 字节数(1) + 数据(N) |

### 异常响应格式

```
+----------+----------+----------+----------+----------+
| 设备地址 | 功能码+0x80 | 异常码 | CRC低位  | CRC高位  |
| (1字节)  | (1字节)     | (1字节) | (1字节)  | (1字节)  |
+----------+----------+----------+----------+----------+
```

## 数据解析详解

### 遥信数据处理 (Yx)

遥信数据按位处理，支持：
- 单点遥信：1位表示一个开关状态
- 双点遥信：2位表示一个开关状态（分/合/故障/无效）
- 品质位处理：数据有效性标识

### 遥测数据处理 (Yc) 详细分析

#### 数据类型转换矩阵

| 原始类型 | 字节序模式 | 转换过程 | 目标格式 |
|----------|------------|----------|----------|
| U16 | 0(大端) | [AB] → AB | uint16 |
| U16 | 1(小端) | [AB] → BA | uint16 |
| F32 | 0 | [ABCD] → ABCD | IEEE754 |
| F32 | 2 | [ABCD] → DCBA | IEEE754 |
| F32 | 3 | [ABCD] → CDAB | IEEE754 |
| F32 | 4 | [ABCD] → BADC | IEEE754 |
| F64 | 各种模式 | 8字节重组 | IEEE754双精度 |

#### BCD码处理

BCD码解析支持：
- 压缩BCD：1字节表示2位十进制数
- 非压缩BCD：1字节表示1位十进制数
- 符号位处理：最高位表示正负

## 通信时序

### 轮询时序

```
主站                           从站
 |                              |
 |------- 查询报文 ----------->|
 |                              |
 |<------ 响应报文 ------------|
 |                              |
 |------ 空闲间隔 --------------|
 |                              |
 |------- 下一查询 ----------->|
```

### 超时处理

- **字符间超时**: 1.5个字符时间
- **帧间超时**: 3.5个字符时间
- **响应超时**: 600ms（可配置）
- **重传机制**: 自动重传3次

## 配置文件格式

### 模型配置文件

```ini
[ModelInfo]
ModelName=TP02DMODBUS
YcNum=100        ; 遥测点数
YxNum=200        ; 遥信点数
YkNum=50         ; 遥控点数
YsNum=30         ; 遥调点数
PollInterval=1000 ; 轮询间隔(ms)

[YcInfo]
;功能码，起始地址，数量，装置ID/内序号，类型，解析参数
1,3,1000,2,0,1001
2,3,1002,2,0,1002

[YxInfo]
;功能码，起始地址，数量，装置ID/内序号，类型，解析参数
1,2,2000,0,2001
2,2,2000,1,2002

[YkInfo]
;格式: 序号,点号,起始地址,合闸值,分闸偏移,分闸值,数据类型
1,3001,3000,0xFF00,1,0x0000,0
2,3002,3002,0xFF00,1,0x0000,0
3,3003,3004,0xFF00,1,0x0000,0

[YsInfo]
;格式: 序号,点号,起始地址,数据类型,字节序,最小值,最大值
1,4001,4000,2,0,-1000.0,1000.0
2,4002,4002,2,0,0.0,100.0
3,4003,4004,1,0,0,65535
```

#### 遥控配置详解

**YkInfo字段说明:**
- **序号**: 遥控点序列号
- **点号**: RTDB中的遥控点号
- **寄存器地址**: Modbus寄存器地址
- **合闸值**: 合闸操作写入值 (通常0xFF00)
- **分闸偏移**: 分闸寄存器相对偏移
- **分闸值**: 分闸操作写入值 (通常0x0000)
- **数据类型**: 0=16位, 1=32位

**遥控操作流程:**
```c
// 合闸操作: 写入合闸寄存器
WriteRegister(基址 + 遥控地址, 合闸值);

// 分闸操作: 写入分闸寄存器
WriteRegister(基址 + 遥控地址 + 分闸偏移, 分闸值);
```

#### 遥调配置详解

**YsInfo字段说明:**
- **序号**: 遥调点序列号
- **点号**: RTDB中的遥调点号
- **寄存器地址**: Modbus寄存器地址
- **数据类型**: 0=U16, 1=I16, 2=F32, 3=U32, 4=I32
- **字节序**: 0=大端, 1=小端, 2=字节交换, 3=字交换
- **最小值**: 允许的最小设定值
- **最大值**: 允许的最大设定值

**遥调数据转换:**
```c
// 根据数据类型和字节序转换数据
switch (数据类型) {
    case 0: // U16
        value = (uint16_t)设定值;
        break;
    case 2: // F32
        *(float*)&value = 设定值;
        // 根据字节序重排字节
        break;
}
```

### Modbus参数配置

```ini
[ModbusParam]
; 遥测配置: 功能码,起始地址,长度,起始信息点,数据类型,参数,符号,分片
YcParam=3,1000,50,1001,2,1,0,10

; 遥信配置: 功能码,起始地址,长度,起始信息点,数据类型
YxParam=2,2000,16,2001,1,0,0,0

; 遥控配置: 功能码,起始地址,长度,起始信息点,数据类型,参数,符号,分片
YkParam=5,3000,20,3001,0,1,0,0

; 遥调配置: 功能码,起始地址,长度,起始信息点,数据类型,参数,符号,分片
YsParam=6,4000,10,4001,2,1,0,0

; 时间同步配置: 地址,长度,格式
TimeSync=3000,6,013579BCD
```

#### 参数详细说明

**遥测参数 (YcParam)**
- 功能码: 3 (读保持寄存器)
- 起始地址: 1000 (寄存器起始地址)
- 长度: 50 (读取50个寄存器)
- 起始信息点: 1001 (RTDB中的起始点号)
- 数据类型: 2 (32位浮点数)
- 参数: 1 (字节序模式)
- 符号: 0 (无符号)
- 分片: 10 (每次最多读10个寄存器)

**遥信参数 (YxParam)**
- 功能码: 2 (读离散输入)
- 起始地址: 2000 (输入起始地址)
- 长度: 16 (读取16个输入)
- 起始信息点: 2001 (RTDB中的起始点号)
- 数据类型: 1 (布尔型)

**遥控参数 (YkParam)**
- 功能码: 5 (写单个线圈)
- 起始地址: 3000 (线圈起始地址)
- 长度: 20 (支持20个遥控点)
- 起始信息点: 3001 (RTDB中的起始点号)
- 数据类型: 0 (16位整数)
- 参数: 1 (控制模式)

**遥调参数 (YsParam)**
- 功能码: 6 (写单个寄存器)
- 起始地址: 4000 (寄存器起始地址)
- 长度: 10 (支持10个遥调点)
- 起始信息点: 4001 (RTDB中的起始点号)
- 数据类型: 2 (32位浮点数)
- 参数: 1 (字节序模式)

## 实时数据库接口

### 数据更新接口

```c
// 遥测数据更新
Rtdb_UpdateMvData(&tSigPosi, value, quality);

// 遥信数据更新
Rtdb_UpdateStData(&tSigPosi, state, quality);

// 事件数据更新
Rtdb_UpdateEpData(&tSigPosi, event, timestamp);
```

### 数据结构

```c
typedef struct {
    unsigned short wIndex;   // 数据索引
    unsigned short wIedID;   // 设备ID
    unsigned short wCpuNo;   // CPU编号
    unsigned short wCode;    // 信息点号
} tagSigPosi;
```

## 故障诊断

### 通信状态监控

1. **端口状态**: 端口开启/关闭状态
2. **设备状态**: 设备在线/离线状态
3. **通信质量**: 成功率统计
4. **错误计数**: 各类错误统计

### 调试信息

- 报文收发日志
- 状态机转换日志
- 错误详细信息
- 性能统计数据

## 遥控遥调逻辑详解

### 遥控(Yk)处理流程

#### 1. 遥控命令接收 (`RlyModbus_Send_HostCmd`)

遥控命令通过队列接收，支持两种命令类型：
- **类型64**: 单点遥控命令
- **类型66**: 多点遥调命令

#### 2. 遥控命令验证

```c
// 命令格式验证
if (byMsgBuf[4] != 64 && byMsgBuf[4] != 66) return 0;

// 设备验证
wIedID = *(WORD*)&byMsgBuf[2];
ptIedRtCtl = Pub_GetIedRtCtlByID(wIedID);
```

#### 3. 遥控步骤处理

遥控采用**三步式**操作模式：

| 步骤 | 编码 | 描述 | 操作 |
|------|------|------|------|
| 选择 | 0x80 | 选择操作点 | 发送选择命令到设备 |
| 执行 | 0x40 | 执行操作 | 发送执行命令 |
| 撤销 | 0xC0 | 撤销选择 | 取消选择状态 |

#### 4. 遥控命令类型

```c
// 遥控命令编码
#define YK_CLOSE    1    // 合闸
#define YK_OPEN     10   // 分闸
#define YK_RESET    0    // 复归
#define YK_ALARM    9    // 告警
```

#### 5. 遥控地址映射

```c
// 根据模型信息查找遥控点
ptYkInfo = *(tagPYkInfo*)(ptModelInfo + 60);
while (wLoop < wNumYk && ptYkInfo->wCode != wDataAddr) {
    ++wLoop;
    ++ptYkInfo;
}

// 计算实际寄存器地址
if (byCmd == 1 || byCmd == 10) {
    wDataAddr = ptYkInfo->wRegAddr;           // 直接地址
    wDataValue = ptYkInfo->wOnVal;            // 合闸值
} else {
    wDataAddr = ptYkInfo->wRegAddr + ptYkInfo->wOffShift;  // 偏移地址
    wDataValue = ptYkInfo->wOffVal;           // 分闸值
}
```

### 遥调(Ys)处理流程

#### 1. 遥调命令结构

```c
typedef struct {
    BYTE byNum;           // 调节点数量
    struct {
        WORD wCode;       // 信息点号
        BYTE byCmd;       // 命令类型(0-2)
        BYTE byStep;      // 步骤(0x80/0x40/0xC0)
        WORD wValue;      // 调节值
    } Items[10];          // 最多10个调节点
} YsCommand;
```

#### 2. 遥调数据类型处理

支持多种数据类型的遥调：

```c
switch (ptYsInfo->byType) {
    case 0:  // 16位整数
    case 1:  // 16位有符号整数
        wMsgLen += 2;
        wRegAddr += 1;
        break;

    case 2:  // 32位整数
    case 3:  // 32位有符号整数
    case 4:  // 32位浮点数
        wMsgLen += 4;
        wRegAddr += 2;
        break;

    case 5:  // 64位双精度
        wMsgLen += 8;
        wRegAddr += 4;
        break;
}
```

#### 3. 字节序转换

遥调支持4种字节序模式：

```c
switch (ptYsInfo->byOrder) {
    case 0:  // 标准大端 AB CD
        byTmpBuf[wMsgLen++] = BYTE1(iDataValue);
        byTmpBuf[wMsgLen++] = iDataValue;
        break;

    case 2:  // 字节交换 DC BA
        byTmpBuf[wMsgLen++] = HIBYTE(iDataValue);
        byTmpBuf[wMsgLen++] = BYTE2(iDataValue);
        byTmpBuf[wMsgLen++] = BYTE1(iDataValue);
        byTmpBuf[wMsgLen++] = iDataValue;
        break;

    case 3:  // 字交换 CD AB
        byTmpBuf[wMsgLen++] = iDataValue;
        byTmpBuf[wMsgLen++] = BYTE1(iDataValue);
        byTmpBuf[wMsgLen++] = BYTE2(iDataValue);
        byTmpBuf[wMsgLen++] = HIBYTE(iDataValue);
        break;

    case 4:  // 混合模式 BA DC
        byTmpBuf[wMsgLen++] = BYTE2(iDataValue);
        byTmpBuf[wMsgLen++] = HIBYTE(iDataValue);
        byTmpBuf[wMsgLen++] = iDataValue;
        byTmpBuf[wMsgLen++] = BYTE1(iDataValue);
        break;
}
```

### 命令确认处理 (`RlyModbus_Proc_CmdAck`)

#### 1. 确认报文格式

```c
typedef struct {
    BYTE byLen;           // 报文长度
    BYTE byReserved;      // 保留字节
    WORD wIedID;          // 设备ID
    BYTE byType;          // 报文类型(64)
    BYTE byResult;        // 执行结果(0x81)
    WORD wMsgLen;         // 消息长度(12)
    WORD wCode;           // 信息点号
    WORD wCmd;            // 命令字
    BYTE byStatus;        // 执行状态
} CmdAckMsg;
```

#### 2. 确认状态码

| 状态码 | 含义 | 描述 |
|--------|------|------|
| 0 | 成功 | 命令执行成功 |
| 1 | 失败 | 命令执行失败 |
| 8 | 忙碌 | 设备忙碌，拒绝执行 |
| 2 | 无效 | 命令无效 |

#### 3. 异常处理

```c
// 检查异常响应
if ((pbyMsgBuf[1] & 0x80) != 0) {
    byTag = 1;  // 异常标志
} else {
    // 验证命令参数匹配
    if (pbyMsgBuf[2] != *(BYTE*)(ptCmdRtCtl + 22) ||
        pbyMsgBuf[3] != *(BYTE*)(ptCmdRtCtl + 23) ||
        pbyMsgBuf[4] != *(BYTE*)(ptCmdRtCtl + 24) ||
        pbyMsgBuf[5] != *(BYTE*)(ptCmdRtCtl + 25)) {
        return;  // 参数不匹配，忽略
    }
    byTag = 0;  // 正常标志
}
```

### 实时库通知机制

#### 1. 队列通知接口

```c
// 保存响应到队列
Que_SaveRspToQueue(wCmdSource, wIedID, byMsgBuf, wMsgLen);

// 保存监视数据到队列
Que_SaveMonToQueue(g_tSysRtCtl[12], wPortNo, 0, wParaLen, pbyMsgBuf);
```

#### 2. 实时数据更新

```c
// 遥测数据更新
typedef struct {
    WORD wIndex;    // 数据索引
    WORD wIedID;    // 设备ID
    WORD wCpuNo;    // CPU编号
    WORD wCode;     // 信息点号
} tagSigPosi;

// 更新遥测值
Rtdb_UpdateMvData(&tSigPosi, value, quality);

// 更新遥信状态
Rtdb_UpdateStData(&tSigPosi, state, quality);

// 更新事件数据
Rtdb_UpdateEpData(&tSigPosi, event, timestamp);
```

#### 3. 通信状态管理

```c
// 重置端口通信状态
Pub_ResetPortComm(wPortNo, 120);

// 重置设备通信状态
Pub_ResetIedComm(wIedID, wPortNo, 120);

// 更新任务状态
Daemon_UpdateTaskInfo(2, wPortNo, 10);
Daemon_UpdateTaskError(2, wPortNo);
```

### 定时器管理

#### 1. 命令超时处理

```c
// 启动命令超时定时器(60秒)
Timer_StartTimer(ptCmdRtCtl, 60, 1);

// 检查定时器到期
if (Timer_CheckTimer(ptCmdRtCtl)) {
    // 超时处理
    *(WORD*)(ptCmdRtCtl + 12) = -1;
    Timer_StopTimer(ptCmdRtCtl);
}
```

#### 2. 轮询间隔控制

```c
// 设置轮询间隔
wPollIntv = *(WORD*)(ptPortSet + 10);
if (wPollIntv <= 0x63) wPollIntv = 1000;  // 最小1秒

Timer_StartTimer(ptPortRtCtl + 2062, wPollIntv, 0);
```

## 设备在线判断与双机切换逻辑

### 设备在线状态检测机制

#### 1. 通信状态监控

系统通过多层次的状态监控来判断设备是否在线：

```c
// 端口级通信状态重置 (120秒超时)
Pub_ResetPortComm(wPortNo, 120);

// 设备级通信状态重置 (120秒超时)
Pub_ResetIedComm(wIedID, wPortNo, 120);

// 任务状态更新
Daemon_UpdateTaskInfo(2, wPortNo, 10);
Daemon_UpdateTaskError(2, wPortNo);
```

#### 2. 轮询超时检测

```c
// 轮询间隔定时器检查
if (!(unsigned __int8)Timer_CheckTimer(ptPortRtCtl + 2062))
    return 0;  // 轮询间隔未到，不发送

// 设置下次轮询间隔
wPollIntv = *(WORD*)(ptPortSet + 10);
if (wPollIntv <= 0x63) wPollIntv = 1000;  // 最小1秒
Timer_StartTimer(ptPortRtCtl + 2062, wPollIntv, 0);
```

#### 3. 接收超时处理

```c
// 600ms接收超时检测
if (Timer_CheckTimer(ptIedRtCtl + 17) == 1) {
    Timer_StartTimer(ptIedRtCtl + 17, 600, 1);
    // 发送时间同步或轮询命令
}
```

### 双机切换逻辑

#### 1. 端口配置检查

```c
// 检查设备是否启用
if (!*(_BYTE*)(ptIedSet + 20))
    return 0;  // 设备未启用

// 双端口切换逻辑
if (*(_WORD*)(ptIedSet + 8) != *(_WORD*)(ptIedSet + 6) &&
    *((_WORD*)ptIedRtCtl + 46) != wPortNo)
    return 0;  // 当前端口不是活动端口
```

**关键字段说明：**
- `ptIedSet + 6`: 主端口号
- `ptIedSet + 8`: 备端口号
- `ptIedRtCtl + 46`: 当前活动端口号

#### 2. 端口切换条件

系统支持**主备端口自动切换**：

| 条件 | 主端口号 | 备端口号 | 切换逻辑 |
|------|----------|----------|----------|
| 正常模式 | 相同 | 相同 | 单端口模式，无切换 |
| 双机模式 | 不同 | 不同 | 双端口模式，自动切换 |

```c
// 双机切换判断逻辑
if (主端口号 != 备端口号) {
    // 双机模式
    if (当前端口 != 活动端口) {
        return 0;  // 非活动端口不发送
    }
} else {
    // 单机模式，任意端口都可发送
}
```

#### 3. 通信设备轮询

```c
// 获取当前轮询设备索引
wQueryIdx = *((_WORD*)ptPortRtCtl + 4142);
if (*(_WORD*)(ptPortSet + 14) <= wQueryIdx)
    wQueryIdx = 0;  // 索引回绕

// 更新下一个轮询索引
*((_WORD*)ptPortRtCtl + 4142) = wQueryIdx + 1;

// 获取通信设备信息
ptCommDev = (tagPCommDev)(*(_DWORD*)(ptPortSet + 16) + 6 * wQueryIdx);
if (ptCommDev->wCommType)
    return 0;  // 跳过非Modbus设备
```

#### 4. 设备故障检测

```c
// 检查设备运行时控制结构
ptIedRtCtl = (int*)Pub_GetIedRtCtlByID(ptCommDev->wCommID);
if (!ptIedRtCtl)
    return 0;  // 设备不存在

// 检查设备配置
ptIedSet = ptIedRtCtl[1];
if (!*(_BYTE*)(ptIedSet + 20))
    return 0;  // 设备未启用
```

### 故障切换流程

#### 1. 通信故障检测

```c
// 报文接收完成后的处理
if (*(_WORD*)(ptPortRtCtl + 4122) == 5) {
    // 重置端口通信状态 (120秒)
    Pub_ResetPortComm(wPortNo, 120);

    // 重置设备通信状态 (120秒)
    Pub_ResetIedComm(wIedID, wPortNo, 120);

    // 设置空闲间隔
    wIdleIntv = *(_WORD*)(*(_DWORD*)ptPortRtCtl + 12);
    if (wIdleIntv)
        Timer_StartTimer(ptPortRtCtl + 8248, wIdleIntv, 0);
    else
        Timer_SetExpire(ptPortRtCtl + 8248);
}
```

#### 2. 自动切换机制

当主端口通信故障时，系统自动切换到备端口：

```c
// 端口状态检查
typedef struct {
    WORD wCommType;    // 通信类型 (0=Modbus)
    WORD wCommID;      // 设备ID
    WORD wCommAddr;    // 设备地址
} tagCommDev;

// 设备配置结构
typedef struct {
    WORD wPrimaryPort;   // 主端口号 (offset +6)
    WORD wBackupPort;    // 备端口号 (offset +8)
    BYTE byEnable;       // 启用标志 (offset +20)
} tagIedSet;
```

#### 3. 切换时序

```
正常状态:
主端口 ←→ 设备 (正常通信)
备端口     (待机)

故障检测:
主端口 ×→ 设备 (通信超时/失败)
备端口     (检测到故障)

自动切换:
主端口     (停止通信)
备端口 ←→ 设备 (接管通信)

故障恢复:
主端口 ←→ 设备 (恢复通信)
备端口     (返回待机)
```

### 在线状态判断标准

#### 1. 设备在线条件

设备被认为在线需要满足：

1. **配置有效**: 设备配置存在且启用
2. **地址匹配**: 响应报文设备地址正确
3. **功能码匹配**: 响应功能码与请求一致
4. **CRC校验**: 报文CRC校验正确
5. **超时范围**: 响应时间在600ms内

#### 2. 设备离线条件

设备被认为离线的情况：

1. **通信超时**: 连续600ms无响应
2. **CRC错误**: 报文校验失败
3. **地址错误**: 响应地址不匹配
4. **异常响应**: 收到异常功能码(0x80+)
5. **硬件故障**: 串口硬件错误

#### 3. 状态更新机制

```c
// 成功通信时重置状态
Pub_ResetPortComm(wPortNo, 120);    // 端口通信正常
Pub_ResetIedComm(wIedID, wPortNo, 120);  // 设备通信正常

// 失败时更新错误状态
Daemon_UpdateTaskError(2, wPortNo);  // 更新任务错误
```

## 总结

该库实现了完整的Modbus RTU主站功能，支持：
- 多种数据类型的读写操作
- 灵活的字节序处理
- 完善的错误处理机制
- 高效的状态机实现
- 实时数据更新到RTDB
- 丰富的配置选项
- 完整的故障诊断功能
- **三步式遥控操作**
- **多点遥调功能**
- **实时命令确认机制**
- **完善的超时处理**
- **智能双机切换**
- **设备在线状态监控**
- **自动故障恢复**

适用于电力系统中的RTU设备通信，具有良好的实时性和可靠性。该库特别适合用于变电站自动化系统中的数据采集和控制功能，支持复杂的遥控遥调操作、实时状态反馈和高可用性的双机热备切换。
