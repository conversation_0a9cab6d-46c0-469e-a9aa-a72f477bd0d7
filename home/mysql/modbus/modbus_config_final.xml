<?xml version="1.0" encoding="UTF-8"?>
<!-- 
    Modbus 协议服务配置文件
    用于配置Modbus设备采集、Redis数据上报、性能监控等功能
    配置文件版本: 1.0.0
    最后更新: 2025-08-01
    
    配置说明：
    1. 所有时间单位均为毫秒(ms)，除非特别说明
    2. IP地址支持IPv4格式
    3. 设备ID必须唯一
    4. 数据点格式严格按照Modbus协议规范
    
    延时优化建议：
    - scan_interval_ms: 200ms (数据采集频率，影响实时性)
    - report_interval_ms: 200ms (Redis上报频率，影响数据延时)
    - timeout: 200ms (Modbus通信超时，影响响应时间)
    - connection_timeout: 3000ms (Redis连接超时)
    - command_timeout: 1000ms (Redis命令超时)
-->
<modbus_config>
    <!-- ==================== 服务基础配置 ==================== -->
    <service>
        <!-- 服务名称，用于日志标识和进程管理 -->
        <name>Modbus Service</name>
        
        <!-- 服务版本号 -->
        <version>1.0.0</version>
        
        <!-- 日志级别: TRACE, DEBUG, INFO, WARN, ERROR, FATAL -->
        <!-- DEBUG: 详细调试信息，生产环境建议使用INFO -->
        <log_level>DEBUG</log_level>
        
        <!-- 日志文件路径，支持相对路径和绝对路径 -->
        <log_file>modbus_service.log</log_file>
        
        <!-- 数据上报间隔(毫秒)，影响Redis数据发布频率 -->
        <!-- 建议值: 100-1000ms -->
        <!-- 过小: 增加网络负载和CPU使用率 -->
        <!-- 过大: 增加数据延时 -->
        <report_interval_ms>200</report_interval_ms>
        
        <!-- 最大工作线程数，用于并发处理设备通信和数据处理 -->
        <!-- 建议值: CPU核心数的1-2倍 -->
        <!-- 过多: 可能导致线程切换开销增大 -->
        <!-- 过少: 可能导致处理能力不足 -->
        <max_threads>4</max_threads>
        
        <!-- ==================== Redis 数据库配置 ==================== -->
        <redis>
            <!-- Redis 服务器IP地址 -->
            <ip>**************</ip>
            
            <!-- Redis 服务器端口，默认6379 -->
            <port>6379</port>
            
            <!-- Redis 密码，为空表示无密码认证 -->
            <password></password>
            
            <!-- Redis 数据库编号，默认0 -->
            <!-- 范围: 0-15 (取决于Redis配置) -->
            <database>0</database>
            
            <!-- Redis 连接超时时间(毫秒) -->
            <!-- 建议值: 3000-10000ms -->
            <!-- 网络较差时可适当增大 -->
            <connection_timeout>3000</connection_timeout>
            
            <!-- Redis 命令执行超时时间(毫秒) -->
            <!-- 建议值: 1000-5000ms -->
            <!-- 影响数据写入的响应时间 -->
            <command_timeout>1000</command_timeout>
        </redis>
    </service>
    
    <!-- ==================== 设备配置列表 ==================== -->
    <devices>
        <!-- 
            单个设备配置
            属性说明:
            - id: 设备唯一标识符，用于Redis数据标识，必须唯一
            - name: 设备名称，用于日志和监控显示
            - type: 设备类型，如SVG、PLC、仪表等
        -->
        <device id="1" name="XJ-SVG-1" type="SVG">
            <!-- Modbus 从站ID，范围1-247 -->
            <slave_id>1</slave_id>
            
            <!-- ==================== 通信配置 ==================== -->
            <communication type="RTU">
                <!-- 串口设备路径 -->
                <!-- Linux: /dev/ttyS0, /dev/ttyUSB0, /dev/ttyAMA0 等 -->
                <!-- Windows: COM1, COM2, COM3 等 -->
                <device>/dev/ttyS0</device>
                
                <!-- 波特率，常用值: 1200, 2400, 4800, 9600, 19200, 38400, 115200 -->
                <baud>9600</baud>
                
                <!-- 校验位: N(无), E(偶校验), O(奇校验) -->
                <parity>N</parity>
                
                <!-- 数据位，通常为7或8 -->
                <data_bits>8</data_bits>
                
                <!-- 停止位，通常为1或2 -->
                <stop_bits>1</stop_bits>
                
                <!-- Modbus 通信超时时间(毫秒) -->
                <!-- 建议值: 100-1000ms -->
                <!-- 串口通信建议200-500ms，网络通信建议500-2000ms -->
                <timeout>200</timeout>
            </communication>
            
            <!-- ==================== 数据扫描配置 ==================== -->
            <scan_config>
                <!-- 扫描间隔(毫秒)，决定数据采集频率 -->
                <!-- 建议值: 100-2000ms -->
                <!-- 影响因素: 系统实时性要求、网络负载、设备响应能力 -->
                <scan_interval_ms>200</scan_interval_ms>
                
                <!-- 是否启用自动扫描 -->
                <enable_auto_scan>true</enable_auto_scan>
                
                <!-- 通信失败重试次数 -->
                <!-- 建议值: 1-5次 -->
                <retry_count>3</retry_count>
                
                <!-- 重试间隔(毫秒) -->
                <!-- 建议值: 100-1000ms -->
                <retry_interval_ms>500</retry_interval_ms>
                
                <!-- ==================== 数据变化检测配置 ==================== -->
                <!-- 启用变化检测可减少不必要的Redis写入，提高性能 -->
                <enable_change_detection>true</enable_change_detection>
                
                <!-- 变化阈值，数值变化超过此值才认为数据发生变化 -->
                <!-- 用于过滤微小的数值波动，减少无效数据上报 -->
                <!-- 建议值: 0.01-1.0，根据数据精度要求调整 -->
                <change_threshold>0.01</change_threshold>
            </scan_config>
            
            <!-- ==================== 数据点配置 ==================== -->
            <data_points>
                <!-- 遥测数据点(YC - 模拟量) -->
                <YC>
                    <!-- 
                        数据点格式: 功能码,起始地址,数量,从站ID,数据类型,保留字段
                        功能码说明:
                        - 03: 读保持寄存器 (Read Holding Registers)
                        - 04: 读输入寄存器 (Read Input Registers)
                        
                        数据类型说明:
                        - 0: uint16 (无符号16位整数)
                        - 1: int16  (有符号16位整数)
                        - 2: uint32 (无符号32位整数，占用2个寄存器)
                        - 3: int32  (有符号32位整数，占用2个寄存器)
                        - 4: float  (32位浮点数，占用2个寄存器)
                        - 5: double (64位浮点数，占用4个寄存器)
                    -->
                    <point>03,0000,10,1,0,0</point>
                    <!-- 更多遥测点示例 -->
                    <!-- <point>03,0010,5,1,4,0</point>   读取5个浮点数 -->
                    <!-- <point>04,0020,8,1,1,0</point>   读取8个有符号整数 -->
                </YC>
                
                <!-- 遥信数据点(YX - 开关量) -->
                <YX>
                    <!-- 
                        遥信数据点格式: 功能码,起始地址,数量,从站ID,数据类型,保留字段
                        功能码说明:
                        - 01: 读线圈状态 (Read Coils)
                        - 02: 读离散输入状态 (Read Discrete Inputs)
                        
                        数据类型: 遥信固定为0
                    -->
                    <!-- <point>01,0000,16,1,0,0</point>  读取16个线圈状态 -->
                    <!-- <point>02,0000,8,1,0,0</point>   读取8个离散输入 -->
                </YX>
                
                <!-- 遥控数据点(YK - 控制量) -->
                <YK>
                    <!-- 
                        遥控数据点格式: 功能码,地址,从站ID,数据类型,保留字段1,保留字段2
                        功能码说明:
                        - 05: 写单个线圈 (Write Single Coil)
                        - 06: 写单个寄存器 (Write Single Register)
                        - 15: 写多个线圈 (Write Multiple Coils)
                        - 16: 写多个寄存器 (Write Multiple Registers)
                    -->
                    <!-- <point>05,0000,1,0,0,0</point>   控制单个线圈 -->
                    <!-- <point>06,0001,1,0,0,0</point>   控制单个寄存器 -->
                </YK>
                
                <!-- 遥调数据点(YT - 设定量) -->
                <YT>
                    <!-- 
                        遥调数据点格式: 功能码,地址,从站ID,数据类型,保留字段1,保留字段2
                        功能码说明:
                        - 06: 写单个寄存器 (Write Single Register)
                        - 16: 写多个寄存器 (Write Multiple Registers)
                        
                        数据类型: 同遥测数据点
                    -->
                    <!-- <point>06,0100,1,4,0,0</point>   设置浮点数值 -->
                    <!-- <point>16,0200,1,0,0,0</point>   设置整数值 -->
                </YT>
            </data_points>
        </device>
    </devices>

    <!-- ==================== 高级功能配置 ==================== -->
    <advanced>
        <!-- 是否启用统计信息收集 -->
        <!-- 统计信息包括：通信成功率、数据点读取次数、错误计数等 -->
        <enable_statistics>true</enable_statistics>
        
        <!-- 统计信息更新间隔(毫秒) -->
        <!-- 建议值: 1000-10000ms，过小会增加系统开销 -->
        <statistics_interval_ms>5000</statistics_interval_ms>
        
        <!-- 是否启用性能监控 -->
        <!-- 监控内容：CPU使用率、内存使用量、网络延时、队列长度等 -->
        <enable_performance_monitor>true</enable_performance_monitor>
        
        <!-- 最大内存使用限制(MB) -->
        <!-- 超过此值会触发告警或自动清理 -->
        <!-- 建议值: 256-1024MB，根据系统配置调整 -->
        <max_memory_usage_mb>512</max_memory_usage_mb>

        <!-- 是否启用健康检查（看门狗功能） -->
        <!-- 禁用看门狗服务，避免自动重启和健康检查 -->
        <enable_health_check>false</enable_health_check>
        

    </advanced>
</modbus_config>
