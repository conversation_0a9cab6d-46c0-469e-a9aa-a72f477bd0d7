#include <iostream>
#include <cassert>
#include <vector>
#include <memory>

// 包含要测试的头文件
#include "../src/types/modbus_types.h"
#include "../src/utils/logger.h"
#include "../src/utils/utils.h"
#include "../src/config/config_manager.h"
#include "../src/device/modbus_device.h"

using namespace modbus;

// 简单的测试框架
class TestFramework {
public:
    static void RunTest(const std::string& test_name, std::function<void()> test_func) {
        std::cout << "Running test: " << test_name << " ... ";
        try {
            test_func();
            std::cout << "PASSED" << std::endl;
            passed_count_++;
        } catch (const std::exception& e) {
            std::cout << "FAILED: " << e.what() << std::endl;
            failed_count_++;
        } catch (...) {
            std::cout << "FAILED: Unknown exception" << std::endl;
            failed_count_++;
        }
        total_count_++;
    }
    
    static void PrintSummary() {
        std::cout << "\n=== Test Summary ===" << std::endl;
        std::cout << "Total: " << total_count_ << std::endl;
        std::cout << "Passed: " << passed_count_ << std::endl;
        std::cout << "Failed: " << failed_count_ << std::endl;
        std::cout << "Success Rate: " << (total_count_ > 0 ? (double)passed_count_ / total_count_ * 100.0 : 0.0) << "%" << std::endl;
    }
    
    static int GetFailedCount() { return failed_count_; }
    
private:
    static int total_count_;
    static int passed_count_;
    static int failed_count_;
};

int TestFramework::total_count_ = 0;
int TestFramework::passed_count_ = 0;
int TestFramework::failed_count_ = 0;

// 测试基础数据类型
void TestBasicTypes() {
    // 测试 TypeIndex
    TypeIndex idx1(DataType::YC, 100);
    TypeIndex idx2(DataType::YX, 200);
    TypeIndex idx3(DataType::YC, 100);
    
    assert(idx1 != idx2);
    assert(idx1 == idx3);
    assert(idx1 < idx2);
    
    // 测试 DataPointValue
    DataPointValue value;
    value.type_idx = idx1;
    value.raw_value = 1234;
    value.scaled_value = 12.34;
    value.is_valid = true;
    value.quality = DataQuality::GOOD;
    value.timestamp = utils::TimeUtils::GetCurrentTimestamp();
    
    assert(value.is_valid);
    assert(value.quality == DataQuality::GOOD);
    assert(value.scaled_value == 12.34);
}

// 测试工具类
void TestUtils() {
    // 测试字符串工具
    std::string formatted = utils::StringUtils::Format("Test %d: %.2f", 123, 45.67);
    assert(formatted == "Test 123: 45.67");
    
    assert(utils::StringUtils::ToInt("123", 0) == 123);
    assert(utils::StringUtils::ToInt("invalid", 999) == 999);
    
    assert(utils::StringUtils::ToDouble("123.45", 0.0) == 123.45);
    assert(utils::StringUtils::ToDouble("invalid", 999.0) == 999.0);
    
    // 测试时间工具
    uint64_t timestamp1 = utils::TimeUtils::GetCurrentTimestamp();
    utils::TimeUtils::SleepMs(10);
    uint64_t timestamp2 = utils::TimeUtils::GetCurrentTimestamp();
    assert(timestamp2 > timestamp1);
    
    std::string time_str = utils::TimeUtils::FormatTimestamp(timestamp1);
    assert(!time_str.empty());
}

// 测试日志系统
void TestLogger() {
    Logger& logger = Logger::GetInstance();
    
    // 初始化日志系统
    logger.Initialize(LogLevel::DEBUG, LogTarget::CONSOLE);
    
    // 测试不同级别的日志
    WRITE_DEBUG_LOG("Debug message: %d", 123);
    WRITE_INFO_LOG("Info message: %s", "test");
    WRITE_WARN_LOG("Warning message");
    WRITE_ERROR_LOG("Error message: %.2f", 45.67);
    
    // 测试日志级别过滤
    logger.SetLevel(LogLevel::WARN);
    WRITE_DEBUG_LOG("This debug message should not appear");
    WRITE_INFO_LOG("This info message should not appear");
    WRITE_WARN_LOG("This warning message should appear");
    
    logger.Shutdown();
}

// 测试配置管理
void TestConfigManager() {
    ConfigManager config_manager;
    
    // 测试默认配置
    auto default_config = config_manager.GetDefaultConfig();
    assert(!default_config.service_name.empty());
    
    // 测试设备配置
    DeviceConfig device_config(1, "Test Device");
    device_config.device_type = "Test Type";
    device_config.comm_type = CommType::RTU;
    device_config.slave_id = 1;
    device_config.scan_interval_ms = 1000;
    
    assert(device_config.device_id == 1);
    assert(device_config.device_name == "Test Device");
    assert(device_config.comm_type == CommType::RTU);
}

// 测试设备工厂
void TestDeviceFactory() {
    DeviceConfig config(1, "Test RTU Device");
    config.comm_type = CommType::RTU;
    config.device_type = "Test RTU";
    
    // 验证配置
    auto validate_result = ModbusDeviceFactory::ValidateConfig(config);
    assert(validate_result.IsSuccess());
    
    // 创建设备
    auto device = ModbusDeviceFactory::CreateDevice(config);
    assert(device != nullptr);
    assert(device->GetDeviceId() == 1);
    assert(device->GetDeviceName() == "Test RTU Device");
    assert(device->GetStatus() == DeviceStatus::DISCONNECTED);
    
    // 测试 TCP 设备
    DeviceConfig tcp_config(2, "Test TCP Device");
    tcp_config.comm_type = CommType::TCP;
    tcp_config.device_type = "Test TCP";
    
    auto tcp_device = ModbusDeviceFactory::CreateDevice(tcp_config);
    assert(tcp_device != nullptr);
    assert(tcp_device->GetDeviceId() == 2);
}

// 测试设备生命周期
void TestDeviceLifecycle() {
    DeviceConfig config(1, "Lifecycle Test Device");
    config.comm_type = CommType::RTU;
    config.auto_scan = false; // 禁用自动扫描以简化测试
    
    auto device = ModbusDeviceFactory::CreateDevice(config);
    assert(device != nullptr);
    
    // 初始状态
    assert(device->GetStatus() == DeviceStatus::DISCONNECTED);
    assert(!device->IsRunning());
    assert(!device->IsConnected());
    
    // 初始化设备
    auto init_result = device->Initialize();
    assert(init_result.IsSuccess());
    assert(device->GetStatus() == DeviceStatus::INITIALIZED);
    
    // 启动设备（注意：这里可能会失败，因为没有真实的硬件）
    auto start_result = device->Start();
    // 不检查启动结果，因为没有真实硬件
    
    // 获取统计信息
    auto stats = device->GetStatistics();
    assert(stats.scan_count >= 0);
    assert(stats.success_rate >= 0.0);
    
    // 停止设备
    auto stop_result = device->Stop();
    assert(stop_result.IsSuccess());
    
    // 关闭设备
    device->Shutdown();
}

// 测试错误处理
void TestErrorHandling() {
    // 测试无效的设备配置
    DeviceConfig invalid_config(0, ""); // 无效的设备ID和名称
    auto validate_result = ModbusDeviceFactory::ValidateConfig(invalid_config);
    assert(!validate_result.IsSuccess());
    
    // 测试创建无效设备
    auto device = ModbusDeviceFactory::CreateDevice(invalid_config);
    assert(device == nullptr);
    
    // 测试 Result 类
    Result<int> success_result(42);
    assert(success_result.IsSuccess());
    assert(success_result.data == 42);
    
    Result<int> error_result(ErrorCode::INVALID_PARAM, "Test error");
    assert(!error_result.IsSuccess());
    assert(error_result.error_code == ErrorCode::INVALID_PARAM);
    assert(error_result.error_message == "Test error");
}

// 测试线程安全
void TestThreadSafety() {
    Logger& logger = Logger::GetInstance();
    logger.Initialize(LogLevel::INFO, LogTarget::CONSOLE);
    
    // 创建多个线程同时写日志
    std::vector<std::thread> threads;
    const int thread_count = 5;
    const int messages_per_thread = 10;
    
    for (int i = 0; i < thread_count; ++i) {
        threads.emplace_back([i, messages_per_thread]() {
            for (int j = 0; j < messages_per_thread; ++j) {
                WRITE_INFO_LOG("Thread %d, Message %d", i, j);
                utils::TimeUtils::SleepMs(1);
            }
        });
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    logger.Shutdown();
}

// 主测试函数
int main() {
    std::cout << "Starting Modbus Protocol Library Basic Tests" << std::endl;
    std::cout << "=============================================" << std::endl;
    
    // 运行所有测试
    TestFramework::RunTest("Basic Types", TestBasicTypes);
    TestFramework::RunTest("Utils", TestUtils);
    TestFramework::RunTest("Logger", TestLogger);
    TestFramework::RunTest("Config Manager", TestConfigManager);
    TestFramework::RunTest("Device Factory", TestDeviceFactory);
    TestFramework::RunTest("Device Lifecycle", TestDeviceLifecycle);
    TestFramework::RunTest("Error Handling", TestErrorHandling);
    TestFramework::RunTest("Thread Safety", TestThreadSafety);
    
    // 打印测试总结
    TestFramework::PrintSummary();
    
    return TestFramework::GetFailedCount();
}
