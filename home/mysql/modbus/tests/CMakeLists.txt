# 测试配置
cmake_minimum_required(VERSION 3.10)

# 基础测试
add_executable(test_basic test_basic.cpp)
target_link_libraries(test_basic modbus_protocol_static)

# 设置测试输出目录
set_target_properties(test_basic PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests
)

# 添加测试到 CTest
enable_testing()
add_test(NAME BasicTests COMMAND test_basic)

# 集成测试（如果需要）
if(BUILD_INTEGRATION_TESTS)
    add_executable(test_integration test_integration.cpp)
    target_link_libraries(test_integration modbus_protocol_static)
    
    set_target_properties(test_integration PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests
    )
    
    add_test(NAME IntegrationTests COMMAND test_integration)
endif()

# 性能测试（如果需要）
if(BUILD_PERFORMANCE_TESTS)
    add_executable(test_performance test_performance.cpp)
    target_link_libraries(test_performance modbus_protocol_static)
    
    set_target_properties(test_performance PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests
    )
    
    add_test(NAME PerformanceTests COMMAND test_performance)
endif()
