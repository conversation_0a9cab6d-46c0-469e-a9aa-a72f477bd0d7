# XJ-SVG数据采集程序架构分析报告

## 1. 程序概述

XJ-SVG数据采集程序是一个基于Qt框架的工业数据采集系统，主要功能是通过Modbus协议从设备采集数据，并通过Redis进行数据存储和发布订阅通信。

### 1.1 主要功能
- Modbus RTU/TCP设备数据采集
- Redis数据存储和发布订阅
- MySQL数据库集成
- 多设备管理
- 实时数据监控和控制

## 2. 程序架构

### 2.1 核心组件
```
main.cpp
├── XJSVGMasterService (主服务)
    ├── XJSVGConfigManager (配置管理)
    ├── XJSVGDeviceManager (设备管理)
        └── XJSVGDeviceService (设备服务)
            ├── ModbusCommImpl (Modbus通信)
            ├── AgvcMysqlRedis (Redis操作)
            └── RedisCallBackWorker (Redis回调)
```

### 2.2 启动流程
1. 加载配置文件 `xj_svg_collect.ini`
2. 初始化日志系统
3. 启动主服务 `XJSVGMasterService`
4. 根据配置创建设备服务实例
5. 启动数据采集和Redis订阅

## 3. 报文收发逻辑

### 3.1 Modbus通信实现

#### 3.1.1 通信参数配置
```ini
[5]
DevName = XJ-SVG
Device = /dev/ttyS1
Baud = 9600
Parity = N
DataBit = 8
StopBit = 1
```

#### 3.1.2 支持的Modbus功能码
- **0x01**: 读取线圈状态 (Read Coils)
- **0x03**: 读取保持寄存器 (Read Holding Registers)
- **0x05**: 写单个线圈 (Write Single Coil)
- **0x06**: 写单个寄存器 (Write Single Register)

#### 3.1.3 数据采集流程
1. **初始化连接**: 根据配置参数建立Modbus RTU连接
2. **设置从站ID**: 为每个数据点设置对应的从站地址
3. **循环读取**: 按照配置的点表定时读取数据
4. **数据处理**: 根据数据类型进行相应处理
5. **错误处理**: 连接断开时自动重连

#### 3.1.4 数据点配置格式
```ini
[PointList]
1:00001 = 3,3,0,151        # 遥测数据: 功能码3, 从站3, 起始地址0, 数量151
2:00001 = 3,1,0,47         # 遥信数据: 功能码3, 从站1, 起始地址0, 数量47
3:00001 = 3,6,20480,13     # 遥调数据: 功能码3, 从站6, 起始地址20480, 数量13
4:00001 = 3,5,12288,21     # 遥控数据: 功能码3, 从站5, 起始地址12288, 数量21
```

### 3.2 数据类型处理

#### 3.2.1 遥测数据 (YC - 模拟量)
- **数据类型**: 1
- **功能码**: 0x03 (读保持寄存器)
- **数据格式**: 16位寄存器值
- **处理**: 支持线性变换和工程量转换

#### 3.2.2 遥信数据 (YX - 开关量)
- **数据类型**: 2  
- **功能码**: 0x01/0x03 (读线圈/寄存器)
- **数据格式**: 位状态
- **处理**: 按位解析状态信息

#### 3.2.3 遥调数据 (YT - 模拟量设定)
- **数据类型**: 3
- **功能码**: 0x06 (写单个寄存器)
- **数据格式**: 16位寄存器值

#### 3.2.4 遥控数据 (YK - 开关量控制)
- **数据类型**: 4
- **功能码**: 0x05 (写单个线圈)
- **数据格式**: 布尔值

## 4. Redis发布订阅逻辑

### 4.1 Redis连接配置
```ini
[Service]
RedisIP = 127.0.0.1
RedisPort = 6379
ReportTime = 200
```

### 4.2 发布订阅架构

#### 4.2.1 订阅频道
- **EVENT_RC_SCADA_YK**: 遥控命令频道
- **EVENT_RC_SCADA_YT**: 遥调命令频道

#### 4.2.2 消息处理流程
1. **初始化订阅**: 创建两个Redis订阅者实例
2. **设置回调**: 为每个频道设置消息处理回调函数
3. **消息过滤**: 根据设备ID过滤消息
4. **命令执行**: 将Redis命令转换为Modbus写操作

#### 4.2.3 遥控消息处理
```cpp
void RedisCallBackWorker::ykMsgCallback(char *channel, char *msg, int msglen, void *pridata)
{
    XJSVGDeviceService *dev_obj = (XJSVGDeviceService *)pridata;
    REDIS_YK *yk = (REDIS_YK *)msg;
    if (dev_obj->GetDeviceID() != yk->deviceId) {
        return;
    }
    dev_obj->WriteModbusDB(4, yk->dataId, yk->val);
}
```

#### 4.2.4 遥调消息处理
```cpp
void RedisCallBackWorker::ytMsgCallback(char *channel, char *msg, int msglen, void *pridata)
{
    XJSVGDeviceService *dev_obj = (XJSVGDeviceService *)pridata;
    REDIS_YT *yt = (REDIS_YT *)msg;
    if (dev_obj->GetDeviceID() != yt->deviceId) {
        return;
    }
    dev_obj->WriteModbusDB(3, yt->dataId, yt->val);
}
```

### 4.3 数据发布机制

#### 4.3.1 数据上报
- **遥测数据**: 通过 `setAgvcYCValue()` 发布到Redis
- **遥信数据**: 通过 `setAgvcYXValue()` 发布到Redis
- **实时性**: 根据 `ReportTime` 配置定时上报

#### 4.3.2 数据格式
```cpp
// 遥测数据结构
typedef struct {
    int deviceId;    // 设备ID
    int dataId;      // 数据点ID
    double val;      // 数值
    unsigned char status; // 状态
} REDIS_YT;

// 遥控数据结构  
typedef struct {
    int deviceId;    // 设备ID
    int dataId;      // 数据点ID
    unsigned short val; // 数值
    unsigned char status; // 状态
} REDIS_YK;
```

## 5. 外部依赖库分析

### 5.1 Qt框架
- **版本**: Qt 5.9.2
- **模块**: 
  - QtCore: 核心功能
  - QtSql: 数据库操作
  - QtNetwork: 网络通信
- **用途**: 
  - 应用程序框架
  - 事件循环管理
  - 数据库连接
  - 配置文件处理

### 5.2 Boost库
- **组件**: 
  - boost_thread: 多线程支持
  - boost::shared_ptr: 智能指针
  - boost::bind: 函数绑定
- **用途**:
  - 线程管理和同步
  - 内存管理
  - 回调函数绑定

### 5.3 libmodbus
- **版本**: 标准libmodbus库
- **功能**:
  - Modbus RTU/TCP协议实现
  - 串口和网络通信
  - 标准Modbus功能码支持
- **API使用**:
  - `modbus_new_rtu()`: 创建RTU上下文
  - `modbus_read_registers()`: 读寄存器
  - `modbus_write_register()`: 写寄存器
  - `modbus_read_bits()`: 读线圈

### 5.4 Redis相关库
- **hiredis**: Redis C客户端库 (版本1.1.0)
- **自定义RDBI**: Redis数据库接口封装
- **功能**:
  - Redis连接管理
  - 发布订阅机制
  - 数据存储和检索

### 5.5 MySQL库
- **CLibMySql**: MySQL C客户端库
- **功能**:
  - 数据库连接
  - 点表配置加载
  - 历史数据存储

### 5.6 自定义公共库
- **pub_comm**: 公共通信库
- **log**: 日志系统
- **public_function_library**: 公共函数库
- **功能**:
  - 配置文件解析
  - 字符串处理
  - 系统操作
  - 日志记录

## 6. 线程模型

### 6.1 主线程
- Qt事件循环
- 配置管理
- 设备管理

### 6.2 设备服务线程
- 数据采集循环
- Modbus通信
- 数据处理和上报

### 6.3 Redis回调线程
- YK消息处理线程
- YT消息处理线程
- 异步消息处理

## 7. 配置管理

### 7.1 主配置文件
- **文件**: `xj_svg_collect.ini`
- **内容**: 服务参数和设备参数

### 7.2 点表配置
- **文件**: `point/xj_svg_point.ini`
- **格式**: 数据类型:点号 = 功能码,从站,地址,数量

### 7.3 动态配置
- 支持配置重载
- 设备热插拔
- 参数在线修改

## 8. 错误处理和容错机制

### 8.1 通信容错
- 自动重连机制
- 超时处理
- 错误日志记录

### 8.2 数据容错
- 数据有效性检查
- 异常值过滤
- 状态标识

### 8.3 系统容错
- 异常捕获
- 资源清理
- 优雅退出

## 9. 性能特点

### 9.1 实时性
- 可配置采集周期 (默认200ms)
- 异步数据处理
- 优先级调度

### 9.2 可扩展性
- 多设备支持
- 模块化设计
- 插件式架构

### 9.3 可靠性
- 数据完整性保证
- 通信状态监控
- 故障自恢复

## 10. 总结

XJ-SVG数据采集程序是一个功能完整、架构清晰的工业数据采集系统。它通过Modbus协议实现设备通信，通过Redis实现数据存储和分发，具有良好的实时性、可靠性和可扩展性。程序采用多线程架构，支持多设备并发采集，具备完善的错误处理和容错机制。
