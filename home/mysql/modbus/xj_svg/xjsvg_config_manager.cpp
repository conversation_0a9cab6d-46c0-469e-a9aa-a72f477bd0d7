#include "log.h"
#include "config.h"
#include "system.h"
#include "public_function_library.h"
#include "xjsvg_config_manager.h"

IMPLEMENT_SINGLETON(XJSVGConfigManager);

XJSVGConfigManager::XJSVGConfigManager(void) {
    mutex_lock_.reset(new MutexLock());
    cfg_obj_.reset(new LoadIniConfig());
}

XJSVGConfigManager::~XJSVGConfigManager(void) {
    cfg_obj_.reset(); {
        AutoLock auto_lock(*mutex_lock_.get());
        service_param_.dev_params.clear();
    }
    mutex_lock_.reset();
}

bool XJSVGConfigManager::LoadConfig(const std::string& cfg_file) {
    if (RET_SUCCESS != cfg_obj_->ParserFile(cfg_file)) {
        return false;
    }
    if (!LoadALLConfigParam()) {
        return false;
    }
    config_file_ = cfg_file;
    return true;
}

bool XJSVGConfigManager::ReLoadConfig(void) {
    return LoadConfig(config_file_);
}

bool XJSVGConfigManager::LoadALLConfigParam(void) {
    StrList device_projects = cfg_obj_->GetConfigProjects();
    AutoLock auto_lock(*mutex_lock_.get());
    service_param_.dev_params.clear();
    for (StrList_Iter iter = device_projects.begin(); iter != device_projects.end(); ++iter) {
        if ((*iter).empty()) {
            continue;
        }
        if (SERVICE_KEY == *iter) {
            if (!LoadServConfigParams(*iter)) {
                return false;
            }
            continue;
        } else if (LOG_CONFIG == *iter) {
            continue;
        } else {
            DeviceParam dev_params;
            if (LoadDevConfigParams(*iter, dev_params)) {
                service_param_.dev_params.insert(std::make_pair(dev_params.dev_id, dev_params));
            }
        }
    }
    return true;
}

bool XJSVGConfigManager::LoadServConfigParams(const std::string &key_name) {
    // Redis服务IP地址
    std::string redis_ip = cfg_obj_->GetConfigValueByKey(key_name, SERVICE_REDIS_IP);
    if (redis_ip.empty()) {
        return false;
    }
    service_param_.redis_ip = redis_ip;

    // Redis服务端口
    std::string redis_port = cfg_obj_->GetConfigValueByKey(key_name, SERVICE_REDIS_PORT);
    if (redis_port.empty()) {
        return false;
    }
    service_param_.redis_port = PubOpt::TypeOpt::StringToInt32(redis_port);

    // 超时时间
    std::string report_time = cfg_obj_->GetConfigValueByKey(key_name, SERVICE_REPORT_TIME);
    if (report_time.empty()) {
        return false;
    }
    service_param_.report_time = PubOpt::TypeOpt::StringToInt32(report_time);
    return true;
}

bool XJSVGConfigManager::LoadDevConfigParams(const std::string& key_name, DeviceParam &dev_params) {
    dev_params.dev_id = PubOpt::TypeOpt::StringToInt32(key_name);
    std::string dev_name = cfg_obj_->GetConfigValueByKey(key_name, DEVICE_NAME);
    if (!dev_name.empty()) {
        dev_params.dev_name = dev_name;
    }
    std::string point_file = cfg_obj_->GetConfigValueByKey(key_name, DEVICE_POINT_FILE);
    if (point_file.empty()) {
        point_file = dev_params.point_file;
    }
    dev_params.point_file = PubOpt::StringOpt::StringFormat("%spoint/%s", PubOpt::SystemOpt::GetCurrentProcPath().c_str(), point_file.c_str());
    std::string dev_comm_type = cfg_obj_->GetConfigValueByKey(key_name, DEVICE_COMM_TYPE);
    if (!dev_comm_type.empty()) {
        dev_params.dev_comm_type = PubOpt::TypeOpt::StringToInt32(dev_comm_type);
    }
    bool result = false;
    switch (dev_params.dev_comm_type)
    {
    case 0:     // ModbusRTU
        result = LoadDevModbusRtuParam(key_name, dev_params.modbus_rtu);
        break;
    default:
        result = false;
        break;
    }
    return result;
}

bool XJSVGConfigManager::LoadDevModbusRtuParam(const std::string &key_name, RtuCommParam &rtu_param) {
    if (key_name.empty()) {
        return false;
    }
    std::string device = cfg_obj_->GetConfigValueByKey(key_name, DEVICE_RTU_DEVICE);
    if (device.empty()) {
        return false;
    }
    rtu_param.device = device;
    std::string baud = cfg_obj_->GetConfigValueByKey(key_name, DEVICE_RTU_BAUD);
    if (!baud.empty()) {
       rtu_param.baud = PubOpt::TypeOpt::StringToInt32(baud);
    }
    std::string parity = cfg_obj_->GetConfigValueByKey(key_name, DEVICE_RTU_PARITY);
    if (!parity.empty()) {
        rtu_param.parity = parity.at(0);
    }
    std::string data_bit = cfg_obj_->GetConfigValueByKey(key_name, DEVICE_RTU_DATABIT);
    if (!data_bit.empty()) {
        rtu_param.data_bit = PubOpt::TypeOpt::StringToInt32(data_bit);
    }
    std::string stop_bit = cfg_obj_->GetConfigValueByKey(key_name, DEVICE_RTU_STOPBIT);
    if (!stop_bit.empty()) {
        rtu_param.stop_bit = PubOpt::TypeOpt::StringToInt32(stop_bit);
    }
    return true;
}

bool XJSVGConfigManager::GetServParamByDevID(const int &dev_id, DeviceParam& dev_params) {
    AutoLock auto_lock(*mutex_lock_.get());
    if (service_param_.dev_params.find(dev_id) == service_param_.dev_params.end()) {
        return false;
    }
    dev_params.dev_id = service_param_.dev_params[dev_id].dev_id;
    dev_params.dev_name = service_param_.dev_params[dev_id].dev_name;
    dev_params.point_file = service_param_.dev_params[dev_id].point_file;
    dev_params.dev_comm_type = service_param_.dev_params[dev_id].dev_comm_type;
    dev_params.modbus_rtu.device = service_param_.dev_params[dev_id].modbus_rtu.device;
    dev_params.modbus_rtu.baud = service_param_.dev_params[dev_id].modbus_rtu.baud;
    dev_params.modbus_rtu.parity = service_param_.dev_params[dev_id].modbus_rtu.parity;
    dev_params.modbus_rtu.data_bit = service_param_.dev_params[dev_id].modbus_rtu.data_bit;
    dev_params.modbus_rtu.stop_bit = service_param_.dev_params[dev_id].modbus_rtu.stop_bit;
    dev_params.modbus_rtu.mode = service_param_.dev_params[dev_id].modbus_rtu.mode;
    return true;
}

bool XJSVGConfigManager::GetServiceParam(ServiceParam& service_param) {
    AutoLock auto_lock(*mutex_lock_.get());
    service_param.redis_ip = service_param_.redis_ip;
    service_param.redis_port = service_param_.redis_port;
    service_param.report_time = service_param_.report_time;
    service_param.dev_params.clear();
    std::map<int, DeviceParam>::iterator iter = service_param_.dev_params.begin();
    for (; iter != service_param_.dev_params.end(); ++iter) {
        service_param.dev_params.insert(std::make_pair(iter->first, iter->second));
    }
    return true;
}
