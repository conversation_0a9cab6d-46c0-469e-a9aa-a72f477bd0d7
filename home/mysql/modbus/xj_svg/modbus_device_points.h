#ifndef __MODBUS_DEVICE_POINTS_H__
#define __MODBUS_DEVICE_POINTS_H__

#include "public_function_library.h"

#include <boost/shared_ptr.hpp>

#define POINT_LIST      "PointList"
#define CALC_POINT      "CalcPoint"

typedef struct TypeIndex {
    int data_type;
    int data_idx;
    std::string GetKey(void) const {
        return PubOpt::StringOpt::StringFormat("%d:%05d", data_type, data_idx);
    }
}*P_TYPE_INDEX;
typedef struct PointParam {
    TypeIndex type_idx;
    int mdata_slave;
    int mdata_func_code;
    int mdata_addr;
    int mdata_number;
    int mdata_types;
    PointParam(void) {
        mdata_slave = 1;
        mdata_func_code = 0;
        mdata_addr = 0;
        mdata_number = 0;
        mdata_types = 0;
    }
}*P_POINT_PARAM;
typedef std::list<PointParam> PointList;
typedef std::map<std::string, PointParam> CtrlList;
typedef struct DevicePoints {
    PointList read_list;
    CtrlList ctrl_list;
}*P_DEVICE_POINTS;

class LoadIniConfig;
typedef boost::shared_ptr< LoadIniConfig >   LoadIniConfigPtr;

class ModbusDevicePoints
{
public:
    ModbusDevicePoints(void);
    virtual ~ModbusDevicePoints(void);
public:
    bool LoadPoints(const std::string &point_file);
    bool GetDevicePoints(DevicePoints &dev_points);
private:
    bool LoadALLPointParam(void);
    bool LoadPointList(void);
    bool LoadCtrlPointList(const PointParam &point_param);
    bool ParsePointParam(const std::string &value, PointParam &point_param);
    bool ParseTypeIndex(const std::string &key, TypeIndex &type_index);
private:
    std::string point_file_;
    LoadIniConfigPtr ini_cfg_obj_;
    DevicePoints dev_points_;
};

#endif//__MODBUS_DEVICE_POINTS_H__
