#ifndef __XJSVG_DEVICE_MANAGER_H__
#define __XJSVG_DEVICE_MANAGER_H__

#include "singleton.h"
#include "xjsvg_config_manager.h"

#include <boost/shared_ptr.hpp>

class MutexLock;
class XJSVGDeviceService;

typedef boost::shared_ptr< XJSVGDeviceService >   XJSVGDeviceSharedPtr;
typedef std::map <int, XJSVGDeviceSharedPtr> XJSVGDevices;

class XJSVGDeviceManager
{
    DECLARE_SINGLETON_INIT(XJSVGDeviceManager)
public:
    bool AddDevice(DeviceParam &dev_params);
    bool DelDevice(const int& dev_id);
    void ClearDevices(void);
private:
    bool IsExistDevice(const int& dev_id);
private:
    boost::shared_ptr<MutexLock> mutex_lock_;
    XJSVGDevices modbus_devices_;
};

#endif//__XJSVG_DEVICE_MANAGER_H__
