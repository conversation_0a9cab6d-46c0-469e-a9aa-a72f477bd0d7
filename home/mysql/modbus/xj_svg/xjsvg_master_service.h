#ifndef __XJSVG_MASTER_SERVICE_H__
#define __XJSVG_MASTER_SERVICE_H__

#include "pub_head.h"
#include "xjsvg_config_manager.h"
#include <boost/shared_ptr.hpp>

class XJSVGMasterService
{
public:
    XJSVGMasterService(void);
    virtual ~XJSVGMasterService(void);
public:
    bool InitService(const std::string &cfg_file);
    void RunService(void);
    void StopService(void);
private:
    ServiceParam serv_param_;
};
#endif//__XJSVG_MASTER_SERVICE_H__
