#include "log.h"
#include "system.h"
#include "public_function_library.h"
#include "xjsvg_device_service.h"
#include "xjsvg_device_manager.h"

IMPLEMENT_SINGLETON(XJSVGDeviceManager)

XJSVGDeviceManager::XJSVGDeviceManager(void) {
    mutex_lock_.reset(new MutexLock());
    modbus_devices_.clear();
}

XJSVGDeviceManager::~XJSVGDeviceManager(void) {
    ClearDevices();
    mutex_lock_.reset();
}

bool XJSVGDeviceManager::IsExistDevice(const int &dev_id) {
    AutoLock auto_lock(*mutex_lock_.get());
    if (modbus_devices_.find(dev_id) == modbus_devices_.end()) {
        return false;
    }
    return true;
}

bool XJSVGDeviceManager::AddDevice(DeviceParam &dev_params) {
    if (IsExistDevice(dev_params.dev_id)) {
        return false;
    }
    XJSVGDeviceSharedPtr modbus_device(new XJSVGDeviceService(dev_params.dev_id));
    if (!modbus_device->InitService()) {
        WRITE_ERROR_LOG("设备[%s], 初始化失败", dev_params.GetKey().c_str());
        return false;
    }
    modbus_device->RunService();
    WRITE_INFO_LOG("设备[%s]采集服务, 启动成功", dev_params.GetKey().c_str());
    AutoLock auto_lock(*mutex_lock_.get());
    modbus_devices_.insert(std::make_pair(dev_params.dev_id, modbus_device));
    return true;
}

bool XJSVGDeviceManager::DelDevice(const int& dev_id) {
    if (!IsExistDevice(dev_id)) {
        return false;
    }
    AutoLock auto_lock(*mutex_lock_.get());
    modbus_devices_[dev_id]->StopService();
    modbus_devices_.erase(dev_id);
    return true;
}

void XJSVGDeviceManager::ClearDevices(void) {
    if (modbus_devices_.empty()) {
        return;
    }
    XJSVGDevices::iterator iter = modbus_devices_.begin();
    for (; iter != modbus_devices_.end(); ++iter) {
        DelDevice(iter->first);
    }
    AutoLock auto_lock(*mutex_lock_.get());
    modbus_devices_.clear();
}
