#include "log.h"

#include "xjsvg_device_service.h"
#include "redis_callback_worker.h"

#include <boost/bind/bind.hpp>

RedisCallBackWorker::RedisCallBackWorker(XJSVGDeviceService* dev_obj)
{
    m_sub_yk = new RedisSubscriber();
    m_sub_yt = new RedisSubscriber();
    dev_obj_ = dev_obj;
}

RedisCallBackWorker::~RedisCallBackWorker(void)
{
    /*
    if(NULL != m_sub_yk)
    {
        delete m_sub_yk;
        m_sub_yk = NULL;
    }
    */
}

bool RedisCallBackWorker::InitService()
{
    //m_subinfo_yk.type = RC_VALUE_INT;
    m_subinfo_yk.type = TYPE_SUBSCRIBE;
    m_subinfo_yk.channelName = EVENT_RC_SCADA_YK;
    m_sub_yk->Subscribe(m_subinfo_yk);

    //m_subinfo_yt.type = RC_VALUE_DOUBLE;
    m_subinfo_yt.type = TYPE_SUBSCRIBE;
    m_subinfo_yt.channelName = EVENT_RC_SCADA_YT;
    m_sub_yt->Subscribe(m_subinfo_yt);

    return true;
}

bool RedisCallBackWorker::StartYKCallback(void)
{
    m_sub_yk->SetCallback(&RedisCallBackWorker::ykMsgCallback, dev_obj_);
    return m_sub_yk->Start();
}

bool RedisCallBackWorker::StartYTCallback(void)
{
    m_sub_yt->SetCallback(&RedisCallBackWorker::ytMsgCallback, dev_obj_);
    return m_sub_yt->Start();
}

void RedisCallBackWorker::ytMsgCallback(char *channel, char *msg, int msglen, void *pridata)
{
        XJSVGDeviceService *dev_obj = (XJSVGDeviceService *)pridata;
        REDIS_YT *yt = (REDIS_YT *)msg;
        if (dev_obj->GetDeviceID() != yt->deviceId)
        {
            return ;
        }
        WRITE_INFO_LOG("YT Update -> Channel:%s MsgLen:%d DeviceID:%d DataID:%d Value:%f", channel, msglen, yt->deviceId, yt->dataId, yt->val);
        dev_obj->WriteModbusDB(3, yt->dataId, yt->val);
}

void RedisCallBackWorker::ykMsgCallback(char *channel, char *msg, int msglen, void *pridata)
{
        XJSVGDeviceService *dev_obj = (XJSVGDeviceService *)pridata;
        REDIS_YK *yk = (REDIS_YK *)msg;
        if (dev_obj->GetDeviceID() != yk->deviceId)
        {
            return ;
        }

        WRITE_INFO_LOG("YK Update -> Channel:%s MsgLen:%d DeviceID:%d DataID:%d Value:%d", channel, msglen, yk->deviceId, yk->dataId, yk->val);
        dev_obj->WriteModbusDB(4, yk->dataId, yk->val);
}

void RedisCallBackWorker::RunService(void)
{
    StartYKCallback();
    StartYTCallback();
}

void RedisCallBackWorker::StopService(void)
{
    //m_sub_yk->UnSubscribe();
}
