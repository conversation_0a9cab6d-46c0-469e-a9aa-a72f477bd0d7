#ifndef __XJSVG_DEVICE_SERVICE_H__
#define __XJSVG_DEVICE_SERVICE_H__

#include "system.h"
#include "boost_socket.h"
#include "db/clibmysql.h"
#include "modbus_device_points.h"
#include "xjsvg_config_manager.h"
#include <boost/shared_ptr.hpp>

class MutexLock;
class ModbusTcpImpl;
class AgvcMysqlRedis;
class RedisCallBackWorker;
class XJSVGDeviceService : public Thread
{
public:
    XJSVGDeviceService(const int& dev_id);
    virtual ~XJSVGDeviceService(void);
public:
    bool InitService(void);
    void RunService(void);
    void StopService(void);
    int GetDeviceID(void);
    bool WriteModbusDB(const int &type, const int &idx, const double &val);
private:
    virtual void Run(void);
    bool LoadPoints(const std::string &point_file);
    bool ReportYCData(const PointParam &report_params);
    bool ReportYXData(const PointParam &report_params);
    bool DoReportModbusData(const PointParam &report_params);
    bool ReportBitsData(const PointParam &report_params);
    bool ReportStatusDatas(uint8_t *dest, const PointParam &report_params);
    bool ReportStatusYCDatas(uint8_t *dest, const PointParam &report_params);
    bool ReportStatusYXDatas(uint8_t *dest, const PointParam &report_params);
    bool ReportRegistersData(const PointParam &report_params);
    bool ReportRegisterDatas(uint16_t *dest, const PointParam &report_params);
    bool ReportRegisterYCDatas(uint16_t *dest, const PointParam &report_params);
    bool ReportRegisterYXDatas(uint16_t *dest, const PointParam &report_params);
    int WriteRedisDB(const int &type, const int &idx, const double &data);
    double GetYCPointXS(const int dev_id, const int idex);
    double GetYTPointXS(const int dev_id, const int idex);
    double GetYTCaclValue(const int &idx, const double &realVal);
    double GetYCRealValue(const int &idx, const double &calcVal);
private:
    DeviceParam dev_param_;
    boost::shared_ptr<AgvcMysqlRedis> redis_obj_;
    boost::shared_ptr<ModbusCommImpl> modbus_obj_;
    DevicePoints device_points_;
    VecParaYC m_vectyc;
    VecParaYT m_vectyt;
    CLibMySql   m_pSql;
    RedisCallBackWorker* redis_callback_;
    boost::shared_ptr<MutexLock> mutex_lock_;
};

#endif//__XJSVG_DEVICE_SERVICE_H__
