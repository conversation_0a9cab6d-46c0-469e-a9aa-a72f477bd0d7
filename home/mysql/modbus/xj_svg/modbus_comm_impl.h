#ifndef __MODBUS_COMM_IMPL_H__
#define __MODBUS_COMM_IMPL_H__

#include <string>
#include <modbus/modbus.h>

typedef struct TcpCommParam
{
    std::string tcp_ip;
    int tcp_port;
    int time_out;
    TcpCommParam(void)
    {
        tcp_ip = "127.0.0.1";
        tcp_port = 502;
        time_out = 1000;
    }
}*P_TCP_COMM_PARAM;

typedef struct RtuCommParam
{
    std::string device;
    int baud;
    char parity;
    int data_bit;
    int stop_bit;
    int mode;
    int time_out;
    RtuCommParam(void)
    {
        device = "/dev/ttyS1";
        baud = 9600;
        parity = 'N';
        data_bit = 8;
        stop_bit = 1;
        mode = 1;
        time_out = 1000;
    }
}*P_RTU_COMM_PARAM;

class ModbusCommImpl
{
public:
    ModbusCommImpl(void);
    virtual ~ModbusCommImpl(void);
public:
    bool InitService(const TcpCommParam &tcp_param);
    bool InitService(const RtuCommParam &rtu_param);
    void StopService(void);
    bool SetSlaveId(int slaveId);
    bool ReadBits(int addr, int nb, uint8_t *dest);
    bool WriteBit(int addr, const int status);
    bool WriteBits(int addr, int nb, const uint8_t *src);
    bool ReadInputBits(int addr, int nb, uint8_t *dest);
    bool ReadRegisters(int addr, int nb, uint16_t *dest);
    bool WriteRegister(int addr, const uint16_t value);
    bool WriteRegisters(int addr, int nb, const uint16_t *src);
    bool ReadInputRegisters(int addr, int nb, uint16_t *dest);
private:
    void SetTimeout(int time_out);
    bool ConnectModbus(void);
    void CloseModbus(void);
    bool ReConnectModbus(void);
    void FreeModbus(void);
private:
    modbus_t *ctx_;
    bool is_start_;
    std::string modbus_type_;
};

#endif //__MODBUS_TCP_IMPL_H__
