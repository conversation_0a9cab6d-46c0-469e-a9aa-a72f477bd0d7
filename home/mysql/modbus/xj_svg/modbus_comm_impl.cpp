#include "modbus_comm_impl.h"

ModbusCommImpl::ModbusCommImpl(void) : ctx_(NULL) {
    is_start_ = false;
}

ModbusCommImpl::~ModbusCommImpl(void) {
    FreeModbus();
}

bool ModbusCommImpl::InitService(const TcpCommParam &tcp_param) {
    ctx_ = modbus_new_tcp(tcp_param.tcp_ip.c_str(), tcp_param.tcp_port);
    if (NULL == ctx_) {
        return false;
    }
    SetTimeout(tcp_param.time_out);
    modbus_type_ = "TCP";
    return ConnectModbus();
}

bool ModbusCommImpl::InitService(const RtuCommParam &rtu_param) {
    ctx_ = modbus_new_rtu(rtu_param.device.c_str(), rtu_param.baud, rtu_param.parity, rtu_param.data_bit, rtu_param.stop_bit);
    if (NULL == ctx_) {
        return false;
    }
    modbus_rtu_set_serial_mode(ctx_, rtu_param.mode);
    SetTimeout(rtu_param.time_out);
    modbus_type_ = "RTU";
    return ConnectModbus();
}

void ModbusCommImpl::StopService(void) {
    FreeModbus();
}

bool ModbusCommImpl::ReConnectModbus(void) {
    CloseModbus();
    return ConnectModbus();
}

bool ModbusCommImpl::ReadBits(int addr, int nb, uint8_t *dest) {
    if ((NULL == ctx_) || (NULL == dest)) {
        return false;
    }
    if (!is_start_ && !ReConnectModbus()) {
        return false;
    }
    int rc = -1;
    if (nb > 100) {
        rc = modbus_read_bits(ctx_, addr, 100, dest);
        if (-1 == rc) {
            CloseModbus();
            return false;
        }
        return ReadBits(addr + 100, nb - 100, dest + 100);
    } else {
        rc = modbus_read_bits(ctx_, addr, nb, dest);
        if (-1 == rc) {
            CloseModbus();
            return false;
        }
    }
    return true;
}

bool ModbusCommImpl::WriteBit(int addr, const int status) {
    if (NULL == ctx_) {
        return false;
    }
    if (!is_start_ && !ReConnectModbus()) {
        return false;
    }
    int rc = modbus_write_bit(ctx_, addr, status);
    if (rc == -1) {
        CloseModbus();
        return false;
    }
    return true;
}

bool ModbusCommImpl::WriteBits(int addr, int nb, const uint8_t *src) {
    if ((NULL == ctx_) || (NULL == src)) {
        return false;
    }
    if (!is_start_ && !ReConnectModbus()) {
        return false;
    }
    int rc = -1;
    if (nb > 100)    {
        rc = modbus_write_bits(ctx_, addr, 100, src);
        if (-1 == rc) {
            CloseModbus();
            return false;
        }
        return WriteBits(addr + 100, nb - 100, src + 100);
    } else {
        rc = modbus_write_bits(ctx_, addr, nb, src);
        if (-1 == rc)        {
            CloseModbus();
            return false;
        }
    }
    return true;
}

bool ModbusCommImpl::ReadInputBits(int addr, int nb, uint8_t *dest) {
    if ((NULL == ctx_) || (NULL == dest)) {
        return false;
    }
    if (!is_start_ && !ReConnectModbus()) {
        return false;
    }
    int rc = -1;
    if (nb > 100) {
        rc = modbus_read_input_bits(ctx_, addr, 100, dest);
        if (-1 == rc)
        {
            CloseModbus();
            return false;
        }
        return ReadInputBits(addr + 100, nb - 100, dest + 100);
    } else {
        rc = modbus_read_input_bits(ctx_, addr, nb, dest);
        if (-1 == rc) {
            CloseModbus();
            return false;
        }
    }
    return true;
}

bool ModbusCommImpl::ReadRegisters(int addr, int nb, uint16_t *dest) {
    if ((NULL == ctx_) || (NULL == dest)) {
        return false;
    }
    if (!is_start_ && !ReConnectModbus()) {
        return false;
    }
    int rc = -1;
    if (nb > 100) {
        rc = modbus_read_registers(ctx_, addr, 100, dest);
        if (-1 == rc) {
            CloseModbus();
            return false;
        }
        return ReadRegisters(addr + 100, nb - 100, dest + 100);
    } else {
        rc = modbus_read_registers(ctx_, addr, nb, dest);
        if (-1 == rc) {
            CloseModbus();
            return false;
        }
    }
    return true;
}

bool ModbusCommImpl::WriteRegister(int addr, const uint16_t value) {
    if (NULL == ctx_) {
        return false;
    }
    if (!is_start_ && !ReConnectModbus()) {
        return false;
    }
    int rc = modbus_write_register(ctx_, addr, value);
    if (rc == -1) {
        CloseModbus();
        return false;
    }
    return true;
}

bool ModbusCommImpl::WriteRegisters(int addr, int nb, const uint16_t *src) {
    if ((NULL == ctx_) || (NULL == src)) {
        return false;
    }
    if (!is_start_ && !ReConnectModbus()) {
        return false;
    }
    int rc = -1;
    if (nb > 100) {
        rc = modbus_write_registers(ctx_, addr, 100, src);
        if (-1 == rc) {
            CloseModbus();
            return false;
        }
        return WriteRegisters(addr + 100, nb - 100, src + 100);
    } else {
        rc = modbus_write_registers(ctx_, addr, nb, src);
        if (-1 == rc) {
            CloseModbus();
            return false;
        }
    }
    return true;
}

bool ModbusCommImpl::ReadInputRegisters(int addr, int nb, uint16_t *dest) {
    if ((NULL == ctx_) || (NULL == dest)) {
        return false;
    }
    if (!is_start_ && !ReConnectModbus()) {
        return false;
    }
    int rc = -1;
    if (nb > 100) {
        rc = modbus_read_input_registers(ctx_, addr, 100, dest);
        if (-1 == rc) {
            CloseModbus();
            return false;
        }
        return ReadInputRegisters(addr + 100, nb - 100, dest + 100);
    } else {
        rc = modbus_read_input_registers(ctx_, addr, nb, dest);
        if (-1 == rc) {
            CloseModbus();
            return false;
        }
    }
    return true;
}

bool ModbusCommImpl::SetSlaveId(int slaveId) {
    int rc = modbus_set_slave(ctx_, slaveId);
    if (rc != 0) {
        is_start_ = false;
        return false;
    }
    return true;
}

void ModbusCommImpl::SetTimeout(int time_out) {
    uint32_t tv_sec = time_out / 1000;
    uint32_t tv_usec = time_out % 1000;
    modbus_set_response_timeout(ctx_, tv_sec, tv_usec);
    modbus_get_response_timeout(ctx_, &tv_sec, &tv_usec);
}

bool ModbusCommImpl::ConnectModbus(void) {
    if (NULL == ctx_) {
        return false;
    }
    int rc = modbus_connect(ctx_);
    if (rc == -1) {
        is_start_ = false;
        return false;
    }
    is_start_ = true;
    return true;
}

void ModbusCommImpl::CloseModbus(void) {
    if (NULL != ctx_) {
        modbus_close(ctx_);
    }
    is_start_ = false;
}

void ModbusCommImpl::FreeModbus(void) {
    if (NULL != ctx_) {
        CloseModbus();
        modbus_free(ctx_);
        ctx_ = NULL;
    }
}
