#include "config.h"
#include "public_function_library.h"

#include "modbus_device_points.h"

ModbusDevicePoints::ModbusDevicePoints(void) {
    point_file_.clear();
    ini_cfg_obj_.reset(new LoadIniConfig());
}

ModbusDevicePoints::~ModbusDevicePoints(void) {
    point_file_.clear();
    ini_cfg_obj_.reset();
}

bool ModbusDevicePoints::LoadPoints(const std::string &point_file) {
    if (RET_SUCCESS != ini_cfg_obj_->ParserFile(point_file)) {
        return false;
    }
    if (!LoadALLPointParam()) {
        return false;
    }
    point_file_ = point_file;
    return true;
}

bool ModbusDevicePoints::LoadALLPointParam(void) {
    StrList device_projects = ini_cfg_obj_->GetConfigProjects();
    for (StrList_Iter iter = device_projects.begin(); iter != device_projects.end(); ++iter) {
        if ((*iter).empty()) {
            continue;
        }
        if (POINT_LIST == *iter) {
            if (!LoadPointList()) {
                return false;
            }
            continue;
        }
    }
    return true;
}

bool ModbusDevicePoints::LoadPointList(void) {
    dev_points_.read_list.clear();
    StrStrMap point_list;
    if (!ini_cfg_obj_->GetConfigByKey(POINT_LIST, point_list)) {
        return false;
    }
    StrStrMap_Iter iter = point_list.begin();
    PointParam point_param;
    for(; iter != point_list.end(); ++iter) {
        if (!ParseTypeIndex(iter->first, point_param.type_idx)) {
            return false;
        }
        if (!ParsePointParam(iter->second, point_param)) {
            return false;
        }
        switch (point_param.type_idx.data_type) {
        case 1:
        case 2:
            dev_points_.read_list.push_back(point_param);
            break;
        case 3:
        case 4:
            LoadCtrlPointList(point_param);
            break;
        default:
            return false;
        }
    }
    return true;
}

bool ModbusDevicePoints::LoadCtrlPointList(const PointParam &point_param) {
    int idx = point_param.type_idx.data_idx;
    int addr = point_param.mdata_addr;
    PointParam ctrl_point;
    for(int i=0; i<point_param.mdata_number; i++) {
        ctrl_point.type_idx.data_idx = idx + i;
        ctrl_point.type_idx.data_type = point_param.type_idx.data_type;
        ctrl_point.mdata_slave = point_param.mdata_slave;
        ctrl_point.mdata_addr = addr + i;
        ctrl_point.mdata_func_code = point_param.mdata_func_code;
        ctrl_point.mdata_number = 1;
        ctrl_point.mdata_types = point_param.mdata_types;
        dev_points_.ctrl_list.insert(std::make_pair(ctrl_point.type_idx.GetKey(), ctrl_point));
    }
    return true;
}

bool ModbusDevicePoints::ParsePointParam(const std::string &value, PointParam &point_param) {
    if (value.empty()) {
        return false;
    }
    StrList values = PubOpt::StringOpt::StringSplit(value, ",");
    if (values.size() < 4) {
        return false;
    }
    point_param.mdata_slave = PubOpt::TypeOpt::StringToInt32(values.front());
    values.pop_front();
    point_param.mdata_func_code = PubOpt::TypeOpt::StringToInt32(values.front());
    values.pop_front();
    point_param.mdata_addr = PubOpt::TypeOpt::StringToInt32(values.front());
    values.pop_front();
    point_param.mdata_number = PubOpt::TypeOpt::StringToInt32(values.front());
    values.pop_front();
    if (!values.empty()) {
        point_param.mdata_types = PubOpt::TypeOpt::StringToInt32(values.front());
        values.pop_front();
    }
    return true;
}

bool ModbusDevicePoints::ParseTypeIndex(const std::string &key, TypeIndex &type_index) {
    if (key.empty()) {
        return false;
    }
    StrList keys = PubOpt::StringOpt::StringSplit(key, ":");
    if (keys.size() != 2) {
        return false;
    }
    type_index.data_type = PubOpt::TypeOpt::StringToInt32(keys.front());
    keys.pop_front();
    type_index.data_idx = PubOpt::TypeOpt::StringToInt32(keys.front());
    keys.pop_front();
    return true;
}

bool ModbusDevicePoints::GetDevicePoints(DevicePoints &dev_points) {
    if (point_file_.empty()) {
        return false;
    }
    dev_points.read_list.clear();
    dev_points.read_list.assign(dev_points_.read_list.begin(), dev_points_.read_list.end());
    dev_points.ctrl_list.clear();
    dev_points.ctrl_list.insert(dev_points_.ctrl_list.begin(), dev_points_.ctrl_list.end());
    return true;
}
