QT -= gui
QT += sql network

CONFIG += c++11 console
CONFIG -= app_bundle

DESTDIR = /home/<USER>/qt-linux/bin/linux
TARGET = xj_svg_collect

# The following define makes your compiler emit warnings if you use
# any feature of Qt which as been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if you use deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += main.cpp \
    agvcmysqlredis.cpp \
    modbus_device_points.cpp \
    modbus_comm_impl.cpp \
    redis_callback_worker.cpp \
    xjsvg_config_manager.cpp \
    xjsvg_device_service.cpp \
    xjsvg_device_manager.cpp \
    xjsvg_master_service.cpp

DISTFILES += \
    xj_svg_collect.ini \
    point/xj_svg_point.ini

HEADERS += \
    agvcmysqlredis.h \
    modbus_device_points.h \
    modbus_comm_impl.h \
    redis_callback_worker.h \
    xjsvg_config_manager.h \
    xjsvg_device_service.h \
    xjsvg_device_manager.h \
    xjsvg_master_service.h

INCLUDEPATH += -I /home/<USER>/qt-Linux/bin/common
INCLUDEPATH += -I /home/<USER>/qt-Linux/bin/hiredis-1.1.0
INCLUDEPATH += -I /home/<USER>/qt-Linux/bin/pub_comm

LIBS += -L/home/<USER>/qt-Linux/bin/linux -lrdbi -lCLibMySql -lhiredis -lpub_comm -lboost_thread -lmodbus
