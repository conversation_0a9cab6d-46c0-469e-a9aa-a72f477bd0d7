<?xml version='1.0' encoding='UTF-8'?>
<Templet>
 <!-- 装置模板表-->
 <deviceTemplet>
  <!-- 装置模板id 装置模板名  版本                  说明           备注              采样时间          规约ID-->
  <val id="2002" name="许继SVG快速采集" version="V2.0-230304" explain="无" remark="许继SVG快速采集" collecttime="110" templetid="2002"/>
 </deviceTemplet>
 <!-- 装置端子模板表-->
 <terminalTemplet>
  <!-- 装置模板id        端子类型       同类端子序号    特征字         名称       单位    系数-->
  <!-- 遥测区域 -->
  <val templetid="2002" type="遥测量" index="1" tagword="一般遥测" name="SVG侧无功功率" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="2" tagword="一般遥测" name="SVG可增无功" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="3" tagword="一般遥测" name="SVG可减无功" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="4" tagword="一般遥测" name="装置无功功率指令" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="5" tagword="一般遥测" name="SVG侧Uab" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="6" tagword="一般遥测" name="SVG侧Ubc" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="7" tagword="一般遥测" name="SVG侧Uca" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="8" tagword="一般遥测" name="SVG侧Ia" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="9" tagword="一般遥测" name="SVG侧Ib" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="10" tagword="一般遥测" name="SVG侧Ic" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="11" tagword="一般遥测" name="电网电压频率" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="12" tagword="一般遥测" name="模块最高电压" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="13" tagword="一般遥测" name="模块最低电压" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="14" tagword="一般遥测" name="模块最高温度" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="15" tagword="一般遥测" name="模块最低温度" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="16" tagword="一般遥测" name="A相故障模块数" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="17" tagword="一般遥测" name="B相故障模块数" unit="" xs="1"/>
  <val templetid="2002" type="遥测量" index="18" tagword="一般遥测" name="C相故障模块数" unit="" xs="1"/>
  <!-- 遥信区域 -->
  <val templetid="2002" type="遥信量" index="1" tagword="普通遥信" name="就绪" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="2" tagword="普通遥信" name="启动" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="3" tagword="普通遥信" name="运行" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="4" tagword="普通遥信" name="正常停机" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="5" tagword="普通遥信" name="保护停机" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="6" tagword="普通遥信" name="总保护" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="7" tagword="普通遥信" name="总报警" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="8" tagword="普通遥信" name="复位" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="9" tagword="普通遥信" name="远方控制" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="10" tagword="普通遥信" name="本地控制" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="11" tagword="普通遥信" name="本机站内自主运行状态" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="12" tagword="普通遥信" name="本机响应AVC控制使能" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="13" tagword="普通遥信" name="本机AVC运行状态" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="14" tagword="普通遥信" name="本机站内协调运行状态" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="15" tagword="普通遥信" name="业主断路器闭合" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="16" tagword="普通遥信" name="并网开关闭合" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="17" tagword="普通遥信" name="软启开关闭合" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="18" tagword="普通遥信" name="隔离开关闭合" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="19" tagword="普通遥信" name="正常投运模式" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="20" tagword="普通遥信" name="开入开出测试模式" unit="" xs="0"/>
  <val templetid="2002" type="遥信量" index="21" tagword="普通遥信" name="恒装置无功控制" unit="" xs="0"/>
  <!-- 遥控区域 -->
  <!-- 遥调区域 -->
 </terminalTemplet>
</Templet>
