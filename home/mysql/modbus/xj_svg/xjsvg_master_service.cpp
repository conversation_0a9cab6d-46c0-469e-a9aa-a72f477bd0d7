#include "log.h"
#include "public_function_library.h"
#include "xjsvg_device_manager.h"
#include "xjsvg_master_service.h"

XJSVGMasterService::XJSVGMasterService(void) {
}

XJSVGMasterService::~XJSVGMasterService(void) {
    StopService();
}

bool XJSVGMasterService::InitService(const std::string& cfg_file) {
    if (!SINGLETON(XJSVGConfigManager)->LoadConfig(cfg_file)) {
        WRITE_ERROR_LOG("配制文件:%s, 加载失败!", cfg_file.c_str());
        return false;
    }
    WRITE_INFO_LOG("配制文件:%s, 加载成功!", cfg_file.c_str());
    SINGLETON(XJSVGConfigManager)->GetServiceParam(serv_param_);
    if (serv_param_.dev_params.empty()) {
        WRITE_ERROR_LOG("配制文件, 设备列表为空!");
        return false;
    }
    return true;
}

void XJSVGMasterService::RunService(void) {
    std::map<int, DeviceParam>::iterator iter = serv_param_.dev_params.begin();
    for(; iter != serv_param_.dev_params.end(); ++iter) {
        SINGLETON(XJSVGDeviceManager)->AddDevice(iter->second);
    }
}

void XJSVGMasterService::StopService(void) {
    SINGLETON(XJSVGDeviceManager)->ClearDevices();
}
