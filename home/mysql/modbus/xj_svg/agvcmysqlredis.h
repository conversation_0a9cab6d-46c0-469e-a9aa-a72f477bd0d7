#ifndef AGVCMYSQLREDIS_H
#define AGVCMYSQLREDIS_H

#include <QtCore/qglobal.h>

#if defined(AGVCMYSQLREDIS_LIBRARY)
#define AGVCMYSQLREDISSHARED_EXPORT Q_DECL_EXPORT
#else
#  define AGVCMYSQLREDISSHARED_EXPORT Q_DECL_IMPORT
#endif

#include "rdbi.h"

class AGVCMYSQLREDISSHARED_EXPORT AgvcMysqlRedis
{
public:
    AgvcMysqlRedis();
    virtual ~AgvcMysqlRedis();
private:
    void connectRight();
public:
    bool InitService(const std::string &redis_ip, const int &redis_port);
    void StopService(void);

    void SetStationID(const int &station_id);
    int GetStationID(void);

    bool sendYTOrder(const int deviceid,const int dataid,double val,unsigned char status);
    bool sendYKOrder(const int deviceid,const int dataid,unsigned short val,unsigned char status);

    bool sendDZOrder(const int deviceid,const int dataid,double val,unsigned char status);
    bool sendTTOrder(const int deviceid,const int dataid,double val,unsigned char status);

    int setAgvcYCValue(int rtuid, int dataid, double realVal);
    bool getAgvcYCValue(int rtuid, int dataid, double& realVal, unsigned char& status);
    int setAgvcYCValue(int rtuid, int dataid, double realVal, double calcVal);
    bool getAgvcYCValue(int rtuid, int dataid, double& realVal, double &calcVal, unsigned char& status);

    int setAgvcYXValue(int rtuid, int dataid,short realVal);
    bool getAgvcYXValue(int rtuid,int dataid,short& realVal, unsigned char& status);
    int setAgvcYXValue(int rtuid, int dataid, short realVal, short calcVal);
    bool getAgvcYXValue(int rtuid, int dataid,short& realVal, short& calcVal, unsigned char& status);

    int setAgvcGZValue(int rtuid,int dataid,short gzvalue);
    bool getAgvcGZValue(int rtuid,int dataid,short& gzvalue,unsigned char& status);
    bool getAgvcYMValue(int rtuid,int dataid,double& ymvalue,unsigned char& status);
    bool getAgvcXBValue(int rtuid,int dataid,double& xbvalue,unsigned char& status);
private:
    int station_id_;
    RDBI *m_rdbi;
    bool redis_is_start_;
};

#endif // AGVCMYSQLREDIS_H
