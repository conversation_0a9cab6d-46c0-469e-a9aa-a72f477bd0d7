#include "log.h"
#include "boost_socket.h"
#include "public_function_library.h"
#include "agvcmysqlredis.h"
#include "redis_callback_worker.h"
#include "modbus_comm_impl.h"
#include "xjsvg_device_service.h"
#include <bitset>

XJSVGDeviceService::XJSVGDeviceService(const int& dev_id) {
    dev_param_.dev_id = dev_id;
    mutex_lock_.reset(new MutexLock());
    redis_obj_.reset(new AgvcMysqlRedis());
    modbus_obj_.reset(new ModbusCommImpl());
    redis_callback_ = new RedisCallBackWorker(this);
    m_pSql.Load();
}

XJSVGDeviceService::~XJSVGDeviceService(void) {
    if (NULL != redis_callback_) {
        delete redis_callback_;
        redis_callback_ = NULL;
    }
    redis_obj_.reset();
    mutex_lock_.reset();
    modbus_obj_.reset();
    dev_param_.dev_id  = 0;
    m_pSql.Unload();
}

bool XJSVGDeviceService::InitService(void) {
    if (!SINGLETON(XJSVGConfigManager)->GetServParamByDevID(dev_param_.dev_id , dev_param_)) {
        WRITE_ERROR_LOG("设备[%s], 设备配制参数获取失败", dev_param_.GetKey().c_str());
        return false;
    }
    switch (dev_param_.dev_comm_type) {
    case 0:
        if (!modbus_obj_->InitService(dev_param_.modbus_rtu)) {
            WRITE_ERROR_LOG("设备[%s], ModbusRTU通信[%s:%d], 初始化失败",
                            dev_param_.GetKey().c_str(),
                            dev_param_.modbus_rtu.device.c_str(), dev_param_.modbus_rtu.baud);
            return false;
        }
        WRITE_INFO_LOG("设备[%s], ModbusRTU通信[%s:%d], 初始化成功",
                       dev_param_.GetKey().c_str(),
                       dev_param_.modbus_rtu.device.c_str(), dev_param_.modbus_rtu.baud);
        break;
    default:
        return false;
    }
    m_vectyc.clear();
    if (!m_pSql.SelectYCByDeviceID(m_vectyc, GetDeviceID())) {
        WRITE_ERROR_LOG("设备[%d], MySQL数据库遥测点表加载失败", GetDeviceID());
        return false;
    }
    m_vectyt.clear();;
    if (!m_pSql.SelectYTByDeviceID(m_vectyt, GetDeviceID())) {
        WRITE_ERROR_LOG("设备[%d], MySQL数据库遥调点表加载失败", GetDeviceID());
        return false;
    }
    ServiceParam serv_param;
    SINGLETON(XJSVGConfigManager)->GetServiceParam(serv_param);
    if (!redis_obj_->InitService(serv_param.redis_ip, serv_param.redis_port)) {
        WRITE_ERROR_LOG("设备[%s], Redis数据库[%s:%d], 初始化失败", dev_param_.GetKey().c_str() , serv_param.redis_ip.c_str(), serv_param.redis_port);
        return false;
    }
    WRITE_INFO_LOG("设备[%s], Redis数据库[%s:%d], 初始化成功", dev_param_.GetKey().c_str() , serv_param.redis_ip.c_str(), serv_param.redis_port);
    if (!redis_callback_->InitService()) {
        return false;
    }
    Thread::SetTimeOut(serv_param.report_time);
    return LoadPoints(dev_param_.point_file);
}

void XJSVGDeviceService::RunService(void) {
    redis_callback_->RunService();
    Thread::Start();
}

void XJSVGDeviceService::Run(void) {
    PointList::iterator iter = device_points_.read_list.begin();
    for(; iter != device_points_.read_list.end(); ++iter) {
        switch (iter->type_idx.data_type) {
        case 1:
            ReportYCData(*iter);
            break;
        case 2:
            ReportYXData(*iter);
            break;
        default:
            WRITE_ERROR_LOG("设备[%s], 未知数据类型:%d", dev_param_.GetKey().c_str(), iter->type_idx.data_type);
            continue;
        }
    }
}

bool XJSVGDeviceService::LoadPoints(const std::string &point_file) {
    boost::shared_ptr<ModbusDevicePoints> dev_point(new ModbusDevicePoints());
    if (!dev_point->LoadPoints(point_file)) {
        WRITE_ERROR_LOG("设备[%s], 点表配制文件:%s，加载失败", dev_param_.GetKey().c_str(), point_file.c_str());
        return false;
    }
    WRITE_INFO_LOG("设备[%s], 点表配制文件:%s，加载成功", dev_param_.GetKey().c_str(), point_file.c_str());
    return dev_point->GetDevicePoints(device_points_);
}

bool XJSVGDeviceService::ReportYCData(const PointParam &report_params) {
    if (!DoReportModbusData(report_params)) {
        WRITE_ERROR_LOG("设备[%s], 遥测数据[DIdx:%d, MFunCode:%d MAddr:%02X MNb:%d]，上报失败",
                        dev_param_.GetKey().c_str(),
                        report_params.type_idx.data_idx, report_params.mdata_func_code, report_params.mdata_addr, report_params.mdata_number);
        return false;
    }
    WRITE_DEBUG_LOG("设备[%s], 遥测数据[DIdx:%d, MFunCode:%d MAddr:%02X MNb:%d]，上报成功",
                    dev_param_.GetKey().c_str(),
                    report_params.type_idx.data_idx, report_params.mdata_func_code, report_params.mdata_addr, report_params.mdata_number);
    return true;
}

bool XJSVGDeviceService::ReportYXData(const PointParam &report_params) {
    if (!DoReportModbusData(report_params)) {
        WRITE_ERROR_LOG("设备[%s], 遥信数据[DIdx:%d, MFunCode:%d MAddr:%02X MNb:%d]，上报失败", dev_param_.GetKey().c_str(),
                        report_params.type_idx.data_idx, report_params.mdata_func_code, report_params.mdata_addr, report_params.mdata_number);
        return false;
    }
    WRITE_DEBUG_LOG("设备[%s], 遥信数据[DIdx:%d, MFunCode:%d MAddr:%02X MNb:%d]，上报成功", dev_param_.GetKey().c_str(),
                    report_params.type_idx.data_idx, report_params.mdata_func_code, report_params.mdata_addr, report_params.mdata_number);
    return true;
}

bool XJSVGDeviceService::DoReportModbusData(const PointParam &report_params) {
    bool result = false;
    switch(report_params.mdata_func_code) {
    case 0x01:
        result = ReportBitsData(report_params);
        break;
    case 0x03:
        result = ReportRegistersData(report_params);
        break;
    default:
        WRITE_ERROR_LOG("设备[%s], 未知Modbus功能码:%d", dev_param_.GetKey().c_str(), report_params.mdata_func_code);
        break;
    }
    return result;
}

bool XJSVGDeviceService::ReportBitsData(const PointParam &report_params) {
    AutoLock auto_lock(*mutex_lock_.get());
    modbus_obj_->SetSlaveId(report_params.mdata_slave);
    uint8_t* dest = new uint8_t[report_params.mdata_number];
    if (!modbus_obj_->ReadBits(report_params.mdata_addr, report_params.mdata_number, dest)) {
        return false;
    }

    bool result = ReportStatusDatas(dest, report_params);
    if (!result) {
        WRITE_ERROR_LOG("设备[%s], 上报线圈状态寄存器数据失败", dev_param_.GetKey().c_str());
    }
    delete[] dest;

    return result;
}

bool XJSVGDeviceService::ReportStatusDatas(uint8_t *dest, const PointParam &report_params) {
    bool result = false;
    switch (report_params.type_idx.data_type) {
    case 1:
        result = ReportStatusYCDatas(dest, report_params);
        break;
    case 2:
        result = ReportStatusYXDatas(dest, report_params);
        break;
    default:
        break;
    }
    return result;
}

bool XJSVGDeviceService::ReportStatusYCDatas(uint8_t *dest, const PointParam &report_params) {
    if (NULL == dest) {
        return false;
    }
    for(int i=0; i<report_params.mdata_number; ++i) {
        WRITE_DEBUG_LOG("设备[%s], Redis状态YC数据写入[type:%d idx:%d slaveId:%d addr:%02X data:%d]", dev_param_.GetKey().c_str(),
                        report_params.type_idx.data_type, report_params.type_idx.data_idx+i,
                        report_params.mdata_slave, report_params.mdata_addr+i, dest[i]);
        WriteRedisDB(report_params.type_idx.data_type, report_params.type_idx.data_idx+i, dest[i]);
    }
    return true;
}

bool XJSVGDeviceService::ReportStatusYXDatas(uint8_t *dest, const PointParam &report_params) {
    if (NULL == dest) {
        return false;
    }   

    for(int i=0; i<report_params.mdata_number; ++i) {
        WRITE_DEBUG_LOG("设备[%s], Redis状态YX数据写入[type:%d idx:%d slaveId:%d addr:%02X data:%d]",
                        dev_param_.GetKey().c_str(),
                        report_params.type_idx.data_type, report_params.type_idx.data_idx+i,
                        report_params.mdata_slave, report_params.mdata_addr+i, dest[i]);
        WriteRedisDB(report_params.type_idx.data_type, report_params.type_idx.data_idx+i, dest[i]);
    }   

    return true;
}

bool XJSVGDeviceService::ReportRegistersData(const PointParam &report_params) {
    AutoLock auto_lock(*mutex_lock_.get());
    modbus_obj_->SetSlaveId(report_params.mdata_slave);
    uint16_t* dest = new uint16_t[report_params.mdata_number];
    if (!modbus_obj_->ReadRegisters(report_params.mdata_addr, report_params.mdata_number, dest)) {
        return false;
    }
    bool result = ReportRegisterDatas(dest, report_params);
    if (!result) {
        WRITE_ERROR_LOG("设备[%s], 上报保持寄存器数据失败", dev_param_.GetKey().c_str());
    }
    delete[] dest;
    return result;
}

bool XJSVGDeviceService::ReportRegisterDatas(uint16_t *dest, const PointParam &report_params) {
    bool result = false;
    switch (report_params.type_idx.data_type) {
    case 1:
        result = ReportRegisterYCDatas(dest, report_params);
        break;
    case 2:
        result = ReportRegisterYXDatas(dest, report_params);
        break;
    default:
        break;
    }
    return result;
}

bool XJSVGDeviceService::ReportRegisterYCDatas(uint16_t *dest, const PointParam &report_params) {
    if (NULL == dest) {
        return false;
    }
    TypeIndex type_idx;
    type_idx.data_idx = report_params.type_idx.data_idx;
    type_idx.data_type = report_params.type_idx.data_type;
    for(int i=0; i<report_params.mdata_number; ++i) {
        WriteRedisDB(type_idx.data_type, type_idx.data_idx, short(dest[i]));
        type_idx.data_idx += 1;
    }
    return true;
}

bool XJSVGDeviceService::ReportRegisterYXDatas(uint16_t *dest, const PointParam &report_params) {
    if (NULL == dest) {
        return false;
    }
    TypeIndex type_idx;
    type_idx.data_idx = report_params.type_idx.data_idx;
    type_idx.data_type = report_params.type_idx.data_type;
    for(int i=0; i<report_params.mdata_number; ++i) {
        int data = dest[i]==0?0:1;
        WriteRedisDB(type_idx.data_type, type_idx.data_idx, data);
        type_idx.data_idx += 1;

    }
    return true;
}

int XJSVGDeviceService::WriteRedisDB(const int &type, const int &idx, const double &data) {
    int result = -1;
    switch (type) {
    case 1:
        result = redis_obj_->setAgvcYCValue(dev_param_.dev_id, idx, GetYCRealValue(idx, data), data);
        WRITE_DEBUG_LOG("设备[%s], 写入遥测数据[rutId:%d dataId:%d real_data:%f calc_data:%f]", dev_param_.GetKey().c_str(), GetDeviceID(), idx, GetYCRealValue(idx, data), data);
        break;
    case 2:
        result = redis_obj_->setAgvcYXValue(dev_param_.dev_id, idx, data, data);
        WRITE_DEBUG_LOG("设备[%s], 写入遥信数据[rutId:%d dataId:%d real_data:%f calc_data:%f]", dev_param_.GetKey().c_str(), GetDeviceID(), idx, data, data);
        break;
    default:
        WRITE_ERROR_LOG("设备[%s], 未知Redis数据类型:%d", dev_param_.GetKey().c_str(), type);
        break;
    }
    return result;
}

double XJSVGDeviceService::GetYCRealValue(const int &idx, const double &calcVal) {
    double result = calcVal * GetYCPointXS(GetDeviceID(), idx);
    return result;
}

double XJSVGDeviceService::GetYTCaclValue(const int &idx, const double &realVal) {
    double result = realVal / GetYTPointXS(GetDeviceID(), idx);
    return result;
}

bool XJSVGDeviceService::WriteModbusDB(const int &type, const int &idx, const double& val) {
    uint32_t data = int(val);
    if (type == 3) {
        data = int(GetYTCaclValue(idx, val));
    } else if (type == 4) {
        data = (data == 1)?0xFF00:0x00;
    }
    WRITE_DEBUG_LOG("设备[%s], 写入Modbus数据[rutId:%d type:%d dataId:%d data:%d]", dev_param_.GetKey().c_str(), GetDeviceID(), type, idx, data);
    TypeIndex data_idx{type, idx};
    if (device_points_.ctrl_list.find(data_idx.GetKey()) == device_points_.ctrl_list.end()) {
        WRITE_ERROR_LOG("设备[%s], 不存在的控制点[%s]", dev_param_.GetKey().c_str(), data_idx.GetKey().c_str());
        return false;
    }
    bool result = false;
    int func_code = device_points_.ctrl_list[data_idx.GetKey()].mdata_func_code;
    AutoLock auto_lock(*mutex_lock_.get());
    switch (func_code)
    {
    case 0x05:
        modbus_obj_->SetSlaveId(device_points_.ctrl_list[data_idx.GetKey()].mdata_slave);
        result = modbus_obj_->WriteBit(device_points_.ctrl_list[data_idx.GetKey()].mdata_addr, data);
        break;
    case 0x06:
        modbus_obj_->SetSlaveId(device_points_.ctrl_list[data_idx.GetKey()].mdata_slave);
        uint16_t reg_data;
        reg_data = uint16_t(data & 0xFFFF);
        result = modbus_obj_->WriteRegister(device_points_.ctrl_list[data_idx.GetKey()].mdata_addr, reg_data);
        break;
    default:
        WRITE_ERROR_LOG("设备[%s], 控制点[%s]-功能码[%d], 无操作权限", dev_param_.GetKey().c_str(), data_idx.GetKey().c_str(), func_code);
        return false;
    }
    if (!result) {
        WRITE_ERROR_LOG("设备[%s], 控制点[%s], 写入数据[%d:%d:%d], 失败",
                        dev_param_.GetKey().c_str(), data_idx.GetKey().c_str(),
                       device_points_.ctrl_list[data_idx.GetKey()].mdata_slave, device_points_.ctrl_list[data_idx.GetKey()].mdata_addr, data);
        return false;
    }
    WRITE_INFO_LOG("设备[%s], 控制点[%s], 写入数据[%d:%d:%d], 成功", dev_param_.GetKey().c_str(), data_idx.GetKey().c_str(),
                   device_points_.ctrl_list[data_idx.GetKey()].mdata_slave, device_points_.ctrl_list[data_idx.GetKey()].mdata_addr, data);
    return true;
}

void XJSVGDeviceService::StopService(void) {
    redis_callback_->StopService();
    Thread::Stop();
    redis_obj_->StopService();
    modbus_obj_->StopService();
}

int XJSVGDeviceService::GetDeviceID(void) {
    return dev_param_.dev_id;
}

double XJSVGDeviceService::GetYCPointXS(const int dev_id, const int idex) {
    double xs = 1;
    VecParaYC::iterator iter = m_vectyc.begin();
    for(; iter != m_vectyc.end(); ++iter) {
        if ((*iter).DeviceID == dev_id && (*iter).DeviceIndex == idex) {
            xs = (*iter).Rate;
            break;
        }
    }
    return xs;
}

double XJSVGDeviceService::GetYTPointXS(const int dev_id, const int idex) {
    double xs = 1;
    VecParaYT::iterator iter = m_vectyt.begin();
    for(; iter != m_vectyt.end(); ++iter) {
        if ((*iter).DeviceID == dev_id && (*iter).DeviceIndex == idex) {
            xs = (*iter).Xs;
            break;
        }
    }
    return xs;
}
