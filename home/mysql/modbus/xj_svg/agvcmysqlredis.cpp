#include "log.h"

#include "agvcmysqlredis.h"

#include <QDebug>
#include <dirent.h>

AgvcMysqlRedis::AgvcMysqlRedis()
{
    m_rdbi = new RDBI();
    redis_is_start_ = false;
}

AgvcMysqlRedis::~AgvcMysqlRedis()
{
    StopService();
    if(NULL != m_rdbi)
    {
        delete m_rdbi;
        m_rdbi = NULL;
    }
}

bool AgvcMysqlRedis::InitService(const std::string &redis_ip, const int &redis_port)
{
    bool ret = m_rdbi->Connect(redis_ip.c_str(), redis_port);
    if(!ret)
    {
        WRITE_ERROR_LOG("Redis服务[%s:%d]，连接失败", redis_ip.c_str(), redis_port);
        return false;
    }
    WRITE_INFO_LOG("Redis服务连接成功");
    redis_is_start_ = true;
    return true;
}

void AgvcMysqlRedis::StopService(void)
{
    if (redis_is_start_)
    {
        m_rdbi->Disconnect();
    }
    redis_is_start_ = false;
}

void AgvcMysqlRedis::SetStationID(const int &station_id)
{
    station_id_ = station_id;
}

int AgvcMysqlRedis::GetStationID(void)
{
    return station_id_;
}

void AgvcMysqlRedis::connectRight()
{
    bool ret;
    ret = m_rdbi->IsConnected();
    if(!ret)
    {
        m_rdbi->Connect();
    }
}

bool AgvcMysqlRedis::sendYTOrder(const int deviceid, const int dataid,
                                double val,unsigned char status)
{
    REDIS_YT ytdata;
    ytdata.deviceId = deviceid;
    ytdata.dataId = dataid;
    ytdata.val = val;
    ytdata.status = status;
    return m_rdbi->SendYT(ytdata,true);
}

bool AgvcMysqlRedis::sendYKOrder(const int deviceid, const int dataid,
                                unsigned short val,unsigned char status)
{
    REDIS_YK ykdata;
    ykdata.deviceId = deviceid;
    ykdata.dataId = dataid;
    ykdata.val = val;
    ykdata.status = status;
    return m_rdbi->SendYK(ykdata, true);
}

bool AgvcMysqlRedis::sendDZOrder(const int deviceid, const int dataid, double val, unsigned char status)
{
    REDIS_DZ dzdata;
    dzdata.deviceId = deviceid;
    dzdata.dataId = dataid;
    dzdata.val = val;
    dzdata.status = status;
    return m_rdbi->SendDZ(dzdata, true);
}

bool AgvcMysqlRedis::sendTTOrder(const int deviceid, const int dataid, double val, unsigned char status)
{
    REDIS_TT ttdata;
    ttdata.deviceId = deviceid;
    ttdata.dataId = dataid;
    ttdata.val = val;
    ttdata.status = status;
    return m_rdbi->SendTT(ttdata, true);
}

int AgvcMysqlRedis::setAgvcYCValue(int rtuid, int dataid, double realVal)
{
    connectRight();

    int ret;
    ret = m_rdbi->SetYCRealValue(rtuid, dataid, realVal);
    m_rdbi->FreeReplies(ret);
    return 0;
}

bool AgvcMysqlRedis::getAgvcYCValue(int rtuid, int dataid, double &realVal, unsigned char &status)
{
    connectRight();
    return m_rdbi->GetYCRealValue(rtuid, dataid, realVal, status);
}

int AgvcMysqlRedis::setAgvcYCValue(int rtuid, int dataid, double realVal, double calcVal)
{
    connectRight();

    int ret;
    ret = m_rdbi->SetYCRealValue(rtuid, dataid, realVal, calcVal);
    m_rdbi->FreeReplies(ret);
    return 0;
}

bool AgvcMysqlRedis::getAgvcYCValue(int rtuid, int dataid, double &realVal, double &calcVal, unsigned char &status)
{
    connectRight();
    return m_rdbi->GetYCRealValue(rtuid, dataid, realVal, calcVal, status);
}

int AgvcMysqlRedis::setAgvcYXValue(int rtuid, int dataid, short realVal)
{
    connectRight();

    int ret;
    ret = m_rdbi->SetYXRealValue(rtuid, dataid, realVal);
    m_rdbi->FreeReplies(ret);
    return 0;
}

bool AgvcMysqlRedis::getAgvcYXValue(int rtuid, int dataid, short &realVal, unsigned char &status)
{
    connectRight();
    return m_rdbi->GetYXRealValue(rtuid, dataid, realVal, status);
}

int AgvcMysqlRedis::setAgvcYXValue(int rtuid, int dataid, short realVal, short calcVal)
{
    connectRight();

    int ret;
    ret = m_rdbi->SetYXRealValue(rtuid, dataid, realVal, calcVal);
    m_rdbi->FreeReplies(ret);
    return 0;
}

bool AgvcMysqlRedis::getAgvcYXValue(int rtuid, int dataid, short &realVal, short &calcVal, unsigned char &status)
{
    connectRight();
    return m_rdbi->GetYXRealValue(rtuid, dataid, realVal, calcVal, status);
}

bool AgvcMysqlRedis::getAgvcYMValue(int rtuid, int dataid, double &ymvalue, unsigned char &status)
{
    connectRight();
    return m_rdbi->GetYMRealValue(rtuid,dataid,ymvalue,status);
}

bool AgvcMysqlRedis::getAgvcGZValue(int rtuid, int dataid, short &gzvalue, unsigned char &status)
{
    connectRight();
    return m_rdbi->GetGZRealValue(rtuid,dataid,gzvalue,status);
}

bool AgvcMysqlRedis::getAgvcXBValue(int rtuid, int dataid, double &xbvalue, unsigned char &status)
{
    connectRight();
    return m_rdbi->GetXBRealValue(rtuid,dataid,xbvalue,status);
}

int AgvcMysqlRedis::setAgvcGZValue(int rtuid, int dataid, short gzvalue)
{
    int ret;
    connectRight();
    ret = m_rdbi->SetGZRealValue(rtuid,dataid,gzvalue);
    m_rdbi->FreeReplies(ret);
    return 0;
}
