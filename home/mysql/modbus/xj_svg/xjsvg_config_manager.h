#ifndef __XJSVG_CONFIG_MANAGER_H__
#define __XJSVG_CONFIG_MANAGER_H__

#include "singleton.h"
#include "modbus_comm_impl.h"

#include <boost/shared_ptr.hpp>

#define SERVICE_KEY         ("Service")
#define SERVICE_REDIS_IP        ("RedisIP")
#define SERVICE_REDIS_PORT      ("RedisPort")
#define SERVICE_REPORT_TIME     ("ReportTime")

#define DEVICE_NAME             ("DevName")
#define DEVICE_POINT_FILE       ("PointFile")
#define DEVICE_COMM_TYPE        ("CommType")

#define DEVICE_RTU_DEVICE       ("Device")
#define DEVICE_RTU_BAUD         ("Baud")
#define DEVICE_RTU_PARITY       ("Parity")
#define DEVICE_RTU_DATABIT      ("DataBit")
#define DEVICE_RTU_STOPBIT      ("StopBit")

typedef struct DeviceParam
{
    int dev_id;
    std::string dev_name;
    std::string point_file;
    int dev_comm_type;
    RtuCommParam modbus_rtu;
    DeviceParam(void)
    {
        dev_id = 0;
        dev_name = "XJ-SVG";
        dev_comm_type = 0;
        point_file = "xj_svg_point.ini";
    }
    std::string GetKey(void)
    {
        return PubOpt::StringOpt::StringFormat("%s-%d", dev_name.c_str(), dev_id);
    }
}*P_DEVICE_PARAM;

typedef struct ServiceParam
{
    std::string redis_ip;
    unsigned short redis_port;
    unsigned int report_time;
    std::map<int, DeviceParam>  dev_params;
    ServiceParam(void)
    {
        redis_ip = "127.0.0.1";
        redis_port = 6379;
        report_time = 3000;
    }
}*P_SERVICE_PARAM;

class MutexLock;
class LoadIniConfig;

class XJSVGConfigManager
{
    DECLARE_SINGLETON_INIT(XJSVGConfigManager)
public:
    bool LoadConfig(const std::string &cfg_file);
    bool ReLoadConfig(void);
    bool GetServiceParam(ServiceParam& serv_param);
    bool GetServParamByDevID(const int &dev_id, DeviceParam& dev_params);
private:
    bool LoadALLConfigParam(void);
    bool LoadServConfigParams(const std::string& key_name);
    bool LoadDevConfigParams(const std::string& key_name, DeviceParam& dev_params);
    bool LoadDevModbusRtuParam(const std::string& key_name, RtuCommParam& rtu_param);
private:
    std::string config_file_;
    boost::shared_ptr<LoadIniConfig> cfg_obj_;
    boost::shared_ptr<MutexLock> mutex_lock_;
    ServiceParam service_param_;
};

#endif//__XJSVG_CONFIG_MANAGER_H__
