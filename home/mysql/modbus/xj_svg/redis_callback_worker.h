#ifndef REDIS_CALLBACK_WORKER_H
#define REDIS_CALLBACK_WORKER_H

#include <string>
#include <boost/shared_ptr.hpp>

#include "rdbi.h"
#include "xjsvg_device_service.h"

class RedisCallBackWorker
{
public:
    RedisCallBackWorker(XJSVGDeviceService* dev_obj);
    virtual ~RedisCallBackWorker(void);
public:
    bool InitService(void);
    void RunService(void);
    void StopService(void);
    static void ykMsgCallback(char *channel, char *msg, int msglen, void *pridata);
    static void ytMsgCallback(char *channel, char *msg, int msglen, void *pridata);
private:
    bool StartYKCallback(void);
    bool StartYTCallback(void);
private:
    XJSVGDeviceService* dev_obj_;
    RedisSubscriber *m_sub_yk;
    T_SubscribeInfo m_subinfo_yk;
    RedisSubscriber *m_sub_yt;
    T_SubscribeInfo m_subinfo_yt;
};

#endif // REDIS_CALLBACK_WORKER_H
