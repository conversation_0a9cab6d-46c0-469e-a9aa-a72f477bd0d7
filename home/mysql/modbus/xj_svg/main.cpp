#include "log.h"
#include "public_function_library.h"

#include "xjsvg_master_service.h"

#include <QCoreApplication>
using namespace std;

int main(int argc, char *argv[])
{
    std::string mode_name = PubOpt::FileOpt::GetNameByFile(argv[0]);
    std::string cfg_file = PubOpt::StringOpt::StringFormat("%s%s.ini", PubOpt::SystemOpt::GetCurrentProcPath().c_str(), mode_name.c_str());
    INIT_LOG_SIGLETON("/usr/local/log", mode_name);

    QCoreApplication a(argc, argv);

    // 启动BMS服务
    XJSVGMasterService bms_server;
    if (bms_server.InitService(cfg_file))
    {
        bms_server.RunService();
    }

    return a.exec();
}
