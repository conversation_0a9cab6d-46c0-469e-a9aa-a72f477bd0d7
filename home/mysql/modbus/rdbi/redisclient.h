#ifndef REDISCLIENT_H
#define REDISCLIENT_H

#include <hiredis.h>
#include <stdlib.h>
#include <stdio.h>
#include <string>
#include <errno.h>
#include <QString>


#define REDIS_REPLY_STRING 1
#define REDIS_REPLY_ARRAY 2
#define REDIS_REPLY_INTEGER 3
#define REDIS_REPLY_NIL 4
#define REDIS_REPLY_STATUS 5
#define REDIS_REPLY_ERROR 6

// 默认Redis服务器连接参数
#define LOCAL_SERVER		"127.0.0.1" // 本地IP "127.0.0.1"
#define REDIS_PORT			6379        // 服务端口
#define REDIS_DEFAULT_TIMEOUT   20     // 连接超时(ms)


class RedisClient
{
public:
    RedisClient();
    virtual ~RedisClient();

    QString m_host;

public:
    redisReply* SendRedisCmd(const char *format, ...);
    redisReply* SendRedisCmd(const char *format, va_list ap);
    redisReply* SendRedisCmdArgv(int argc, const char **argv, const size_t *argvlen);

    int AppendCommand(const char *format, ...);
    int AppendCommand(const char *format, va_list ap);

    bool Connect(const char* host = LOCAL_SERVER, int port = REDIS_PORT, int timeout = REDIS_DEFAULT_TIMEOUT,int type = 0);
    void Disconnect();
    bool IsConnected();

    bool QuitRedis();

    int GetReply(void** reply);
    void FreeReply(void *reply);


private:
    bool m_bConnected;    //连接标志
    redisContext* c;      // Redis Context

};

#endif // REDISCLIENT_H
