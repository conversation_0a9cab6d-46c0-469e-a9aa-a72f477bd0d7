﻿#ifndef PARAMDB_H
#define PARAMDB_H

#include <QtCore/QVector>

//数据表定义
#define DATA_TBL_UNKOWN     0x00    //未知
#define DATA_TBL_ANALOG		0x01	//遥测
#define DATA_TBL_DIGITAL	0x02	//遥信
#define DATA_TBL_IMPULSE	0x03	//电度
#define DATA_TBL_XB	0x04	//电度
#define DATA_TBL_GZ	0x05	//电度
#define DATA_TBL_YK         0x08    //遥控

//数据表名称定义
#define DATA_TBL_YC_NAME	TEXT("遥测")
#define DATA_TBL_YX_NAME	TEXT("遥信")	//遥信
#define DATA_TBL_YM_NAME	TEXT("遥脉")	//电度




//遥控表格
#define  YK_TYPE_SBO		(1<<0) //预置执行
#define  YK_TYPE_DO			(1<<1) //直接执行


/*参数数据库表格名称定义*/

//厂站
#define T_RTU							("t_rtu")

//厂站数据单位
#define T_RTU_DATA_UNIT					("t_rtu_data_unit")   

//scada节点
#define T_SCADA_NODE					("t_scada_node")

//前置机节点
#define T_FEP_NODE						("t_fep_node")

//前置机采集通道
#define T_FEP_CHANNEL					("t_fep_channel")

//前置机IEC104规约
#define T_FEP_IEC104					("t_fep_iec104")

//前置机五防CDT规约
#define T_FEP_WF_CDT_UDP				("t_fep_wf_cdt_udp")

//前置机其他规约类型
#define T_FEP_OTHER_TYPE				("t_fep_other_type")

//前置机MODBUSTCP规约	
#define T_FEP_MODBUS_TCP				("t_fep_modbus_tcp")

#define T_PROCESS						("t_process")

#define T_PROTOCOL						("t_protocol")

//厂站遥测
#define T_RTU_YC						("t_rtu_yc")

//厂站遥测类型
#define T_RTU_YC_TYPE					("t_rtu_yc_type")

//厂站遥控
#define T_RTU_YK						("t_rtu_yk")

//厂站遥调
#define T_RTU_YT						("t_rtu_yt")

//厂站遥信
#define T_RTU_YX						("t_rtu_yx")

//厂站遥信类型
#define T_RTU_YX_TYPE					("t_rtu_yx_type")

//厂站遥脉
#define T_RTU_YM						("t_rtu_ym")

//厂站遥脉类型
#define T_RTU_YM_TYPE					("t_rtu_ym_type")

//报表
#define T_RPT							("t_rpt")

//报表KEYCELL
#define T_RPT_KEYCELL					("t_rpt_keycell")

//报表时间点
#define T_RPT_TIME_POINT				("t_rpt_timepoint")

//报表定时任务
#define T_RPT_TIMER_TASK				("t_rpt_timer_task")

//用户
#define T_USER							("t_user")

//用户角色
#define T_USER_GROUP					("t_user_group")

//前置机采集通过规约
#define T_FEP_CHANNEL_PROTOCOL			("t_fep_channel_protocol")


#pragma  pack(1)



//通道参数表
#define	 CHANNEL_TYPE_UNKOWN			(0x00)
#define  CHANNEL_TYPE_SERIAL			(0x01) /*串口*/
#define  CHANNEL_TYPE_TCP				(0x02) /*TCP*/
#define	 CHANNEL_TYPE_UDP				(0x03) /*UDP*/

#define  CHANNEL_SERIAL_CHECK_NONE	0x01 /*无校验*/
#define  CHANNEL_SERIAL_CHECK_ODD	0x02 /*奇校验*/
#define  CHANNEL_SERIAL_CHECK_EVEN	0x03 /*偶校验*/
#define  CHANNEL_SERIAL_CHECK_SPACE 0x04 /*空校验*/
#define	 CHANNEL_SERIAL_CHECK_MARK	0x05 /*mark*/

/*==============================================================*/
/*前置机采集通道                                            */
/*==============================================================*/

struct FEPChannel
{
	unsigned int    id;                         /*id*/
	char			name[128];                        /*名称*/

	unsigned int	fepId; //前置机id
	unsigned int	rtuId; //需要映射数据的厂站id


	unsigned int	rtuYCBeginId; //厂站遥测表开始id
	unsigned int	rtuYCEndId; //厂站遥测表结束id

	unsigned int	rtuYXBeginId; //厂站遥信表开始id
	unsigned int	rtuYXEndId; //厂站遥信表结束id

	unsigned int	rtuYTBeginId; //厂站遥调表开始id
	unsigned int	rtuYTEndId; //厂站遥调表结束id

	unsigned int	rtuYKBeginId; //厂站遥控表开始id
	unsigned int	rtuYKEndId; //厂站遥控表结束id


	unsigned int	rtuYMBeginId; //厂站遥脉表开始id
	unsigned int	rtuYMEndId; //厂站遥脉表结束id

};


/************************************************************************/
/* 前置机采集通道规约                                                                     */
/************************************************************************/

struct FEPChannelProtocol
{
	unsigned int	id;
	char			name[256];//规约名称
};

/*==============================================================*/
/* 厂站                                            */
/*==============================================================*/
struct RTU
{
	unsigned int    id;                     /*id*/
	char			name[256];              /*厂站名称*/
};


/************************************************************************/
/* 前置机IEC104规约                                                                     */
/************************************************************************/
struct FEPIEC104
{
	unsigned int	id;
	unsigned int	fepChannelId; /*前置机采集通道id*/

	char			host1IP[128]; /*对端主通道主机IP地址*/
	unsigned int	host1Port; /*对端主通道主机端口*/

	char			host2IP[128];/*对端主通道主机IP地址*/
	unsigned int	host2Port;/*对端主通道主机IP地址*/

	unsigned int	yxAddr;/*遥信信息体开始地址（10进制）*/
	unsigned int	ycAddr;/*遥测信息体开始地址（10进制）*/
	unsigned int	ykAddr;/*遥控信息体开始地址（10进制）*/
	unsigned int	ytAddr;/*遥调信息体开始地址s（10进制）*/
	unsigned int	ymAddr;/*遥调信息体开始地址s（10进制）*/

	unsigned int	t0; /*t0超时时间*/
	unsigned int	t1; /*t1超时时间*/
	unsigned int	t2; /*t2超时时间*/
	unsigned int	t3; /*t3超时时间*/

	unsigned int	k;
	unsigned int	w;

	unsigned int	yxAsdu;//遥信asdu类型(10进制)
	unsigned int	ycAsdu;//遥asdu类型(10进制)
	unsigned int	ykAsdu;//遥控asdu类型(10进制)
	unsigned int	ytAsdu;//遥调asdu类型(10进制)
	unsigned int	soeAsdu;//SOEasdu类型(10进制)

	unsigned int	rtuAddress; //源和目的地址
	unsigned int	pollPeriod; //总召唤巡检周期

	unsigned int	adjustTime; //对时

};


/************************************************************************/
/* 前置机五防CDT规约                                                                     */
/* 2016年7月18日08:54:06 将前置机采集通道ID修改为前置机节点ID
/* 原有设计为映射部分厂站,设计不合理，修改为选点
/************************************************************************/
struct FEPWFCdtUDP
{
	unsigned int	id;
	unsigned int	fepNodeId; /*前置机节点id*/

	char			hostIP[128];/*五防主机IP地址*/
	unsigned int	localRecvPort;//本机接收端口
	unsigned int	remoteRecvPort;//五防主机接收端口

	
	unsigned int	yxCtrlWord;//遥信控制字(10进制)
	unsigned int	yxFrameType;//遥信帧类别(10进制)
	unsigned int	yxFun;//遥信起始功能码(10进制)

	char yxDataIds[8192]; //映射的遥信数据ID,以:分隔

	unsigned int	rtuAddress; //源和目的地址
	unsigned int	pollPeriod; //巡检周期
};



/************************************************************************/
/* 前置机其他类型规约                                                                     */
/************************************************************************/
struct FEPOtherType
{
	unsigned int	id;
	unsigned int	fepChannelId; /*前置机采集通道id*/

	char			remoteIP[128];//对端主机IP地址
	char			localIP[128];//本地主机IP地址
	unsigned int	localRecvPort;//本机接收端口
	unsigned int	remoteRecvPort;//对端主机接收端口

	unsigned int	serialPort; //串口号
	unsigned int	baudrate; //波特率
	unsigned int	databits; //数据位
	unsigned int	stopbits;//停止位
	unsigned int	parity; //校验

	char codeParam[256];					//规约特征字符

	unsigned int	rtuAddress; //RTU地址
	unsigned int	pollPeriod; //巡检周期
};

/************************************************************************/
/* 前置机MODBUSTCP规约                                                                     */
/************************************************************************/
struct FEPModbusTCP 
{
	unsigned int	id;
	unsigned int	fepChannelId; /*前置机采集通道id*/

	char			host1IP[128]; /*对端主通道主机IP地址*/
	unsigned int	host1Port; /*对端主通道主机端口*/

	char			host2IP[128];/*对端主通道主机IP地址*/
	unsigned int	host2Port;/*对端主通道主机IP地址*/

	unsigned int	yxFun;//遥信功能码(10进制)
	unsigned int	yxAddr;/*遥信开始地址（10进制）*/

	unsigned int	ycFun;//遥测功能码(10进制)
	unsigned int	ycAddr;/*遥测开始地址（10进制）*/
	unsigned int	ycType;/*遥测类型 */
						  /* 1:4字节短浮点数 高在前,低在后  2:4字节短浮点数 高在后,低在前  3:2字节整数*/

	unsigned int	ykFun;//遥控功能码(10进制)
	unsigned int	ykAddr;/*遥控开始地址（10进制）*/
	
	unsigned int	ytFun;//遥调功能码(10进制)
	unsigned int	ytAddr;/*遥调开始地址（10进制）*/
	unsigned int	ytType;/*遥测类型 1:4字节短浮点数 2:2字节整数*/

	unsigned int	ymAddr;//遥脉开始地址
	unsigned int	ymFun;//遥脉功能码

	unsigned int	rtuAddress; //源和目的地址
	unsigned int	pollPeriod; //巡检周期

	unsigned int	adjustTime; //对时
	
};

/*==============================================================*/
/* 前置机节点                                                */
/*==============================================================*/
struct FEPNode
{
	unsigned int        id;                     /*id*/

	char name[256];		//前置机名称

	char hostName1[128];//主前置机计算机名称
	char netA1IP[128];//主前置机A网IP地址
	char netB1IP[128];//主前置机B网IP地址

	char hostName2[128];//备前置机计算机名称
	char netA2IP[128];//备前置机A网IP地址
	char netB2IP[128];//备前置机B网IP地址
	
};

/*==============================================================*/
/* SCADA节点                                                */
/*==============================================================*/
#define SCADA_NODE_MASTER_RTDB  (1<<0) //主实时数据服务器
#define SCADA_NODE_SLVAE_RTDB	(1<<1) //备实时数据服务器
#define SCADA_NODE_OPRATER		(1<<2) //操作员站
#define SCADA_NODE_MASTER_HIS	(1<<3) //主历史数据服务器
#define SCADA_NODE_SLAVE_HIS	(1<<4)	//备历史数据服务器


struct SCADANode
{
	unsigned int    id;                     /*id*/
	unsigned int    actor;                   /*节点角色类型*/
	char			hostName[256];                   /*名称*/
	
	char			netAIP[128];	/*A网IP地址*/
	char			netBIP[128];	/*B网IP地址*/
};




/************************************************************************/
/* 厂站数据单位                                                                     */
/************************************************************************/
struct RTUDataUnit
{
	unsigned int       id;                      /*id*/
	char			   name[128];                 /*单位名称*/

};

/************************************************************************/
/* 厂站遥测类型                                                                     */
/************************************************************************/
struct RTUYCType
{
	unsigned int       id;                      /*id*/
	char			   name[128];                 /*类型名称*/

};

/************************************************************************/
/* 厂站遥信类型                                                                    */
/************************************************************************/
struct RTUYXType
{
	unsigned int    id;                      /*id*/
	char			name[128];                 /*类型名称*/

};

/************************************************************************/
/* 厂站遥脉类型                                                                    */
/************************************************************************/
struct RTUYMType
{
	unsigned int    id;                      /*id*/
	char			name[128];                 /*类型名称*/

};

/*==============================================================*/
/* Table: 厂站遥测                                          */
/*==============================================================*/
struct RTUYC
{
	unsigned int    id;                    /*id*/
	unsigned int    rtuId;                 /*设备ID*/
	char			name[256];                    /*遥测名称*/
	
	double			upLimit;                   /*越上限动作值*/
	double			upLimitReset;			/*越上限复归值*/	
	unsigned int	enableUpAlarm; /*使能越上限报警*/
	char			upAlarmVoiceText[512];			/*越上限动作告警语音文本*/
	char			upAlarmResetVoiceText[512];			/*越上限复归告警语音文本*/
	unsigned int	upAlarmPrint; /*越上限打印*/

	double			downLimit;                  /*越下限动作值*/
	double			downLimitReset;                  /*越下限复归值*/
	unsigned int	enableDownAlarm;		/*使能越下限报警*/
	char			downAlarmVoiceText[512]; /*越下限动作告警语音文本*/
	char			downAlarmResetVoiceText[512]; /*越下限复归告警语音文本*/
	unsigned int	downAlarmPrint; /*越下限打印*/

	
	unsigned int    typeId;                  /*遥测类型*/
	unsigned int    unitId;             /*单位*/

	double			factor;                     /*系数*/
	double			offset;                    /*偏移值*/
	
	unsigned int	enableInitValue; /*使能初始值*/
	double			initValue; /*初始值*/

	unsigned int    enableCalculate;               /*是否计算*/
	char			calcFormular[512];                 /*计算公式*/

	unsigned int    storage;                     /*是否保存*/

	unsigned int  saveAlarm;	//告警入库
};


/*==============================================================*/
/* 厂站遥控                                         */
/* 2017年9月12日09:46:01 在表中增加验证开关编号字段
/*==============================================================*/
struct RTUYK
{
	unsigned int        id;                          /*id*/
	unsigned int        rtuId;                      /*设备ID*/
	char				name[256];                 /*遥控名称*/

	unsigned int        type;                          /*类型*/
	unsigned int		verifySerialNumber; //验证开关编号
	char				serialNumber[256]; //开关编号

	char				remark[256]; //说明

};

/*==============================================================*/
/* 厂站遥调                                         */
/*==============================================================*/
struct RTUYT
{
	unsigned int        id;                          /*id*/
	unsigned int        rtuId;                      /*设备ID*/
	char				name[256];                 /*遥调名称*/
	unsigned int		unitId;						/*单位*/
	double				factor;                       /*命令下发系数*/

	char				remark[256]; //说明

};

/*==============================================================*/
/* 厂站遥信                                         */
/* 2017年9月12日10:14:48 增加合分报警弹窗字段 
/*==============================================================*/
struct RTUYX
{
	unsigned int		id;                         /*id*/
	unsigned int		rtuId;                  /*厂站ID*/
	char				name[256];                      /*遥信名称*/
	unsigned int		alarmPrint;                  /*变位打印*/
	unsigned int		typeId;                        /*遥信类型*/

	unsigned int		onAlarm;                   /*合报警*/
	char				onAlarmVoiceText[512]; /*合报警语音文本*/
	unsigned int        onAlarmPopupWindow;		//合报警弹窗

	unsigned int        offAlarm;                   /*分报警*/
	char				offAlarmVoiceText[512]; /*分语音报警文本*/
	unsigned int		offAlarmPopupWindow;	//分报警弹窗
	

	unsigned int        enableCalculate;            /*是否计算*/
	char				calcFormular[512];               /*计算公式*/

	unsigned int        reverse;               /*是否取反*/
	
	unsigned int        storage;                 /*是否保存实时数据*/
	
	unsigned int		enableInitValue; /*初始值开关*/
	unsigned short int		initValue; /*初始值*/
	
	unsigned int		virtualSOE; //虚拟SOE

	unsigned int  saveAlarm;	//告警入库
};


/*==============================================================*/
/* 厂站遥脉                                        */
/*==============================================================*/
struct  RTUYM
{
	unsigned int    id;                          /*id*/
	unsigned int    rtuId;                   /*设备ID*/
	char			name[256];                        /*电度量名称*/
	unsigned int    unitId;                    /*单位*/
	unsigned int    typeId;                     /*类型*/
	double			baseValue;                  /*基值*/

	unsigned int    enableMaxValue; /*使能越量程检查*/
	double			maxValue;                      /*量程*/
	unsigned int    maxAlarmPrint;          /*越量程打印*/
	char			maxAlarmVoiceText[512]; /*越量程动作语音文本*/
	char			maxAlarmResetVoiceText[512]; /*越量程复归语音文本*/

	double			factor;                   /*系数*/
	double			offset;                    /*偏移值*/
	
	unsigned int    enableCalculate;                /*是否计算*/
	char			calcFormular[512];                   /*计算公式*/

	unsigned int    storage;                   /*是否保存*/

	unsigned int  saveAlarm;	//告警入库
};


/*==============================================================*/
/* Table: 报表                                         */
/*==============================================================*/
struct RPT
{
	unsigned int        id;                     /*id*/
	char				name[256];                   /*报表名称*/
	unsigned int		rtuId; /*站ID*/
	char			savePath[512]; /*手动生成报表的保存路径*/
};


/*==============================================================*/
/* 报表keycell                                          */
/*==============================================================*/
struct  RPTKeycell
{
	unsigned int        id;                            /*id*/
	unsigned int        rptId;                        /*报表ID*/
	unsigned int		dataId;						/*数据ID*/
	unsigned int		dataType;                      /*数据类型*/
	unsigned int        row;                           /*行*/
	unsigned int        col;                      /*列*/
	unsigned int        valueType;                     /*值类型*/
	unsigned int        timePointId;                   /*时间点ID*/
	unsigned int        valueCount;                    /*数据个数*/
	unsigned int		spanYears;							/*步长:年*/
	unsigned int		spanMonths;							/*步长:月*/
	unsigned int        spanDays;                      /*步长:天*/
	unsigned int        spanHours;                     /*步长:小时*/
	unsigned int        spanMinutes;                   /*步长:分钟*/

};


/*==============================================================*/
/* 报表时间点                                        */
/*==============================================================*/
struct RPTTimePoint
{
	unsigned int    id;                  /*id*/
	char			name[128];                  /*报表时间*/
	char			formular[512];                    /*公式*/

};

/*==============================================================*/
/* Table: 用户                                                */
/*==============================================================*/
struct  User
{
	unsigned int    id;                   /*id*/
	char			name[128];                 /*用户名*/
	char			password[128];              /*密码*/
	unsigned int    groupId;              /*所属角色id*/
	
	char			department[128];            /*用户部门*/

	unsigned int deleted; /*是否被删除*/
	
};


/*==============================================================*/
/* Table: 用户组表                                            */
/*==============================================================*/
struct UserGroup
{
	unsigned  int               id;                       /*id*/
	char       name[128];                    /*角色名称*/

	//参数数据库/画面组态/主控平台/报表/历史事件曲线/计算公式/退出系统
	unsigned int paradb; 
	unsigned int picture;
	unsigned int jtview;
	unsigned int report;
	unsigned int hisdb;
	unsigned int calcFormular;
	unsigned int quitSystem;
};



/*==============================================================*/
/* Table: 报表定时任务                                            */
/*==============================================================*/
struct RPTTimerTask
{
	unsigned int	id;                       /*id*/
	unsigned int	rptId; /*所在报表id*/
	unsigned int	print;
	unsigned int	save;
	unsigned int	month;
	unsigned int	day;
	unsigned int	hour;
	unsigned int	minute;
	unsigned int	relDays; /*相对天数*/
	unsigned int	relHours;
	unsigned int	relMinutes;

	char			savePath[512]; /*生成报表的保存路径*/

};

#pragma  pack() 

typedef QVector<RTU>			VecRTU;
typedef QVector<FEPChannel>		VecFEPChannel;
typedef QVector<FEPNode>		VecFEPNode;
typedef QVector<FEPChannelProtocol> VecFEPChannelProtocol;

typedef QVector<RTUDataUnit>	VecRTUDataUnit;


typedef QVector<RTUYC>			VecRTUYC;
typedef QVector<RTUYCType>		VecRTUYCType;

typedef QVector<RTUYK>			VecRTUYK;

typedef QVector<RTUYX>			VecRTUYX;
typedef QVector<RTUYXType>		VecRTUYXType;

typedef QVector<RTUYM>			VecRTUYM;
typedef QVector<RTUYMType>		VecRTUYMType;
	
typedef QVector<RTUYT>			VecRTUYT;

typedef QVector<SCADANode>		VecSCADANode;

typedef QVector<RPT>			VecRPT;
typedef QVector<RPTKeycell>		VecRPTKeycell;
typedef QVector<RPTTimePoint>	VecRPTTimePoint;
typedef QVector<RPTTimerTask>	VecRPTTimerTask;

//typedef QVector<User>			VecUser;
//typedef QVector<UserGroup>		VecUserGroup;

typedef QVector<FEPIEC104>		VecFEPIEC104;
typedef QVector<FEPModbusTCP>	VecFEPModbusTCP;
typedef QVector<FEPWFCdtUDP>	VecFEPWFCdtUDP;
typedef QVector<FEPOtherType>	VecFEPOtherType;


/*使用命名空间,避免与其他类库重载符号相冲突*/
namespace paramdb
{
	template<class TYPE>
	inline bool operator != (const TYPE& lhs, const TYPE& rhs)
	{
		if (memcmp(&lhs, &rhs, sizeof(TYPE)) != 0)
			return true;
		return false;
	}
}



#endif




















