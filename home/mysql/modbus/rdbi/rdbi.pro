#-------------------------------------------------
#
# Project created by QtCreator 2024-11-20T09:53:07
#
#-------------------------------------------------
QT       += sql network
QT       -= gui

TARGET = rdbi
TEMPLATE = lib

DEFINES += RDBI_LIBRARY

# The following define makes your compiler emit warnings if you use
# any feature of Qt which has been marked as deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if you use deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
        rdbi.cpp \
    redisclient.cpp

HEADERS += \
        rdbi.h \
    redisclient.h

unix {
    target.path = /usr/lib
    DEPENDPATH +=.
    INSTALLS += target
    DESTDIR = /home/<USER>/qt-Linux/bin/
    INCLUDEPATH += -I /home/<USER>/qt-Linux/bin/hiredis-1.1.0
    INCLUDEPATH += -I /home/<USER>/qt-Linux/bin/common
}

LIBS += -L/home/<USER>/qt-linux/bin/linux/lib -lhiredis
LIBS += -L/home/<USER>/qt-linux/bin/linux/lib -lCLibMySql
