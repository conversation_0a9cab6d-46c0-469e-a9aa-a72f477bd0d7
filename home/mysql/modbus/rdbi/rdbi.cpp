﻿#include "rdbi.h"
#include <QDateTime>
#include <time.h>
#include <thread>
#include <string.h>
#include "qhostinfo.h"

RDBI::RDBI()
{
    CLibMySql   pSql;
    bool loadret = pSql.Load();

    if(loadret)
    {
        bool ret = pSql.SelectNodeConfig(m_listNodeConfig);

        if(!ret)
        {
            printf("SelectNodeConfig error!!!!!");
        }
    }
    else
    {
        printf("SelectNodeConfig error!!!!!");
    }

    pSql.Unload();

}

RDBI::~RDBI()
{
}

bool RDBI::Connect(const char *host, int port, int timeout)
{
    if(port < 1000)
    {
        port = 6379;
    }
    if(timeout < 10 || timeout > 50)
    {
        timeout = 20;
    }

    if (host)
    {
        m_redisClient.Connect(host, port, timeout);
        m_redisClient.m_host = host;
    }
    else
    {
        m_redisClient.Connect(LOCAL_SERVER,port,timeout);
        m_redisClient.m_host = LOCAL_SERVER;
    }

    //    mysqlInterface mysql;
    bool flag = false;

    /*
    CLibMySql   pSql;
    pSql.Load();

    bool ret = pSql.SelectNodeConfig(m_listNodeConfig);

    pSql.Unload();
*/

    //    bool ret = mysql.getNodeConfig(m_listNodeConfig);
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if( m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2 ||  m_listNodeConfig[i].node_type == 5 ||  m_listNodeConfig[i].node_type == 6)
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, port, timeout);
                    if(ret)
                    {
                        printf("Connection master ip success\n");
                        flag = true;
                    }
                    m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                    ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, port, timeout);
                    if(ret)
                    {
                        printf("Connection slave ip success\n");
                        flag = true;
                    }
                }
            }
        }
    }


    if(flag)
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool RDBI::ConnectWithpass(const char *host, int port, const char *pass, int timeout)
{
    if(port < 1000)
    {
        port = 6379;
    }
    if(timeout < 10 || timeout > 50)
    {
        timeout = 20;
    }

    if(!pass)
    {
        return false;
    }

    if (host)
    {
        m_redisClient.Connect(host, port, timeout);
        m_redisClient.m_host = host;
    }
    else
    {
        m_redisClient.Connect(LOCAL_SERVER,port,timeout);
        m_redisClient.m_host = LOCAL_SERVER;
    }

    redisReply *reply = m_redisClient.SendRedisCmd("AUTH %s",pass);
    if (!reply)
    {
        printf("password is wrong:%s\n",pass);
        m_redisClient.Disconnect();
        return false;
    }
    if (reply->type == REDIS_REPLY_ERROR)
    {
        printf("password is wrong:%s\n",pass);
        FreeReply(reply);
        m_redisClient.Disconnect();
        return false;
    }

    //    mysqlInterface mysql;
    bool flag = false;


    //    bool ret = mysql.getNodeConfig(m_listNodeConfig);
 //   if(ret)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if( m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2 ||  m_listNodeConfig[i].node_type == 5 ||  m_listNodeConfig[i].node_type == 6)
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, port, timeout);
                    if(ret)
                    {
                        printf("Connection master ip success\n");
                        flag = true;

                        redisReply *reply = m_list_A_Redis[i].SendRedisCmd("AUTH %s",pass);
                        if (!reply)
                        {
                            printf("password is wrong:%s\n",pass);
                            for(int j=0;j<i+1;j++)
                            {
                                if(m_list_A_Redis[j].IsConnected())
                                {
                                    m_list_A_Redis[j].Disconnect();
                                }
                            }

                            m_redisClient.Disconnect();
                            return false;
                        }
                        if (reply->type == REDIS_REPLY_ERROR)
                        {
                            printf("password is wrong:%s\n",pass);
                            FreeReply(reply);
                            for(int j=0;j<i+1;j++)
                            {
                                if(m_list_A_Redis[j].IsConnected())
                                {
                                    m_list_A_Redis[j].Disconnect();
                                }
                            }
                            m_redisClient.Disconnect();
                            return false;
                        }


                    }
                    m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                    ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, port, timeout);
                    if(ret)
                    {
                        printf("Connection slave ip success\n");
                        flag = true;

                        redisReply *reply = m_list_B_Redis[i].SendRedisCmd("AUTH %s",pass);
                        if (!reply)
                        {
                            printf("password is wrong:%s\n",pass);
                            for(int j=0;j<i+1;j++)
                            {
                                if(m_list_B_Redis[j].IsConnected())
                                {
                                    m_list_B_Redis[j].Disconnect();
                                }
                            }
                            m_redisClient.Disconnect();
                            return false;
                        }
                        if (reply->type == REDIS_REPLY_ERROR)
                        {
                            printf("password is wrong:%s\n",pass);
                            FreeReply(reply);
                            for(int j=0;j<i+1;j++)
                            {
                                if(m_list_B_Redis[j].IsConnected())
                                {
                                    m_list_B_Redis[j].Disconnect();
                                }
                            }
                            m_redisClient.Disconnect();
                            return false;
                        }
                    }
                }
            }

        }
    }


    if(flag)
    {
        return true;
    }
    else
    {
        return false;
    }

}

bool RDBI::IsConnected()
{
    bool ret = false;
    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(m_list_A_Redis[i].IsConnected())
                {
                    ret = true;
                    break;
                }

                if(m_list_B_Redis[i].IsConnected())
                {
                    ret = true;
                    break;
                }
            }

        }
    }
    return ret;
}

void RDBI::ConnectedState(int *state_a, int *state_b)
{
    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_list_A_Redis[i].IsConnected())
            {
                state_a[i] = 1;
            }
            else
            {
                bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1);
                if(ret)
                {
                    state_a[i] = 1;
                }
                else
                {
                    state_a[i] = 0;
                }
            }

            if(m_list_B_Redis[i].IsConnected())
            {
                state_b[i] = 1;
            }
            else
            {
                bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2);
                if(ret)
                {
                    state_b[i] = 1;
                }
                else
                {
                    state_b[i] = 0;
                }
            }
        }
    }
}

void RDBI::Disconnect()
{
    m_redisClient.Disconnect();
    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_list_A_Redis[i].IsConnected())
            {
                m_list_A_Redis[i].Disconnect();
            }
            if(m_list_B_Redis[i].IsConnected())
            {
                m_list_B_Redis[i].Disconnect();
            }
        }
    }
    m_listNodeConfig.clear();
    m_listNodeConfig.resize(0);
}

redisReply *RDBI::SendRedisCmd(const char *format, ...)
{
    void *reply = nullptr;
    bool flag = false;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            flag = false;
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
                if(!m_list_B_Redis[i].IsConnected())
                {
                    m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                    bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                    }
                }

            }
            if(m_list_A_Redis[i].IsConnected()&& (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                if(reply)
                {
                    FreeReply(reply);
                    reply = nullptr;
                }

                va_list ap;
                va_start(ap, format);
                reply = m_list_A_Redis[i].SendRedisCmd(format, ap);
                va_end(ap);
                flag = true;
            }
            if(!flag)
            {
                if(m_list_B_Redis[i].IsConnected()&& (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    if(reply)
                    {
                        FreeReply(reply);
                        reply = nullptr;
                    }

                    va_list ap;
                    va_start(ap, format);
                    reply = m_list_B_Redis[i].SendRedisCmd(format, ap);
                    va_end(ap);
                    flag = true;
                }

            }
        }
    }

    return (redisReply *)reply;
}

int RDBI::AppendCommand(const char *format, ...)
{
    int ret = 0;
    bool flag = false;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                va_list ap;
                va_start(ap, format);
                ret = m_list_A_Redis[i].AppendCommand(format, ap);
                va_end(ap);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }
                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    va_list ap;
                    va_start(ap, format);
                    ret = m_list_B_Redis[i].AppendCommand(format, ap);
                    va_end(ap);
                    flag = true;
                }
            }
        }
    }

    return ret;
}

void RDBI::FreeReply(void *reply)
{
    if (reply)
    {
        freeReplyObject(reply);
    }

    reply = NULL;
}

void RDBI::FreeReplies(int nReply)
{
    return;
    for (int i = 0; i < nReply; i++)
    {
        redisReply *reply = NULL;
        bool flag = false;

        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_A_Redis[i].IsConnected())
                    {
                        m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                        bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                        }
                    }
                }
                if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    m_list_A_Redis[i].GetReply((void **)&reply);
                    FreeReply(reply);
                    flag = true;
                    break;
                }
            }
        }

        if(!flag)
        {
            if(m_listNodeConfig.size() > 0)
            {
                for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
                {
                    if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                    {
                        if(!m_list_B_Redis[i].IsConnected())
                        {
                            m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                            bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                            if(ret)
                            {
                                printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                            }
                        }
                    }
                    if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                    {
                        m_list_A_Redis[i].GetReply((void **)&reply);
                        FreeReply(reply);
                        flag = true;
                        break;
                    }
                }
            }
        }
    }
}

bool RDBI::SendKeepAlive(int nodeId, int type, bool primary, int expireInSec)
{
    bool ret = false;

    if (type == NET_NODE_TYPE_SCADA)
    {
        redisReply* reply = SendRedisCmd("SET ex:scada:%d 1 EX %d", nodeId, expireInSec);

        if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
            ret = true;

        FreeReply(reply);
    }
    else if (type == NET_NODE_TYPE_FEP)
    {
        redisReply* reply = SendRedisCmd("SET ex:fep:%d:%d 1 EX %d", nodeId, primary? 0:1, expireInSec);

        if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
            ret = true;

        FreeReply(reply);
    }

    return ret;
}

bool RDBI::sendPing()
{

}

bool RDBI::GetCurrentNodeStatus(uint8_t &status)
{
    status = 0;

    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            QString hostName = QHostInfo::localHostName();
            if(strcmp(hostName.toStdString().c_str(),m_listNodeConfig[i].node_name)==0)
            {
                {
                    if(!m_list_A_Redis[i].IsConnected())
                    {
                        m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                        bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                        }
                    }
                }
                if(m_list_A_Redis[i].IsConnected())
                {
                    reply = m_list_A_Redis[i].SendRedisCmd("HMGET fepNode:%d status", m_listNodeConfig[i].id);
                    flag = true;
                    break;
                }
            }

        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                QString hostName = QHostInfo::localHostName();
                if(strcmp(hostName.toStdString().c_str(),m_listNodeConfig[i].node_name)==0)
                {
                    {
                        if(!m_list_B_Redis[i].IsConnected())
                        {
                            m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                            bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                            if(ret)
                            {
                                printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                            }
                        }
                    }
                    if(m_list_B_Redis[i].IsConnected())
                    {
                        reply = m_list_B_Redis[i].SendRedisCmd("HMGET fepNode:%d status", m_listNodeConfig[i].id);
                        flag = true;
                        break;
                    }
                }
            }
        }
    }


    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        if (reply->element[0]->str)
        {
            status = atoi(reply->element[0]->str);
        }

        ret = true;
    }
    FreeReply(reply);

    return ret;

}

bool RDBI::GetScadaNodeStatus(int id, uint8_t &status)
{
    bool ret = false;
    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET scada:%d status", id);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }

                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET scada:%d status", id);
                    flag = true;
                    break;
                }
            }
        }
    }


    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        status = atoi(reply->element[0]->str);
        ret = true;
    }
    FreeReply(reply);

    return ret;
}


bool RDBI::SetScadaNodeStatus(int id, uint8_t status)
{
    bool ret = false;
    redisReply* reply;

    if (status == NET_NODE_STATUS_CONNCET)
    {
        reply = SendRedisCmd("HMSET scada:%d status %d", id, status);
    }
    else
    {
        int64_t now = QDateTime::currentMSecsSinceEpoch();
        reply = SendRedisCmd("HMSET scada:%d status %d lastOnlineTime %b",
                             id, status, &now, sizeof(int64_t));
    }

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
        ret = true;

    FreeReply(reply);

    return ret;
}


bool RDBI::GetScadaNodeStatus(VecScadaNode &vecScadaNode)
{
    bool ret = true;
    int count = vecScadaNode.size();

    if(!m_redisClient.IsConnected())
    {
        m_redisClient.m_host = LOCAL_SERVER;
        bool ret = m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);

    }

    count = vecScadaNode.size();

    int i = 0;
    for (i = 0; i < count; i++)
    {
        m_redisClient.AppendCommand("HMGET scada:%d status lastOnlineTime", vecScadaNode[i].param.id);
    }

    for (i = 0; i < count; i++)
    {
        redisReply *reply = NULL;;
        m_redisClient.GetReply((void **)&reply);

        if (reply && reply->type == REDIS_REPLY_ARRAY)
        {
            if (reply->element[0]->str)
                vecScadaNode[i].status = atoi(reply->element[0]->str);
            if (reply->element[1]->str)
                vecScadaNode[i].lastOnlineTime = *(int64_t*)(reply->element[1]->str);
        }
        else
        {
            ret = false;
        }

        FreeReply(reply);
    }
    return ret;
}

bool RDBI::GetDeviceStatus(int id, uint8_t &status)
{
    bool ret = false;

    bool flag = false;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                m_list_A_Redis[i].AppendCommand("HMGET deviceid:%d status", id);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }
                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    m_list_B_Redis[i].AppendCommand("HMGET deviceid:%d status", id);
                    flag = true;
                    break;
                }
            }
        }
    }

    redisReply *reply = NULL;
    m_redisClient.GetReply((void **)&reply);
    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        if(reply->element[0]->str)
        {
            status = atoi(reply->element[0]->str);
        }
        ret = true;
    }
    FreeReply(reply);

    return ret;
}


bool RDBI::SetDeviceStatus(int id, uint8_t status)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET deviceid:%d status %d", id, status);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}


int RDBI::SetYCRealValue(int rtuId, int dataId, double realValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET yc:%d:%d realValue %b status %d updateTime %b",
                                     rtuId, dataId, &realValue, sizeof(double), status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

int RDBI::SetYCRealValue(int rtuId, int dataId, double realValue, double calValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET yc:%d:%d realValue %b calValue %b status %d updateTime %b",
                                     rtuId, dataId, &realValue, sizeof(double),&calValue, sizeof(double), status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

int RDBI::SetYCRealValue(int rtuId, int dataId, char *stringbuf,uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET yc:%d:%d realValue %s calValue %s status %d updateTime %b",
                                     rtuId, dataId, stringbuf, stringbuf, status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

int RDBI::SetYCRealValueLocal(int rtuId, int dataId, double realValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    if(!m_redisClient.IsConnected())
    {
        m_redisClient.m_host = LOCAL_SERVER;
        bool ret = m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
        if(ret)
        {
            ;
        }
    }

    redisReply* reply = m_redisClient.SendRedisCmd("HMSET yc:%d:%d realValue %b status %d updateTime %b",
                                                   rtuId, dataId, &realValue, sizeof(double), status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

int RDBI::SetYCRealValueLocal(int rtuId, int dataId, double realValue, double calValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();
    if(!m_redisClient.IsConnected())
    {
        m_redisClient.m_host = LOCAL_SERVER;
        bool ret = m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
        if(ret)
        {
            ;
        }
    }

    redisReply* reply = m_redisClient.SendRedisCmd("HMSET yc:%d:%d realValue %b calValue %b status %d updateTime %b",
                                                   rtuId, dataId, &realValue, sizeof(double),&calValue, sizeof(double), status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

int RDBI::SetYCRealValueLocal(int rtuId, int dataId, char *stringbuf, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();
    if(!m_redisClient.IsConnected())
    {
        m_redisClient.m_host = LOCAL_SERVER;
        bool ret = m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
        if(ret)
        {
            ;
        }
    }

    redisReply* reply = m_redisClient.SendRedisCmd("HMSET yc:%d:%d realValue %s calValue %s status %d updateTime %b",
                                                   rtuId, dataId, stringbuf, stringbuf, status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetYCManualValue(int rtuId, int dataId, double manualValue)
{
    bool ret = false;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET yc:%d:%d status 1 manualValue %b manualFlag 1 manualTime %b",
                                     rtuId, dataId, &manualValue, sizeof(double), &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK")==0)
    {
        ret = true;
    }

    if(reply)
    {
        FreeReply(reply);
    }

    return ret;
}

bool RDBI::GetYCRealValue(int rtuId, int dataId, char *stringbuf, int buflen, uint8_t &status)
{
    bool ret = false;

    if(buflen < 2 || !stringbuf)
    {
        return false;
    }

    redisReply *reply = NULL;
    bool flag = false;
    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET yc:%d:%d realValue status", rtuId, dataId);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }
                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET yc:%d:%d realValue status", rtuId, dataId);
                    flag = true;
                    break;
                }
            }
        }
    }

    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {

        if (reply->element[0]->str)
        {
            strncpy(stringbuf,reply->element[0]->str,buflen);
        }

        if (reply->element[1]->str)
        {
            status = atoi(reply->element[1]->str);
        }

        ret = true;
    }

    if(reply)
    {
        FreeReply(reply);
    }

    return ret;

}

bool RDBI::GetYCRealValue(int rtuId, int dataId, double &realValue, uint8_t &status)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET yc:%d:%d realValue manualValue manualFlag status", rtuId, dataId);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }
                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET yc:%d:%d realValue manualValue manualFlag status", rtuId, dataId);
                    flag = true;
                    break;
                }
            }
        }
    }

    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        double _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            //不是double，返回false
            if(reply->element[0]->len == sizeof(double))
            {
                realValue = *(double*)(reply->element[0]->str);
            }
            else
            {
                FreeReply(reply);
                return false;
            }
        }
        if (reply->element[1]->str)
        {
            _manualValue = *(double*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _flag = atoi(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            status = atoi(reply->element[3]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    if(reply)
    {
        FreeReply(reply);
    }

    return ret;
}

bool RDBI::GetYCRealValue(int rtuId, int dataId, double &realValue, double &calValue, uint8_t &status)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET yc:%d:%d realValue calValue manualValue manualFlag status", rtuId, dataId);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }
                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET yc:%d:%d realValue calValue manualValue manualFlag status", rtuId, dataId);
                    flag = true;
                    break;
                }
            }
        }
    }

    if(!flag)
    {
        return false;
    }

    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        double _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            //不是double，返回false
            if(reply->element[0]->len == sizeof(double))
            {
                realValue = *(double*)(reply->element[0]->str);
            }
            else
            {
                FreeReply(reply);
                return false;
            }

        }
        if (reply->element[1]->str)
        {
            calValue = *(double*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _manualValue = *(double*)(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            _flag = atoi(reply->element[3]->str);
        }
        if (reply->element[4]->str)
        {
            status = atoi(reply->element[4]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    if(reply)
    {
        FreeReply(reply);
    }

    return ret;
}

bool RDBI::GetYCRealValueLocal(int rtuId, int dataId, char *stringbuf, int buflen, uint8_t &status)
{
    bool ret = false;

    if(buflen < 2 || !stringbuf)
    {
        return false;
    }

    redisReply *reply = NULL;

    if(!m_redisClient.IsConnected())
    {
        m_redisClient.m_host = LOCAL_SERVER;
        bool ret = m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
        if(ret)
        {
            ;
        }
    }

    reply = m_redisClient.SendRedisCmd("HMGET yc:%d:%d realValue status", rtuId, dataId);

    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {

        if (reply->element[0]->str)
        {
            strncpy(stringbuf,reply->element[0]->str,buflen);
        }

        if (reply->element[1]->str)
        {
            status = atoi(reply->element[1]->str);
        }

        ret = true;
    }

    if(reply)
    {
        FreeReply(reply);
    }

    return ret;
}

bool RDBI::GetYCRealValueLocal(int rtuId, int dataId, double &realValue, uint8_t &status)
{
    bool ret = false;

    redisReply *reply = NULL;
    if(!m_redisClient.IsConnected())
    {
        m_redisClient.m_host = LOCAL_SERVER;
        bool ret = m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
        if(ret)
        {
            ;
        }
    }

    reply = m_redisClient.SendRedisCmd("HMGET yc:%d:%d realValue manualValue manualFlag status", rtuId, dataId);

    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        double _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            //不是double，返回false
            if(reply->element[0]->len == sizeof(double))
            {
                realValue = *(double*)(reply->element[0]->str);
            }
            else
            {
                FreeReply(reply);
                return false;
            }
        }
        if (reply->element[1]->str)
        {
            _manualValue = *(double*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _flag = atoi(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            status = atoi(reply->element[3]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    if(reply)
    {
        FreeReply(reply);
    }

    return ret;
}

bool RDBI::GetYCRealValueLocal(int rtuId, int dataId, double &realValue, double &calValue, uint8_t &status)
{
    bool ret = false;

    redisReply *reply = NULL;

    if(!m_redisClient.IsConnected())
    {
        m_redisClient.m_host = LOCAL_SERVER;
        bool ret = m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
        if(ret)
        {
            ;
        }
    }

    reply = m_redisClient.SendRedisCmd("HMGET yc:%d:%d realValue calValue manualValue manualFlag status", rtuId, dataId);

    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        double _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            //不是double，返回false
            if(reply->element[0]->len == sizeof(double))
            {
                realValue = *(double*)(reply->element[0]->str);
            }
            else
            {
                FreeReply(reply);
                return false;
            }

        }
        if (reply->element[1]->str)
        {
            calValue = *(double*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _manualValue = *(double*)(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            _flag = atoi(reply->element[3]->str);
        }
        if (reply->element[4]->str)
        {
            status = atoi(reply->element[4]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    if(reply)
    {
        FreeReply(reply);
    }

    return ret;
}

bool RDBI::SetYCStatus(int rtuId, int dataId, uint8_t status)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET yc:%d:%d status %d", rtuId, dataId, status);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetYCNoAlarm(int rtuId, int dataId, uint8_t enable)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET yc:%d:%d noAlarm %d", rtuId, dataId, enable);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::ResetYCManualFlag(int rtuId, int dataId, uint8_t status)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET yc:%d:%d status %d manualFlag 0", rtuId, dataId, status);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetYCValues(QVector<T_Analog> &vecYC)
{
    bool ret = true;

    int count = vecYC.size();

    int i = 0;
    for (i = 0; i<count; i++)
    {
        bool flag = false;
        if(m_listNodeConfig.size() > 0)
        {
            for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
            {
                if(m_list_A_Redis[j].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    m_list_A_Redis[j].AppendCommand("HMGET yc:%d:%d realValue manualValue manualFlag manualTime status updateTime maxValue maxValueTime minValue minValueTime overUplimitNum overUplimitFlag overUplimitTime overDownlimitNum overDownlimitFlag overDownlimitTime noAlarm",
                                                    vecYC[i].param.DeviceID, vecYC[i].param.DeviceIndex);
                    flag = true;
                    redisReply *reply = NULL;
                    m_list_A_Redis[j].GetReply((void **)&reply);

                    if (reply && reply->type == REDIS_REPLY_ARRAY)
                    {
                        if (reply->element[0]->str)
                            vecYC[i].data.realValue = *(double*)(reply->element[0]->str);
                        if (reply->element[1]->str)
                            vecYC[i].data.manualValue = *(double*)(reply->element[1]->str);
                        if (reply->element[2]->str)
                            vecYC[i].data.manualFlag = atoi(reply->element[2]->str);
                        if (reply->element[3]->str)
                            vecYC[i].data.manualTime = *(int64_t*)(reply->element[3]->str);
                        if (reply->element[4]->str)
                            vecYC[i].data.status = atoi(reply->element[4]->str);
                        if (reply->element[5]->str)
                            vecYC[i].data.updateTime = *(int64_t*)(reply->element[5]->str);
                        if (reply->element[6]->str)
                            vecYC[i].data.maxValue = *(double*)(reply->element[6]->str);
                        if (reply->element[7]->str)
                            vecYC[i].data.maxValueTime = *(int64_t*)(reply->element[7]->str);
                        if (reply->element[8]->str)
                            vecYC[i].data.minValue = *(double*)(reply->element[8]->str);
                        if (reply->element[9]->str)
                            vecYC[i].data.minValueTime = *(int64_t*)(reply->element[9]->str);
                        if (reply->element[10]->str)
                            vecYC[i].data.overUplimitNum = atoi(reply->element[10]->str);
                        if (reply->element[11]->str)
                            vecYC[i].data.overUplimitFlag = atoi(reply->element[11]->str);
                        if (reply->element[12]->str)
                            vecYC[i].data.overUplimitTime = *(int64_t*)(reply->element[12]->str);
                        if (reply->element[13]->str)
                            vecYC[i].data.overDownlimitNum = atoi(reply->element[13]->str);
                        if (reply->element[14]->str)
                            vecYC[i].data.overDownlimitFlag = atoi(reply->element[14]->str);
                        if (reply->element[15]->str)
                            vecYC[i].data.overDownlimitTime = *(int64_t*)(reply->element[15]->str);
                        if (reply->element[16]->str)
                            vecYC[i].data.noAlarm = atoi(reply->element[16]->str);
                    }
                    else
                    {
                        ret = false;
                    }

                    FreeReply(reply);
                    break;
                }
            }
        }

        if(!flag)
        {
            if(m_listNodeConfig.size() > 0)
            {
                for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                {
                    if(m_list_B_Redis[j].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                    {
                        m_list_B_Redis[j].AppendCommand("HMGET yc:%d:%d realValue manualValue manualFlag manualTime status updateTime maxValue maxValueTime minValue minValueTime overUplimitNum overUplimitFlag overUplimitTime overDownlimitNum overDownlimitFlag overDownlimitTime noAlarm",
                                                        vecYC[i].param.DeviceID, vecYC[i].param.DeviceIndex);
                        flag = true;
                        redisReply *reply = NULL;
                        m_list_B_Redis[j].GetReply((void **)&reply);

                        if (reply && reply->type == REDIS_REPLY_ARRAY)
                        {
                            if (reply->element[0]->str)
                                vecYC[i].data.realValue = *(double*)(reply->element[0]->str);
                            if (reply->element[1]->str)
                                vecYC[i].data.manualValue = *(double*)(reply->element[1]->str);
                            if (reply->element[2]->str)
                                vecYC[i].data.manualFlag = atoi(reply->element[2]->str);
                            if (reply->element[3]->str)
                                vecYC[i].data.manualTime = *(int64_t*)(reply->element[3]->str);
                            if (reply->element[4]->str)
                                vecYC[i].data.status = atoi(reply->element[4]->str);
                            if (reply->element[5]->str)
                                vecYC[i].data.updateTime = *(int64_t*)(reply->element[5]->str);
                            if (reply->element[6]->str)
                                vecYC[i].data.maxValue = *(double*)(reply->element[6]->str);
                            if (reply->element[7]->str)
                                vecYC[i].data.maxValueTime = *(int64_t*)(reply->element[7]->str);
                            if (reply->element[8]->str)
                                vecYC[i].data.minValue = *(double*)(reply->element[8]->str);
                            if (reply->element[9]->str)
                                vecYC[i].data.minValueTime = *(int64_t*)(reply->element[9]->str);
                            if (reply->element[10]->str)
                                vecYC[i].data.overUplimitNum = atoi(reply->element[10]->str);
                            if (reply->element[11]->str)
                                vecYC[i].data.overUplimitFlag = atoi(reply->element[11]->str);
                            if (reply->element[12]->str)
                                vecYC[i].data.overUplimitTime = *(int64_t*)(reply->element[12]->str);
                            if (reply->element[13]->str)
                                vecYC[i].data.overDownlimitNum = atoi(reply->element[13]->str);
                            if (reply->element[14]->str)
                                vecYC[i].data.overDownlimitFlag = atoi(reply->element[14]->str);
                            if (reply->element[15]->str)
                                vecYC[i].data.overDownlimitTime = *(int64_t*)(reply->element[15]->str);
                            if (reply->element[16]->str)
                                vecYC[i].data.noAlarm = atoi(reply->element[16]->str);
                        }
                        else
                        {
                            ret = false;
                        }

                        FreeReply(reply);
                        break;
                    }
                }
            }
        }

    }

    return ret;
}

bool RDBI::InvalidateYCData(QVector<T_Analog> &vecYC)
{
    int count = vecYC.size();
    bool ret = false;

    int type[2] = {1,2};
    for(int k=0;k<2;k++)
    {
        bool flag = false;
        for (int i = 0; i<count; i++)
        {
            flag = false;
            if(m_listNodeConfig.size() > 0)
            {
                for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                {
                    if(m_list_A_Redis[j].IsConnected() && m_listNodeConfig[j].node_type == type[k])
                    {
                        redisReply* reply = m_list_A_Redis[j].SendRedisCmd("HMSET yc:%d:%d status %d", vecYC[i].param.DeviceID, vecYC[i].param.DeviceIndex, DATA_STATUS_INVALID);
                        if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
                        {
                            ret = true;
                        }

                        FreeReply(reply);

                    }
                }
            }

            if(!flag)
            {
                if(m_listNodeConfig.size() > 0)
                {
                    for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                    {
                        if(m_list_B_Redis[j].IsConnected() && m_listNodeConfig[j].node_type == type[k])
                        {
                            redisReply* reply = m_list_B_Redis[j].SendRedisCmd("HMSET yc:%d:%d status %d", vecYC[i].param.DeviceID, vecYC[i].param.DeviceIndex, DATA_STATUS_INVALID);
                            if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
                            {
                                ret = true;
                            }

                            FreeReply(reply);
                        }
                    }
                }
            }
        }
    }

    return ret;
}

int RDBI::SetXBRealValue(int rtuId, int dataId, double realValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET xb:%d:%d realValue %b status %d updateTime %b",
                                     rtuId, dataId, &realValue, sizeof(double), status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

int RDBI::SetXBRealValue(int rtuId, int dataId, double realValue, double calValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET xb:%d:%d realValue %b calValue %b status %d updateTime %b",
                                     rtuId, dataId, &realValue, sizeof(double),&calValue, sizeof(double), status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetXBManualValue(int rtuId, int dataId, double manualValue)
{
    bool ret = false;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET xb:%d:%d status 1 manualValue %b manualFlag 1 manualTime %b",
                                     rtuId, dataId, &manualValue, sizeof(double), &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK")==0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetXBRealValue(int rtuId, int dataId, double &realValue, uint8_t &status)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET xb:%d:%d realValue manualValue manualFlag status", rtuId, dataId);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET xb:%d:%d realValue manualValue manualFlag status", rtuId, dataId);
                    flag = true;
                    break;
                }
            }
        }
    }

    if(!flag)
    {
        return false;
    }

    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        double _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            realValue = *(double*)(reply->element[0]->str);
        }
        if (reply->element[1]->str)
        {
            _manualValue = *(double*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _flag = atoi(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            status = atoi(reply->element[3]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetXBRealValue(int rtuId, int dataId, double &realValue, double &calValue, uint8_t &status)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET xb:%d:%d realValue calValue manualValue manualFlag status", rtuId, dataId);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET xb:%d:%d realValue calValue manualValue manualFlag status", rtuId, dataId);
                    flag = true;
                    break;
                }
            }
        }
    }

    if(!flag)
    {
        return false;
    }

    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        double _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            realValue = *(double*)(reply->element[0]->str);
        }
        if (reply->element[1]->str)
        {
            calValue = *(double*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _manualValue = *(double*)(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            _flag = atoi(reply->element[3]->str);
        }
        if (reply->element[4]->str)
        {
            status = atoi(reply->element[4]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetXBStatus(int rtuId, int dataId, uint8_t status)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET xb:%d:%d status %d", rtuId, dataId, status);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetXBNoAlarm(int rtuId, int dataId, uint8_t enable)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET xb:%d:%d noAlarm %d", rtuId, dataId, enable);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::ResetXBManualFlag(int rtuId, int dataId, uint8_t status)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET xb:%d:%d status %d manualFlag 0", rtuId, dataId, status);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetXBValues(QVector<T_SignalXB> &vecXB)
{
    bool ret = true;

    int count = vecXB.size();

    int i = 0;
    for (i = 0; i<count; i++)
    {
        bool flag = false;
        if(m_listNodeConfig.size() > 0)
        {
            for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
            {
                if(m_list_A_Redis[j].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    m_list_A_Redis[j].AppendCommand("HMGET xb:%d:%d realValue manualValue manualFlag manualTime status updateTime maxValue maxValueTime minValue minValueTime overUplimitNum overUplimitFlag overUplimitTime overDownlimitNum overDownlimitFlag overDownlimitTime noAlarm",
                                                    vecXB[i].param.DeviceID, vecXB[i].param.DeviceIndex);
                    flag = true;

                    redisReply *reply = NULL;
                    m_list_A_Redis[j].GetReply((void **)&reply);

                    if (reply && reply->type == REDIS_REPLY_ARRAY)
                    {
                        if (reply->element[0]->str)
                            vecXB[i].data.realValue = *(double*)(reply->element[0]->str);
                        if (reply->element[1]->str)
                            vecXB[i].data.manualValue = *(double*)(reply->element[1]->str);
                        if (reply->element[2]->str)
                            vecXB[i].data.manualFlag = atoi(reply->element[2]->str);
                        if (reply->element[3]->str)
                            vecXB[i].data.manualTime = *(int64_t*)(reply->element[3]->str);
                        if (reply->element[4]->str)
                            vecXB[i].data.status = atoi(reply->element[4]->str);
                        if (reply->element[5]->str)
                            vecXB[i].data.updateTime = *(int64_t*)(reply->element[5]->str);
                        if (reply->element[6]->str)
                            vecXB[i].data.maxValue = *(double*)(reply->element[6]->str);
                        if (reply->element[7]->str)
                            vecXB[i].data.maxValueTime = *(int64_t*)(reply->element[7]->str);
                        if (reply->element[8]->str)
                            vecXB[i].data.minValue = *(double*)(reply->element[8]->str);
                        if (reply->element[9]->str)
                            vecXB[i].data.minValueTime = *(int64_t*)(reply->element[9]->str);
                        if (reply->element[10]->str)
                            vecXB[i].data.overUplimitNum = atoi(reply->element[10]->str);
                        if (reply->element[11]->str)
                            vecXB[i].data.overUplimitFlag = atoi(reply->element[11]->str);
                        if (reply->element[12]->str)
                            vecXB[i].data.overUplimitTime = *(int64_t*)(reply->element[12]->str);
                        if (reply->element[13]->str)
                            vecXB[i].data.overDownlimitNum = atoi(reply->element[13]->str);
                        if (reply->element[14]->str)
                            vecXB[i].data.overDownlimitFlag = atoi(reply->element[14]->str);
                        if (reply->element[15]->str)
                            vecXB[i].data.overDownlimitTime = *(int64_t*)(reply->element[15]->str);
                        if (reply->element[16]->str)
                            vecXB[i].data.noAlarm = atoi(reply->element[16]->str);
                    }
                    else
                    {
                        ret = false;
                    }

                    FreeReply(reply);

                    break;
                }
            }
        }

        if(!flag)
        {
            if(m_listNodeConfig.size() > 0)
            {
                for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                {
                    if(m_list_B_Redis[j].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                    {
                        m_list_B_Redis[j].AppendCommand("HMGET xb:%d:%d realValue manualValue manualFlag manualTime status updateTime maxValue maxValueTime minValue minValueTime overUplimitNum overUplimitFlag overUplimitTime overDownlimitNum overDownlimitFlag overDownlimitTime noAlarm",
                                                        vecXB[i].param.DeviceID, vecXB[i].param.DeviceIndex);
                        flag = true;

                        redisReply *reply = NULL;
                        m_list_B_Redis[j].GetReply((void **)&reply);

                        if (reply && reply->type == REDIS_REPLY_ARRAY)
                        {
                            if (reply->element[0]->str)
                                vecXB[i].data.realValue = *(double*)(reply->element[0]->str);
                            if (reply->element[1]->str)
                                vecXB[i].data.manualValue = *(double*)(reply->element[1]->str);
                            if (reply->element[2]->str)
                                vecXB[i].data.manualFlag = atoi(reply->element[2]->str);
                            if (reply->element[3]->str)
                                vecXB[i].data.manualTime = *(int64_t*)(reply->element[3]->str);
                            if (reply->element[4]->str)
                                vecXB[i].data.status = atoi(reply->element[4]->str);
                            if (reply->element[5]->str)
                                vecXB[i].data.updateTime = *(int64_t*)(reply->element[5]->str);
                            if (reply->element[6]->str)
                                vecXB[i].data.maxValue = *(double*)(reply->element[6]->str);
                            if (reply->element[7]->str)
                                vecXB[i].data.maxValueTime = *(int64_t*)(reply->element[7]->str);
                            if (reply->element[8]->str)
                                vecXB[i].data.minValue = *(double*)(reply->element[8]->str);
                            if (reply->element[9]->str)
                                vecXB[i].data.minValueTime = *(int64_t*)(reply->element[9]->str);
                            if (reply->element[10]->str)
                                vecXB[i].data.overUplimitNum = atoi(reply->element[10]->str);
                            if (reply->element[11]->str)
                                vecXB[i].data.overUplimitFlag = atoi(reply->element[11]->str);
                            if (reply->element[12]->str)
                                vecXB[i].data.overUplimitTime = *(int64_t*)(reply->element[12]->str);
                            if (reply->element[13]->str)
                                vecXB[i].data.overDownlimitNum = atoi(reply->element[13]->str);
                            if (reply->element[14]->str)
                                vecXB[i].data.overDownlimitFlag = atoi(reply->element[14]->str);
                            if (reply->element[15]->str)
                                vecXB[i].data.overDownlimitTime = *(int64_t*)(reply->element[15]->str);
                            if (reply->element[16]->str)
                                vecXB[i].data.noAlarm = atoi(reply->element[16]->str);
                        }
                        else
                        {
                            ret = false;
                        }

                        FreeReply(reply);

                        break;
                    }
                }
            }
        }

    }

    return ret;

}

bool RDBI::InvalidateXBData(QVector<T_SignalXB> &vecXB)
{
    int count = vecXB.size();

    int type[2] = {1,2};
    for(int k=0;k<2;k++)
    {
        bool flag = false;
        for (int i = 0; i<count; i++)
        {
            flag = false;
            if(m_listNodeConfig.size() > 0)
            {
                for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                {
                    if(m_list_A_Redis[j].IsConnected() && m_listNodeConfig[j].node_type == type[k])
                    {
                        m_list_A_Redis[j].AppendCommand("HMSET xb:%d:%d status %d", vecXB[i].param.DeviceID, vecXB[i].param.DeviceIndex, DATA_STATUS_INVALID);
                        flag = true;
                        break;
                    }
                }
            }

            if(!flag)
            {
                if(m_listNodeConfig.size() > 0)
                {
                    for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                    {
                        if(m_list_B_Redis[j].IsConnected() && m_listNodeConfig[j].node_type == type[k])
                        {
                            m_list_B_Redis[j].AppendCommand("HMSET xb:%d:%d status %d", vecXB[i].param.DeviceID, vecXB[i].param.DeviceIndex, DATA_STATUS_INVALID);
                            flag = true;
                            break;
                        }
                    }
                }
            }
        }

        if(flag)
        {
            for (int i = 0; i < count; i++)
            {
                redisReply *reply = NULL;
                bool replyflag = false;

                if(m_listNodeConfig.size() > 0)
                {
                    for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
                    {
                        if(m_list_A_Redis[i].IsConnected() && m_listNodeConfig[i].node_type == type[k])
                        {
                            m_list_A_Redis[i].GetReply((void **)&reply);
                            FreeReply(reply);
                            replyflag = true;
                        }
                    }
                }

                if(!replyflag)
                {
                    if(m_listNodeConfig.size() > 0)
                    {
                        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
                        {
                            if(m_list_B_Redis[i].IsConnected() && m_listNodeConfig[i].node_type == type[k])
                            {
                                m_list_A_Redis[i].GetReply((void **)&reply);
                                FreeReply(reply);
                            }
                        }
                    }
                }
            }
        }
    }

    return true;
}

int RDBI::SetYXRealValue(int rtuId, int dataId, short realValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET yx:%d:%d realValue %b status %d updateTime %b",
                                     rtuId, dataId, &realValue, sizeof(short), status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

int RDBI::SetYXRealValue(int rtuId, int dataId, short realValue, short calValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET yx:%d:%d realValue %b calValue %b status %d updateTime %b",
                                     rtuId, dataId, &realValue, sizeof(short),&calValue, sizeof(short), status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

int RDBI::SetYXRealValueLocal(int rtuId, int dataId, short realValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    if(!m_redisClient.IsConnected())
    {
        m_redisClient.m_host = LOCAL_SERVER;
        bool ret = m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
        if(ret)
        {
            ;
        }
    }

    redisReply* reply = m_redisClient.SendRedisCmd("HMSET yx:%d:%d realValue %b status %d updateTime %b",
                                                   rtuId, dataId, &realValue, sizeof(short), status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

int RDBI::SetYXRealValueLocal(int rtuId, int dataId, short realValue, short calValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    if(!m_redisClient.IsConnected())
    {
        m_redisClient.m_host = LOCAL_SERVER;
        bool ret = m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
        if(ret)
        {
            ;
        }
    }

    redisReply* reply = m_redisClient.SendRedisCmd("HMSET yx:%d:%d realValue %b calValue %b status %d updateTime %b",
                                                   rtuId, dataId, &realValue, sizeof(short),&calValue, sizeof(short), status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetYXManualValue(int rtuId, int dataId, short manualValue, uint8_t enable)
{
    bool ret = false;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET yx:%d:%d status 1 manualValue %b manualFlag 1 manualTime %b",
                                     rtuId, dataId, &manualValue, sizeof(short), &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetYXRealValue(int rtuId, int dataId, short &realValue, uint8_t &status)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET yx:%d:%d realValue manualValue manualFlag status", rtuId, dataId);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }
                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET yx:%d:%d realValue manualValue manualFlag status", rtuId, dataId);
                    flag = true;
                    break;
                }
            }
        }
    }


    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        short _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            realValue = *(short*)(reply->element[0]->str);
        }
        if (reply->element[1]->str)
        {
            _manualValue = *(short*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _flag = atoi(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            status = atoi(reply->element[3]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetYXRealValue(int rtuId, int dataId, short &realValue, short &calValue, uint8_t &status)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET yx:%d:%d realValue calValue manualValue manualFlag status", rtuId, dataId);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }
                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET yx:%d:%d realValue calValue manualValue manualFlag status", rtuId, dataId);
                    flag = true;
                    break;
                }
            }
        }
    }


    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        short _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            realValue = *(short*)(reply->element[0]->str);
        }
        if (reply->element[1]->str)
        {
            calValue = *(short*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _manualValue = *(short*)(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            _flag = atoi(reply->element[3]->str);
        }
        if (reply->element[4]->str)
        {
            status = atoi(reply->element[4]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetYXRealValueLocal(int rtuId, int dataId, short &realValue, uint8_t &status)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(!m_redisClient.IsConnected())
    {
        m_redisClient.m_host = LOCAL_SERVER;
        bool ret = m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
        if(ret)
        {
            ;
        }
    }

    reply = m_redisClient.SendRedisCmd("HMGET yx:%d:%d realValue manualValue manualFlag status", rtuId, dataId);

    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        short _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            realValue = *(short*)(reply->element[0]->str);
        }
        if (reply->element[1]->str)
        {
            _manualValue = *(short*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _flag = atoi(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            status = atoi(reply->element[3]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetYXRealValueLocal(int rtuId, int dataId, short &realValue, short &calValue, uint8_t &status)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(!m_redisClient.IsConnected())
    {
        m_redisClient.m_host = LOCAL_SERVER;
        bool ret = m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
        if(ret)
        {
            ;
        }
    }

    reply = m_redisClient.SendRedisCmd("HMGET yx:%d:%d realValue calValue manualValue manualFlag status", rtuId, dataId);

    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        short _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            realValue = *(short*)(reply->element[0]->str);
        }
        if (reply->element[1]->str)
        {
            calValue = *(short*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _manualValue = *(short*)(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            _flag = atoi(reply->element[3]->str);
        }
        if (reply->element[4]->str)
        {
            status = atoi(reply->element[4]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetYXStatus(int rtuId, int dataId, uint8_t status)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET yx:%d:%d status %d", rtuId, dataId, status);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetYXNoAlarm(int rtuId, int dataId, uint8_t enable)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET yx:%d:%d noAlarm %d", rtuId, dataId, enable);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::ResetYXManualFlag(int rtuId, int dataId, uint8_t status)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET yx:%d:%d status %d manualFlag 0", rtuId, dataId, status);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetFiveAntiLock(int rtuId, int dataId, uint8_t enable)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET yx:%d:%d fiveAntilock %d",
                                     rtuId, dataId, enable);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetHangup(int rtuId, int dataId, uint8_t enable)
{
    bool ret = false;

    if (enable)
    {
        int64_t now = QDateTime::currentMSecsSinceEpoch();

        redisReply* reply = SendRedisCmd("HMSET yx:%d:%d hangup %d hangupTime %b",
                                         rtuId, dataId, enable, &now, sizeof(int64_t));

        if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
        {
            ret = true;
        }

        FreeReply(reply);
    }
    else
    {
        redisReply* reply = SendRedisCmd("HMSET yx:%d:%d hangup %d",
                                         rtuId, dataId, enable);

        if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
        {
            ret = true;
        }

        FreeReply(reply);
    }

    return ret;

}

bool RDBI::GetYXValues(QVector<T_Digital> &vecYX)
{
    bool ret = true;

    int count = vecYX.size();
    int i = 0;
    for (i = 0; i<count; i++)
    {
        bool flag = false;
        if(m_listNodeConfig.size() > 0)
        {
            for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
            {
                if(m_list_A_Redis[j].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    m_list_A_Redis[j].AppendCommand("HMGET yx:%d:%d realValue manualValue manualFlag manualTime status fiveAntilock hangup hangupTime updateTime alarmTime onAlarmNum offAlarmNum noAlarm",
                                                    vecYX[i].param.DeviceID, vecYX[i].param.DeviceIndex);
                    flag = true;

                    redisReply *reply = NULL;;
                    m_list_A_Redis[j].GetReply((void **)&reply);

                    if (reply && reply->type == REDIS_REPLY_ARRAY)
                    {
                        if (reply->element[0]->str)
                            vecYX[i].data.realValue = *(short*)(reply->element[0]->str);
                        if (reply->element[1]->str)
                            vecYX[i].data.manualValue = *(short*)(reply->element[1]->str);
                        if (reply->element[2]->str)
                            vecYX[i].data.manualFlag = atoi(reply->element[2]->str);
                        if (reply->element[3]->str)
                            vecYX[i].data.manualTime = *(int64_t*)(reply->element[3]->str);
                        if (reply->element[4]->str)
                            vecYX[i].data.status = atoi(reply->element[4]->str);

                        if (reply->element[5]->str)
                            vecYX[i].data.fiveAntilock = atoi(reply->element[5]->str);
                        if (reply->element[6]->str)
                            vecYX[i].data.hangup = atoi(reply->element[6]->str);
                        if (reply->element[7]->str)
                            vecYX[i].data.hangupTime = *(int64_t*)(reply->element[7]->str);
                        if (reply->element[8]->str)
                            vecYX[i].data.updateTime = *(int64_t*)(reply->element[8]->str);
                        if (reply->element[9]->str)
                            vecYX[i].data.alarmTime = *(int64_t*)(reply->element[9]->str);
                        if (reply->element[10]->str)
                            vecYX[i].data.onAlarmNum = atoi(reply->element[10]->str);
                        if (reply->element[11]->str)
                            vecYX[i].data.offAlarmNum = atoi(reply->element[11]->str);
                        if (reply->element[12]->str)
                            vecYX[i].data.noAlarm = atoi(reply->element[12]->str);
                    }
                    else
                    {
                        ret = false;
                    }

                    FreeReply(reply);

                    break;
                }
            }
        }

        if(!flag)
        {
            if(m_listNodeConfig.size() > 0)
            {
                for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                {
                    if(m_list_B_Redis[j].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                    {
                        m_list_B_Redis[j].AppendCommand("HMGET yx:%d:%d realValue manualValue manualFlag manualTime status fiveAntilock hangup hangupTime updateTime alarmTime onAlarmNum offAlarmNum noAlarm",
                                                        vecYX[i].param.DeviceID, vecYX[i].param.DeviceIndex);
                        flag = true;
                        redisReply *reply = NULL;;
                        m_list_B_Redis[j].GetReply((void **)&reply);

                        if (reply && reply->type == REDIS_REPLY_ARRAY)
                        {
                            if (reply->element[0]->str)
                                vecYX[i].data.realValue = *(short*)(reply->element[0]->str);
                            if (reply->element[1]->str)
                                vecYX[i].data.manualValue = *(short*)(reply->element[1]->str);
                            if (reply->element[2]->str)
                                vecYX[i].data.manualFlag = atoi(reply->element[2]->str);
                            if (reply->element[3]->str)
                                vecYX[i].data.manualTime = *(int64_t*)(reply->element[3]->str);
                            if (reply->element[4]->str)
                                vecYX[i].data.status = atoi(reply->element[4]->str);

                            if (reply->element[5]->str)
                                vecYX[i].data.fiveAntilock = atoi(reply->element[5]->str);
                            if (reply->element[6]->str)
                                vecYX[i].data.hangup = atoi(reply->element[6]->str);
                            if (reply->element[7]->str)
                                vecYX[i].data.hangupTime = *(int64_t*)(reply->element[7]->str);
                            if (reply->element[8]->str)
                                vecYX[i].data.updateTime = *(int64_t*)(reply->element[8]->str);
                            if (reply->element[9]->str)
                                vecYX[i].data.alarmTime = *(int64_t*)(reply->element[9]->str);
                            if (reply->element[10]->str)
                                vecYX[i].data.onAlarmNum = atoi(reply->element[10]->str);
                            if (reply->element[11]->str)
                                vecYX[i].data.offAlarmNum = atoi(reply->element[11]->str);
                            if (reply->element[12]->str)
                                vecYX[i].data.noAlarm = atoi(reply->element[12]->str);
                        }
                        else
                        {
                            ret = false;
                        }

                        FreeReply(reply);
                        break;
                    }
                }
            }
        }
    }

    return ret;

}

bool RDBI::InvalidateYXData(QVector<T_Digital> &vecYX)
{
    bool ret = false;
    int count = vecYX.size();
    int type[2] = {1,2};
    for(int k=0;k<2;k++)
    {
        bool flag = false;
        for (int i = 0; i<count; i++)
        {
            flag = false;
            if(m_listNodeConfig.size() > 0)
            {
                for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                {
                    if(m_listNodeConfig[j].node_type == type[k])
                    {
                        if(!m_list_A_Redis[j].IsConnected())
                        {
                            m_list_A_Redis[j].m_host = m_listNodeConfig[j].node_ip1;
                            bool ret = m_list_A_Redis[j].Connect(m_listNodeConfig[j].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                            if(ret)
                            {
                                printf("Connection master ip success:%s\n",m_listNodeConfig[j].node_ip1);
                            }
                        }
                    }
                    if(m_list_A_Redis[j].IsConnected() && m_listNodeConfig[j].node_type == type[k])
                    {
                        redisReply* reply = m_list_A_Redis[j].SendRedisCmd("HMSET yx:%d:%d status %d", vecYX[i].param.DeviceID, vecYX[i].param.DeviceIndex, DATA_STATUS_INVALID);
                        if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
                        {
                            ret = true;
                        }
                        FreeReply(reply);

                    }
                }
            }

            if(!flag)
            {
                if(m_listNodeConfig.size() > 0)
                {
                    for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                    {
                        if(m_listNodeConfig[j].node_type == type[k])
                        {
                            if(!m_list_B_Redis[j].IsConnected())
                            {
                                m_list_B_Redis[j].m_host = m_listNodeConfig[j].node_ip2;
                                bool ret = m_list_B_Redis[j].Connect(m_listNodeConfig[j].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                                if(ret)
                                {
                                    printf("Connection master ip success:%s\n",m_listNodeConfig[j].node_ip2);
                                }
                            }
                        }
                        if(m_list_B_Redis[j].IsConnected() && m_listNodeConfig[j].node_type == type[k])
                        {
                            redisReply* reply = m_list_B_Redis[j].SendRedisCmd("HMSET yx:%d:%d status %d", vecYX[i].param.DeviceID, vecYX[i].param.DeviceIndex, DATA_STATUS_INVALID);
                            if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
                            {
                                ret = true;
                            }
                            FreeReply(reply);
                        }
                    }
                }
            }
        }
    }


    return true;
}

int RDBI::SetGZRealValue(int rtuId, int dataId, short realValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET gz:%d:%d realValue %b status %d updateTime %b",
                                     rtuId, dataId, &realValue, sizeof(short), status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

int RDBI::SetGZRealValue(int rtuId, int dataId, short realValue, short calValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET gz:%d:%d realValue %b calValue %b status %d updateTime %b",
                                     rtuId, dataId, &realValue, sizeof(short), &calValue, sizeof(short),status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetGZManualValue(int rtuId, int dataId, short manualValue, uint8_t enable)
{
    bool ret = false;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET gz:%d:%d status 1 manualValue %b manualFlag 1 manualTime %b",
                                     rtuId, dataId, &manualValue, sizeof(short), &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetGZRealValue(int rtuId, int dataId, short &realValue, uint8_t &status)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET gz:%d:%d realValue manualValue manualFlag status", rtuId, dataId);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }
                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET gz:%d:%d realValue manualValue manualFlag status", rtuId, dataId);
                    flag = true;
                    break;
                }
            }
        }
    }

    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        short _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            realValue = *(short*)(reply->element[0]->str);
        }
        if (reply->element[1]->str)
        {
            _manualValue = *(short*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _flag = atoi(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            status = atoi(reply->element[3]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetGZRealValue(int rtuId, int dataId, short &realValue, short &calValue, uint8_t &status)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET gz:%d:%d realValue calValue manualValue manualFlag status", rtuId, dataId);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }
                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET gz:%d:%d realValue calValue manualValue manualFlag status", rtuId, dataId);
                    flag = true;
                    break;
                }
            }
        }
    }


    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        short _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            realValue = *(short*)(reply->element[0]->str);
        }

        if (reply->element[1]->str)
        {
            calValue = *(short*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _manualValue = *(short*)(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            _flag = atoi(reply->element[3]->str);
        }
        if (reply->element[4]->str)
        {
            status = atoi(reply->element[4]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetGZStatus(int rtuId, int dataId, uint8_t status)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET gz:%d:%d status %d", rtuId, dataId, status);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetGZNoAlarm(int rtuId, int dataId, uint8_t enable)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET gz:%d:%d noAlarm %d", rtuId, dataId, enable);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::ResetGZManualFlag(int rtuId, int dataId, uint8_t status)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET gz:%d:%d status %d manualFlag 0", rtuId, dataId, status);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetGZValues(QVector<T_SignalGZ> &vecGZ)
{
    bool ret = true;

    int count = vecGZ.size();
    int i = 0;
    for (i = 0; i<count; i++)
    {
        bool flag = false;
        if(m_listNodeConfig.size() > 0)
        {
            for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
            {
                if(m_list_A_Redis[j].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    m_list_A_Redis[j].AppendCommand("HMGET gz:%d:%d realValue manualValue manualFlag manualTime status fiveAntilock hangup hangupTime updateTime alarmTime onAlarmNum offAlarmNum noAlarm",
                                                    vecGZ[i].param.DeviceID, vecGZ[i].param.DeviceIndex);
                    flag = true;
                    redisReply *reply = NULL;;
                    m_list_A_Redis[j].GetReply((void **)&reply);

                    if (reply && reply->type == REDIS_REPLY_ARRAY)
                    {
                        if (reply->element[0]->str)
                            vecGZ[i].data.realValue = *(short*)(reply->element[0]->str);
                        if (reply->element[1]->str)
                            vecGZ[i].data.manualValue = *(short*)(reply->element[1]->str);
                        if (reply->element[2]->str)
                            vecGZ[i].data.manualFlag = atoi(reply->element[2]->str);
                        if (reply->element[3]->str)
                            vecGZ[i].data.manualTime = *(int64_t*)(reply->element[3]->str);
                        if (reply->element[4]->str)
                            vecGZ[i].data.status = atoi(reply->element[4]->str);

                        if (reply->element[5]->str)
                            vecGZ[i].data.fiveAntilock = atoi(reply->element[5]->str);
                        if (reply->element[6]->str)
                            vecGZ[i].data.hangup = atoi(reply->element[6]->str);
                        if (reply->element[7]->str)
                            vecGZ[i].data.hangupTime = *(int64_t*)(reply->element[7]->str);
                        if (reply->element[8]->str)
                            vecGZ[i].data.updateTime = *(int64_t*)(reply->element[8]->str);
                        if (reply->element[9]->str)
                            vecGZ[i].data.alarmTime = *(int64_t*)(reply->element[9]->str);
                        if (reply->element[10]->str)
                            vecGZ[i].data.onAlarmNum = atoi(reply->element[10]->str);
                        if (reply->element[11]->str)
                            vecGZ[i].data.offAlarmNum = atoi(reply->element[11]->str);
                        if (reply->element[12]->str)
                            vecGZ[i].data.noAlarm = atoi(reply->element[12]->str);
                    }
                    else
                    {
                        ret = false;
                    }

                    FreeReply(reply);

                    break;
                }
            }
        }

        if(!flag)
        {
            if(m_listNodeConfig.size() > 0)
            {
                for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                {
                    if(m_list_B_Redis[j].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                    {
                        m_list_B_Redis[j].AppendCommand("HMGET gz:%d:%d realValue manualValue manualFlag manualTime status fiveAntilock hangup hangupTime updateTime alarmTime onAlarmNum offAlarmNum noAlarm",
                                                        vecGZ[i].param.DeviceID, vecGZ[i].param.DeviceIndex);
                        flag = true;

                        redisReply *reply = NULL;;
                        m_list_B_Redis[j].GetReply((void **)&reply);

                        if (reply && reply->type == REDIS_REPLY_ARRAY)
                        {
                            if (reply->element[0]->str)
                                vecGZ[i].data.realValue = *(short*)(reply->element[0]->str);
                            if (reply->element[1]->str)
                                vecGZ[i].data.manualValue = *(short*)(reply->element[1]->str);
                            if (reply->element[2]->str)
                                vecGZ[i].data.manualFlag = atoi(reply->element[2]->str);
                            if (reply->element[3]->str)
                                vecGZ[i].data.manualTime = *(int64_t*)(reply->element[3]->str);
                            if (reply->element[4]->str)
                                vecGZ[i].data.status = atoi(reply->element[4]->str);

                            if (reply->element[5]->str)
                                vecGZ[i].data.fiveAntilock = atoi(reply->element[5]->str);
                            if (reply->element[6]->str)
                                vecGZ[i].data.hangup = atoi(reply->element[6]->str);
                            if (reply->element[7]->str)
                                vecGZ[i].data.hangupTime = *(int64_t*)(reply->element[7]->str);
                            if (reply->element[8]->str)
                                vecGZ[i].data.updateTime = *(int64_t*)(reply->element[8]->str);
                            if (reply->element[9]->str)
                                vecGZ[i].data.alarmTime = *(int64_t*)(reply->element[9]->str);
                            if (reply->element[10]->str)
                                vecGZ[i].data.onAlarmNum = atoi(reply->element[10]->str);
                            if (reply->element[11]->str)
                                vecGZ[i].data.offAlarmNum = atoi(reply->element[11]->str);
                            if (reply->element[12]->str)
                                vecGZ[i].data.noAlarm = atoi(reply->element[12]->str);
                        }
                        else
                        {
                            ret = false;
                        }

                        FreeReply(reply);

                        break;
                    }
                }
            }
        }

    }

    return ret;
}

bool RDBI::InvalidateGZData(QVector<T_SignalGZ> &vecGZ)
{
    int count = vecGZ.size();

    int type[2] = {1,2};
    for(int k=0;k<2;k++)
    {
        bool flag = false;
        for (int i = 0; i<count; i++)
        {
            flag = false;
            if(m_listNodeConfig.size() > 0)
            {
                for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                {
                    if(m_list_A_Redis[j].IsConnected() && m_listNodeConfig[j].node_type == type[k])
                    {
                        m_list_A_Redis[j].AppendCommand("HMSET gz:%d:%d status %d", vecGZ[i].param.DeviceID, vecGZ[i].param.DeviceIndex, DATA_STATUS_INVALID);
                        flag = true;
                        break;
                    }
                }
            }

            if(!flag)
            {
                if(m_listNodeConfig.size() > 0)
                {
                    for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                    {
                        if(m_list_B_Redis[j].IsConnected() && m_listNodeConfig[j].node_type == type[k])
                        {
                            m_list_B_Redis[j].AppendCommand("HMSET gz:%d:%d status %d", vecGZ[i].param.DeviceID, vecGZ[i].param.DeviceIndex, DATA_STATUS_INVALID);
                            flag = true;
                            break;
                        }
                    }
                }
            }
        }

        if(flag)
        {
            for (int i = 0; i < count; i++)
            {
                redisReply *reply = NULL;
                bool replyflag = false;

                if(m_listNodeConfig.size() > 0)
                {
                    for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
                    {
                        if(m_list_A_Redis[i].IsConnected() && m_listNodeConfig[i].node_type == type[k])
                        {
                            m_list_A_Redis[i].GetReply((void **)&reply);
                            FreeReply(reply);
                            replyflag = true;
                        }
                    }
                }

                if(!replyflag)
                {
                    if(m_listNodeConfig.size() > 0)
                    {
                        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
                        {
                            if(m_list_B_Redis[i].IsConnected() && m_listNodeConfig[i].node_type == type[k])
                            {
                                m_list_A_Redis[i].GetReply((void **)&reply);
                                FreeReply(reply);
                            }
                        }
                    }
                }
            }
        }
    }

    return true;
}

int RDBI::SetYMRealValue(int rtuId, int dataId, double realValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET ym:%d:%d realValue %b status %d updateTime %b",
                                     rtuId, dataId, &realValue, sizeof(double), status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

int RDBI::SetYMRealValue(int rtuId, int dataId, double realValue, double calValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET ym:%d:%d realValue %b calValue %b status %d updateTime %b",
                                     rtuId, dataId, &realValue, sizeof(double),&calValue, sizeof(double), status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetYMManualValue(int rtuId, int dataId, double manualValue)
{
    bool ret = false;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET ym:%d:%d status 1 manualValue %b manualFlag 1 manualTime %b",
                                     rtuId, dataId, &manualValue, sizeof(double), &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetYMRealValue(int rtuId, int dataId, double &realValue, uint8_t &status)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET ym:%d:%d realValue manualValue manualFlag status", rtuId, dataId);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }

                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET ym:%d:%d realValue manualValue manualFlag status", rtuId, dataId);
                    flag = true;
                    break;
                }
            }
        }
    }


    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        double _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            realValue = *(double*)(reply->element[0]->str);
        }
        if (reply->element[1]->str)
        {
            _manualValue = *(double*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _flag = atoi(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            status = atoi(reply->element[3]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetYMRealValue(int rtuId, int dataId, double &realValue, double &calValue, uint8_t &status)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET ym:%d:%d realValue calValue manualValue manualFlag status", rtuId, dataId);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }
                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET ym:%d:%d realValue calValue manualValue manualFlag status", rtuId, dataId);
                    flag = true;
                    break;
                }
            }
        }
    }

    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        double _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            realValue = *(double*)(reply->element[0]->str);
        }
        if (reply->element[1]->str)
        {
            calValue = *(double*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _manualValue = *(double*)(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            _flag = atoi(reply->element[3]->str);
        }
        if (reply->element[4]->str)
        {
            status = atoi(reply->element[4]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetYMStatus(int rtuId, int dataId, uint8_t status)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET ym:%d:%d status %d", rtuId, dataId, status);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetYMNoAlarm(int rtuId, int dataId, uint8_t enable)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET ym:%d:%d noAlarm %d", rtuId, dataId, enable);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::ResetYMManualFlag(int rtuId, int dataId, uint8_t status)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET ym:%d:%d status %d manualFlag 0", rtuId, dataId, status);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetYMValues(QVector<T_Impulse> &vecYM)
{
    bool ret = true;

    int count = vecYM.size();

    int i = 0;
    for (i = 0; i<count; i++)
    {
        bool flag = false;
        if(m_listNodeConfig.size() > 0)
        {
            for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
            {
                if(m_list_A_Redis[j].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    m_list_A_Redis[j].AppendCommand("HMGET ym:%d:%d realValue manualValue manualFlag manualTime status updateTime overRangeNum overRangeFlag overRangeTime noAlarm",
                                                    vecYM[i].param.DeviceID, vecYM[i].param.DeviceIndex);
                    flag = true;

                    redisReply *reply = NULL;
                    m_list_A_Redis[j].GetReply((void **)&reply);

                    if (reply && reply->type == REDIS_REPLY_ARRAY)
                    {
                        if (reply->element[0]->str)
                            vecYM[i].data.realValue = *(double*)(reply->element[0]->str);
                        if (reply->element[1]->str)
                            vecYM[i].data.manualValue = *(double*)(reply->element[1]->str);
                        if (reply->element[2]->str)
                            vecYM[i].data.manualFlag = atoi(reply->element[2]->str);
                        if (reply->element[3]->str)
                            vecYM[i].data.manualTime = *(int64_t*)(reply->element[3]->str);
                        if (reply->element[4]->str)
                            vecYM[i].data.status = atoi(reply->element[4]->str);
                        if (reply->element[5]->str)
                            vecYM[i].data.updateTime = *(int64_t*)(reply->element[5]->str);
                        if (reply->element[6]->str)
                            vecYM[i].data.overRangeNum = atoi(reply->element[6]->str);
                        if (reply->element[7]->str)
                            vecYM[i].data.overRangeFlag = atoi(reply->element[7]->str);
                        if (reply->element[8]->str)
                            vecYM[i].data.overRangeTime = *(int64_t*)(reply->element[8]->str);
                        if (reply->element[9]->str)
                            vecYM[i].data.noAlarm = atoi(reply->element[9]->str);
                    }
                    else
                    {
                        ret = false;
                    }

                    FreeReply(reply);

                    break;
                }
            }
        }

        if(!flag)
        {
            if(m_listNodeConfig.size() > 0)
            {
                for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                {
                    if(m_list_B_Redis[j].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                    {
                        m_list_B_Redis[j].AppendCommand("HMGET ym:%d:%d realValue manualValue manualFlag manualTime status updateTime overRangeNum overRangeFlag overRangeTime noAlarm",
                                                        vecYM[i].param.DeviceID, vecYM[i].param.DeviceIndex);
                        flag = true;

                        redisReply *reply = NULL;
                        m_list_B_Redis[j].GetReply((void **)&reply);

                        if (reply && reply->type == REDIS_REPLY_ARRAY)
                        {
                            if (reply->element[0]->str)
                                vecYM[i].data.realValue = *(double*)(reply->element[0]->str);
                            if (reply->element[1]->str)
                                vecYM[i].data.manualValue = *(double*)(reply->element[1]->str);
                            if (reply->element[2]->str)
                                vecYM[i].data.manualFlag = atoi(reply->element[2]->str);
                            if (reply->element[3]->str)
                                vecYM[i].data.manualTime = *(int64_t*)(reply->element[3]->str);
                            if (reply->element[4]->str)
                                vecYM[i].data.status = atoi(reply->element[4]->str);
                            if (reply->element[5]->str)
                                vecYM[i].data.updateTime = *(int64_t*)(reply->element[5]->str);
                            if (reply->element[6]->str)
                                vecYM[i].data.overRangeNum = atoi(reply->element[6]->str);
                            if (reply->element[7]->str)
                                vecYM[i].data.overRangeFlag = atoi(reply->element[7]->str);
                            if (reply->element[8]->str)
                                vecYM[i].data.overRangeTime = *(int64_t*)(reply->element[8]->str);
                            if (reply->element[9]->str)
                                vecYM[i].data.noAlarm = atoi(reply->element[9]->str);
                        }
                        else
                        {
                            ret = false;
                        }

                        FreeReply(reply);

                        break;
                    }
                }
            }
        }

    }

    return ret;
}

bool RDBI::InvalidateYMData(QVector<T_Impulse> &vecYM)
{
    int count = vecYM.size();

    int type[2] = {1,2};
    for(int k=0;k<2;k++)
    {
        bool flag = false;
        for (int i = 0; i<count; i++)
        {
            flag = false;
            if(m_listNodeConfig.size() > 0)
            {
                for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                {
                    if(m_listNodeConfig[j].node_type == 1 ||  m_listNodeConfig[j].node_type == 2)
                    {
                        if(!m_list_A_Redis[j].IsConnected())
                        {
                            m_list_A_Redis[j].m_host = m_listNodeConfig[j].node_ip1;
                            bool ret = m_list_A_Redis[j].Connect(m_listNodeConfig[j].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                            if(ret)
                            {
                                printf("Connection master ip success:%s\n",m_listNodeConfig[j].node_ip1);
                            }
                        }
                    }
                    if(m_list_A_Redis[j].IsConnected() && m_listNodeConfig[j].node_type == type[k])
                    {
                        m_list_A_Redis[j].AppendCommand("HMSET ym:%d:%d status %d", vecYM[i].param.DeviceID, vecYM[i].param.DeviceIndex, DATA_STATUS_INVALID);
                        flag = true;
                        break;
                    }
                }
            }

            if(!flag)
            {
                if(m_listNodeConfig.size() > 0)
                {
                    for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                    {
                        if(m_listNodeConfig[j].node_type == 1 ||  m_listNodeConfig[j].node_type == 2)
                        {
                            if(!m_list_B_Redis[j].IsConnected())
                            {
                                m_list_B_Redis[j].m_host = m_listNodeConfig[j].node_ip2;
                                bool ret = m_list_B_Redis[j].Connect(m_listNodeConfig[j].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                                if(ret)
                                {
                                    printf("Connection slave ip success:%s\n",m_listNodeConfig[j].node_ip2);
                                }
                            }
                        }
                        if(m_list_B_Redis[j].IsConnected() && m_listNodeConfig[j].node_type == type[k])
                        {
                            m_list_B_Redis[j].AppendCommand("HMSET ym:%d:%d status %d", vecYM[i].param.DeviceID, vecYM[i].param.DeviceIndex, DATA_STATUS_INVALID);
                            flag = true;
                            break;
                        }
                    }
                }
            }
        }

        if(flag)
        {
            for (int i = 0; i < count; i++)
            {
                redisReply *reply = NULL;
                bool replyflag = false;

                if(m_listNodeConfig.size() > 0)
                {
                    for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
                    {
                        if(m_list_A_Redis[i].IsConnected() && m_listNodeConfig[i].node_type == type[k])
                        {
                            m_list_A_Redis[i].GetReply((void **)&reply);
                            FreeReply(reply);
                            replyflag = true;
                        }
                    }
                }

                if(!replyflag)
                {
                    if(m_listNodeConfig.size() > 0)
                    {
                        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
                        {
                            if(m_list_B_Redis[i].IsConnected() && m_listNodeConfig[i].node_type == type[k])
                            {
                                m_list_A_Redis[i].GetReply((void **)&reply);
                                FreeReply(reply);
                            }
                        }
                    }
                }
            }
        }
    }

    return true;
}

bool RDBI::GetFepNodeStatus(int Id, uint8_t &status1, uint8_t &status2)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET fep:%d status1 status2", Id);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }
                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET fep:%d status1 status2", Id);
                    flag = true;
                    break;
                }
            }
        }
    }

    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        status1 = atoi(reply->element[0]->str);
        status2 = atoi(reply->element[1]->str);
        ret = true;
    }
    FreeReply(reply);

    return ret;
}


bool RDBI::SetFepNodeStatus(int Id, uint8_t status, bool bPrimary)
{
    bool ret = false;
    redisReply* reply;

    if (bPrimary)
    {
        if (status == NET_NODE_STATUS_CONNCET)
        {
            reply = SendRedisCmd("HMSET fep:%d status1 %d", Id, status);
        }
        else
        {
            int64_t now = QDateTime::currentMSecsSinceEpoch();
            reply = SendRedisCmd("HMSET fep:%d status1 %d lastOnlineTime1 %b", Id, status, &now, sizeof(int64_t));
        }
    }
    else
    {
        if (status == DATA_STATUS_VALID)
        {
            reply = SendRedisCmd("HMSET fep:%d status2 %d", Id, status);
        }
        else
        {
            int64_t now = QDateTime::currentMSecsSinceEpoch();
            reply = SendRedisCmd("HMSET fep:%d status2 %d lastOnlineTime2 %b", Id, status, &now, sizeof(int64_t));
        }
    }

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}


bool RDBI::GetFepChannelStatus(int Id, uint8_t &status1, uint8_t &status2)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET channel:%d status1 status2", Id);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }

                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET channel:%d status1 status2", Id);
                    flag = true;
                    break;
                }
            }
        }
    }


    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        status1 = atoi(reply->element[0]->str);
        status2 = atoi(reply->element[1]->str);
        ret = true;
    }
    FreeReply(reply);

    return ret;
}

bool RDBI::GetFepChannelStatus(VecFepChannel &vecFepChannel)
{
    bool ret = true;
    int count = vecFepChannel.size();

    count = vecFepChannel.size();

    int i = 0;
    for (i = 0; i < count; i++)
    {
        bool flag = false;
        if(m_listNodeConfig.size() > 0)
        {
            for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_A_Redis[i].IsConnected())
                    {
                        m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                        bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                        }
                    }
                }
                if(m_list_A_Redis[j].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    m_list_A_Redis[j].AppendCommand("HMGET channel:%d status1 status2 lastOnlineTime1 lastOnlineTime2", vecFepChannel[i].param.id);
                    flag = true;
                    break;
                }
            }
        }

        if(!flag)
        {
            if(m_listNodeConfig.size() > 0)
            {
                for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                {
                    if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                    {
                        if(!m_list_B_Redis[i].IsConnected())
                        {
                            m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                            bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                            if(ret)
                            {
                                printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                            }
                        }
                    }

                    if(m_list_B_Redis[j].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                    {
                        m_list_B_Redis[j].AppendCommand("HMGET channel:%d status1 status2 lastOnlineTime1 lastOnlineTime2", vecFepChannel[i].param.id);
                        flag = true;
                        break;
                    }
                }
            }
        }

    }

    for (i = 0; i < count; i++)
    {
        redisReply *reply = NULL;;
        m_redisClient.GetReply((void **)&reply);

        if (reply && reply->type == REDIS_REPLY_ARRAY)
        {
            if (reply->element[0]->str)
                vecFepChannel[i].status1 = atoi(reply->element[0]->str);
            if (reply->element[1]->str)
                vecFepChannel[i].status2 = atoi(reply->element[1]->str);
            if (reply->element[2]->str)
                vecFepChannel[i].lastOnlineTime1 = *(int64_t*)(reply->element[2]->str);
            if (reply->element[3]->str)
                vecFepChannel[i].lastOnlineTime2 = *(int64_t*)(reply->element[3]->str);
        }
        else
        {
            ret = false;
        }

        FreeReply(reply);
    }
    return ret;
}


bool RDBI::SetFepChannelStatus(int Id, uint8_t status1, uint8_t status2)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("HMSET channel:%d status1 %d status2 %d", Id, status1, status2);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}


bool RDBI::GetFepNodeStatus(VecFepNode &vecFepNode)
{
    bool ret = true;
    int count = vecFepNode.size();

    count = vecFepNode.size();

    int i = 0;
    for (i = 0; i < count; i++)
    {
        bool flag = false;
        if(m_listNodeConfig.size() > 0)
        {
            for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_A_Redis[i].IsConnected())
                    {
                        m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                        bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                        }
                    }
                }
                if(m_list_A_Redis[j].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    m_list_A_Redis[j].AppendCommand("HMGET fep:%d status1 status2 lastOnlineTime1 lastOnlineTime2", vecFepNode[i].param.id);
                    flag = true;
                    break;
                }
            }
        }

        if(!flag)
        {
            if(m_listNodeConfig.size() > 0)
            {
                for(int j=0;j<m_listNodeConfig.size() && j<MAXREDISCLIENT;j++)
                {
                    if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                    {
                        if(!m_list_B_Redis[i].IsConnected())
                        {
                            m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                            bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                            if(ret)
                            {
                                printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                            }
                        }
                    }
                    if(m_list_B_Redis[j].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                    {
                        m_list_B_Redis[j].AppendCommand("HMGET fep:%d status1 status2 lastOnlineTime1 lastOnlineTime2", vecFepNode[i].param.id);
                        flag = true;
                        break;
                    }
                }
            }
        }

    }

    for (i = 0; i < count; i++)
    {
        redisReply *reply = NULL;;
        m_redisClient.GetReply((void **)&reply);

        if (reply && reply->type == REDIS_REPLY_ARRAY)
        {
            if (reply->element[0]->str)
                vecFepNode[i].status1 = atoi(reply->element[0]->str);
            if (reply->element[1]->str)
                vecFepNode[i].status2 = atoi(reply->element[1]->str);
            if (reply->element[2]->str)
                vecFepNode[i].lastOnlineTime1 = *(int64_t*)(reply->element[2]->str);
            if (reply->element[3]->str)
                vecFepNode[i].lastOnlineTime2 = *(int64_t*)(reply->element[3]->str);

        }
        else
            ret = false;

        FreeReply(reply);
    }
    return ret;
}

bool RDBI::SendYK(REDIS_YK &yk, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        yk.timestamp = QDateTime::currentMSecsSinceEpoch();

    RedisPublisher pub;
    pub.Publish((char*)EVENT_RC_SCADA_YK, (char*)&yk, sizeof(REDIS_YK));

    return true;
}

bool RDBI::SendYT(REDIS_YT &yt, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        yt.timestamp = QDateTime::currentMSecsSinceEpoch();

    RedisPublisher pub;
    pub.Publish((char*)EVENT_RC_SCADA_YT, (char*)&yt, sizeof (REDIS_YT));

    return true;
}

bool RDBI::SendAdjustTime(REDIS_ADJUST_TIME &_time)
{
    RedisPublisher pub;
    pub.Publish((char*)EVENT_RC_SCADA_TIME, (char*)&_time, sizeof (REDIS_ADJUST_TIME));

    return true;
}

bool RDBI::SendFEPSOE(REDIS_SOE &soe, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        soe.timestamp = QDateTime::currentMSecsSinceEpoch();

    RedisPublisher pub;
    pub.Publish((char*)EVENT_SOE_FEP, (char*)&soe, sizeof (REDIS_SOE));

    return true;
}

bool RDBI::SendSOE(REDIS_SOE &soe, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        soe.timestamp = QDateTime::currentMSecsSinceEpoch();

    RedisPublisher pub;
    pub.Publish((char*)EVENT_SOE_SCADA, (char*)&soe, sizeof (REDIS_SOE));

    return true;
}

bool RDBI::SendSOEChannel(char *channel,REDIS_SOE &soe, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        soe.timestamp = QDateTime::currentMSecsSinceEpoch();

    RedisPublisher pub;
    pub.Publish(channel, (char*)&soe, sizeof (REDIS_SOE));

    return true;
}

bool RDBI::SendNetNodeSOE(REDIS_NETNODE_SOE &soe, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        soe.timestamp = QDateTime::currentMSecsSinceEpoch();

    RedisPublisher pub;
    pub.Publish((char*)EVENT_NETNODE_SOE, (char*)&soe, sizeof (REDIS_NETNODE_SOE));

    return true;
}

bool RDBI::SendDZ(REDIS_DZ &dz, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        dz.timestamp = QDateTime::currentMSecsSinceEpoch();
    RedisPublisher pub;
    pub.Publish((char*)EVENT_DZ_ORDER, (char*)&dz, sizeof (REDIS_DZ));

    return true;
}

bool RDBI::SendTT(REDIS_TT &tt, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        tt.timestamp = QDateTime::currentMSecsSinceEpoch();

    RedisPublisher pub;
    pub.Publish((char*)EVENT_TT_ORDER, (char*)&tt, sizeof (REDIS_TT));

    return true;
}

bool RDBI::SendGZSOE(REDIS_FAULT_SOE &soe, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        soe.timestamp = QDateTime::currentMSecsSinceEpoch();

    RedisPublisher pub;
    pub.Publish((char*)EVENT_GZSOE_SCADA, (char*)&soe, sizeof (REDIS_FAULT_SOE));

    return true;
}

bool RDBI::SendYKM(REDIS_YK &yk, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        yk.timestamp = QDateTime::currentMSecsSinceEpoch();

    Publish((char*)EVENT_RC_SCADA_YK, (char*)&yk, sizeof(REDIS_YK));

    return true;
}

bool RDBI::SendYTM(REDIS_YT &yt, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        yt.timestamp = QDateTime::currentMSecsSinceEpoch();

    Publish((char*)EVENT_RC_SCADA_YT, (char*)&yt, sizeof (REDIS_YT));

    return true;
}

bool RDBI::SendAdjustTimeM(REDIS_ADJUST_TIME &_time)
{
    Publish((char*)EVENT_RC_SCADA_TIME, (char*)&_time, sizeof (REDIS_ADJUST_TIME));

    return true;
}

bool RDBI::SendFEPSOEM(REDIS_SOE &soe, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        soe.timestamp = QDateTime::currentMSecsSinceEpoch();

    SoePublish((char*)EVENT_SOE_FEP, (char*)&soe, sizeof (REDIS_SOE));

    return true;
}

bool RDBI::SendSOEM(REDIS_SOE &soe, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        soe.timestamp = QDateTime::currentMSecsSinceEpoch();

    SoePublish((char*)EVENT_SOE_SCADA, (char*)&soe, sizeof (REDIS_SOE));

    return true;
}

bool RDBI::SendSOEChannelM(char *channel, REDIS_SOE &soe, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        soe.timestamp = QDateTime::currentMSecsSinceEpoch();

    SoePublish(channel, (char*)&soe, sizeof (REDIS_SOE));

    return true;
}

bool RDBI::SendNetNodeSOEM(REDIS_NETNODE_SOE &soe, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        soe.timestamp = QDateTime::currentMSecsSinceEpoch();

    SoePublish((char*)EVENT_NETNODE_SOE, (char*)&soe, sizeof (REDIS_NETNODE_SOE));

    return true;
}

int RDBI::SetDZRealValue(int rtuId, int dataId, double realValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET dz:%d:%d realValue %b status %d updateTime %b",
                                     rtuId, dataId, &realValue, sizeof(double), status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetDZRealValue(int rtuId, int dataId, double &realValue, unsigned char &status)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET dz:%d:%d realValue manualValue manualFlag status", rtuId, dataId);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }

                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET dz:%d:%d realValue manualValue manualFlag status", rtuId, dataId);
                    flag = true;
                    break;
                }
            }
        }
    }

    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        double _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            //不是double，返回false
            if(reply->element[0]->len == sizeof(double))
            {
                realValue = *(double*)(reply->element[0]->str);
            }
            else
            {
                FreeReply(reply);
                return false;
            }
        }
        if (reply->element[1]->str)
        {
            _manualValue = *(double*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _flag = atoi(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            status = atoi(reply->element[3]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    if(reply)
    {
        FreeReply(reply);
    }

    return ret;
}

int RDBI::SetTTRealValue(int rtuId, int dataId, short realValue, uint8_t status)
{
    int ret = 0;

    int64_t now = QDateTime::currentMSecsSinceEpoch();

    redisReply* reply = SendRedisCmd("HMSET tt:%d:%d realValue %b status %d updateTime %b",
                                     rtuId, dataId, &realValue, sizeof(double), status, &now, sizeof(int64_t));

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::GetTTRealValue(int rtuId, int dataId, short &realValue, uint8_t &status)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("HMGET tt:%d:%d realValue manualValue manualFlag status", rtuId, dataId);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }
                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("HMGET tt:%d:%d realValue manualValue manualFlag status", rtuId, dataId);
                    flag = true;
                    break;
                }
            }
        }
    }


    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        short _manualValue = 0;
        uint8_t _flag = 0;

        if (reply->element[0]->str)
        {
            realValue = *(short*)(reply->element[0]->str);
        }
        if (reply->element[1]->str)
        {
            _manualValue = *(short*)(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            _flag = atoi(reply->element[2]->str);
        }
        if (reply->element[3]->str)
        {
            status = atoi(reply->element[3]->str);
        }
        if (_flag)
        {
            realValue = _manualValue;
        }
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SendDZM(REDIS_DZ &dz, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        dz.timestamp = QDateTime::currentMSecsSinceEpoch();

    Publish((char*)EVENT_DZ_ORDER, (char*)&dz, sizeof (REDIS_DZ));

    return true;
}

bool RDBI::SendTTM(REDIS_TT &tt, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        tt.timestamp = QDateTime::currentMSecsSinceEpoch();

    Publish((char*)EVENT_TT_ORDER, (char*)&tt, sizeof (REDIS_TT));

    return true;
}

bool RDBI::SendGZSOEM(REDIS_FAULT_SOE &soe, bool bAutoSetTimestamp)
{
    if (bAutoSetTimestamp)
        soe.timestamp = QDateTime::currentMSecsSinceEpoch();

    SoePublish((char*)EVENT_GZSOE_SCADA, (char*)&soe, sizeof (REDIS_FAULT_SOE));

    return true;
}

bool RDBI::GetNodeFepStatus(int fepid, uint8_t &status_A, uint8_t &status_B, uint8_t &bMaster)
{
    status_A = 0;
    status_B = 0;
    bMaster = 0;

    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            QString hostName = QHostInfo::localHostName();

            if(strcmp(hostName.toStdString().c_str(),m_listNodeConfig[i].node_name)==0)
            {
                {
                    if(!m_list_A_Redis[i].IsConnected())
                    {
                        m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                        bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                        }
                    }
                }
                if(m_list_A_Redis[i].IsConnected())
                {
                    reply = m_list_A_Redis[i].SendRedisCmd("HMGET fepNode:%d A B status", fepid);
                    flag = true;
                    break;
                }
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                QString hostName = QHostInfo::localHostName();
                if(strcmp(hostName.toStdString().c_str(),m_listNodeConfig[i].node_name)==0)
                {
                    {
                        if(!m_list_B_Redis[i].IsConnected())
                        {
                            m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                            bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                            if(ret)
                            {
                                printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                            }
                        }
                    }
                    if(m_list_B_Redis[i].IsConnected())
                    {
                        reply = m_list_B_Redis[i].SendRedisCmd("HMGET fepNode:%d A B status", fepid);
                        flag = true;
                        break;
                    }
                }
            }
        }
    }


    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        if (reply->element[0]->str)
        {
            status_A = atoi(reply->element[0]->str);
        }
        if (reply->element[1]->str)
        {
            status_B = atoi(reply->element[1]->str);
        }
        if (reply->element[2]->str)
        {
            bMaster = atoi(reply->element[2]->str);
        }


        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::SetNodeFepStatus(int fepid, uint8_t status_A, uint8_t status_B, uint8_t bMaster)
{

    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            QString hostName = QHostInfo::localHostName();
            if(strcmp(hostName.toStdString().c_str(),m_listNodeConfig[i].node_name)==0)

            {
                {
                    if(!m_list_A_Redis[i].IsConnected())
                    {
                        m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                        bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                        }
                    }
                }

                if(m_list_A_Redis[i].IsConnected())
                {
                    reply = m_list_A_Redis[i].SendRedisCmd("HMSET fepNode:%d A %d B %d status %d", fepid,status_A,status_B,bMaster);
                    flag = true;
                    break;
                }

            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                QString hostName = QHostInfo::localHostName();
                if(strcmp(hostName.toStdString().c_str(),m_listNodeConfig[i].node_name)==0)
                {
                    {
                        if(!m_list_B_Redis[i].IsConnected())
                        {
                            m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                            bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                            if(ret)
                            {
                                printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                            }
                        }
                    }
                    if(m_list_B_Redis[i].IsConnected())
                    {
                        reply = m_list_B_Redis[i].SendRedisCmd("HMSET fepNode:%d A %d B %d status %d", fepid,status_A,status_B,bMaster);
                        flag = true;
                        break;
                    }

                }
            }
        }
    }

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;

}

bool RDBI::SetNodeFepStatus(int fepid, uint8_t bMaster)
{
    bool ret = false;

    bool flag = false;

    redisReply *reply = NULL;

    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            QString hostName = QHostInfo::localHostName();
            if(strcmp(hostName.toStdString().c_str(),m_listNodeConfig[i].node_name)==0)
            {
                {
                    if(!m_list_A_Redis[i].IsConnected())
                    {
                        m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                        bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                        }
                    }
                }
                if(m_list_A_Redis[i].IsConnected())
                {
                    reply = m_list_A_Redis[i].SendRedisCmd("HMSET fepNode:%d status %d", fepid,bMaster);
                    flag = true;
                    break;
                }

            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                QString hostName = QHostInfo::localHostName();
                if(strcmp(hostName.toStdString().c_str(),m_listNodeConfig[i].node_name)==0)
                {
                    {
                        if(!m_list_B_Redis[i].IsConnected())
                        {
                            m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                            bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                            if(ret)
                            {
                                printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                            }
                        }
                    }
                    if(m_list_B_Redis[i].IsConnected())
                    {
                        reply = m_list_B_Redis[i].SendRedisCmd("HMSET fepNode:%d status %d", fepid,bMaster);
                        flag = true;
                        break;
                    }

                }
            }
        }
    }

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
    {
        ret = true;
    }

    FreeReply(reply);

    return ret;

}

bool RDBI::SetStringTTL(char *skeys, int seconds)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("EXPIRE %s %d", skeys, seconds);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
        ret = true;

    FreeReply(reply);

    return ret;
}

bool RDBI::SetStringValue(char *skeys, char *svalue)
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("SET %s %s", skeys, svalue);

    if (reply && reply->type == REDIS_REPLY_STATUS && stricmp(reply->str, "OK") == 0)
        ret = true;

    FreeReply(reply);

    return ret;
}

bool RDBI::GetStringValue(char *skeys,char *stringbuf,int buflen)
{
    bool ret = false;

    if(buflen < 2 || !stringbuf)
    {
        return false;
    }

    redisReply *reply = NULL;
    bool flag = false;
    if(m_listNodeConfig.size() > 0)
    {
        for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
        {
            if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
            {
                if(!m_list_A_Redis[i].IsConnected())
                {
                    m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                    bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                    if(ret)
                    {
                        printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                    }
                }
            }
            if(m_list_A_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
            {
                reply = m_list_A_Redis[i].SendRedisCmd("GET %s", skeys);
                flag = true;
                break;
            }
        }
    }

    if(!flag)
    {
        if(m_listNodeConfig.size() > 0)
        {
            for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
            {
                if(m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2)
                {
                    if(!m_list_B_Redis[i].IsConnected())
                    {
                        m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                        bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                        if(ret)
                        {
                            printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                        }
                    }
                }
                if(m_list_B_Redis[i].IsConnected() && (m_listNodeConfig[i].node_type == 1 ||  m_listNodeConfig[i].node_type == 2))
                {
                    reply = m_list_B_Redis[i].SendRedisCmd("GET %s", skeys);
                    flag = true;
                    break;
                }
            }
        }
    }

    if (reply && reply->type == REDIS_REPLY_STRING)
    {
        if (reply->str)
        {
            strncpy(stringbuf,reply->str,buflen-1);
        }
        ret = true;
    }

    if(reply)
    {
        FreeReply(reply);
    }

    return ret;
}

bool RDBI::GetCurrStringValue(char *skeys,char *stringbuf,int buflen)
{
    bool ret = false;

    if(!m_redisClient.IsConnected())
    {
        m_redisClient.m_host = LOCAL_SERVER;
        bool ret = m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
        if(ret)
        {
            ;
        }
    }

    if(buflen < 2 || !stringbuf)
    {
        return false;
    }

    redisReply *reply = NULL;

    reply = m_redisClient.SendRedisCmd("GET %s", skeys);

    if (reply && reply->type == REDIS_REPLY_STRING)
    {
        if (reply->str)
        {
            strncpy(stringbuf,reply->str,buflen-1);
        }
        ret = true;
    }

    if(reply)
    {
        FreeReply(reply);
    }

    return ret;
}

bool RDBI::SetQueueData(char *listkey, char *msg, int len)
{
    if(!m_redisClient.IsConnected())
    {
        m_redisClient.m_host = LOCAL_SERVER;
        bool ret = m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
        if(!ret)
        {
            return false;
        }

    }

    if (len>0)
    {
        if(len > 10240)
        {
            printf("error:SetQueueData msg len too big,SetQueueData return\n");
            return false;
        }
        const int argc = 3;
        char buf1[50]={0}, buf2[10240]={0};
        char *argv[3] = { (char*)"LPUSH", buf1, buf2 };

        strcpy(buf1, listkey);
        memcpy(buf2, msg, len);

        size_t argvlen[] = { strlen(argv[0]), strlen(argv[1]), len};
        m_redisClient.FreeReply(m_redisClient.SendRedisCmdArgv(argc, (const char**)argv, argvlen));
    }
    else
    {
        return false;
    }

    return true;
}

bool RDBI::GetQueueData(char *listkey, char *msg, int len, bool bblock,int blocktime)
{

    bool ret = false;

    redisReply *reply = NULL;

    if(!m_redisClient.IsConnected())
    {
        m_redisClient.m_host = LOCAL_SERVER;
        bool ret = m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
        if(!ret)
        {
            return false;
        }
    }

    if(bblock)
    {
        reply = m_redisClient.SendRedisCmd("BRPOP %s %d", listkey,blocktime);
    }
    else
    {
        reply = m_redisClient.SendRedisCmd("RPOP %s", listkey);
    }


    if (reply && reply->type == REDIS_REPLY_STRING)
    {
        if (reply->str)
        {
            int strlen = reply->len;
            if(strlen > len)
            {
                strlen = len;
            }

            memcpy(msg,reply->str,strlen);
        }

        ret = true;
    }

    if (reply && reply->type == REDIS_REPLY_ARRAY)
    {
        if (reply->element[1]->str)
        {
            int strlen = reply->element[1]->len;
            if(strlen > len)
            {
                strlen = len;
            }

            memcpy(msg,reply->element[1]->str,strlen);
        }


        ret = true;
    }

    FreeReply(reply);

    return ret;
}

bool RDBI::Publish(char *channel, char *msg, int len,int type)
{
    if(type == 2)
    {
        return SoePublish(channel,msg,len);
    }

    if(!m_redisClient.IsConnected())
    {
        m_redisClient.m_host = LOCAL_SERVER;
        bool ret = m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
        if(!ret)
        {
            return false;
        }
    }

    if (len>0)
    {
        if(len > 10240)
        {
            printf("error:buf len too big,Publish return\n");
            return false;
        }
        const int argc = 3;
        static char buf1[50]={0}, buf2[10240]={0};
        static char *argv[3] = { (char*)"PUBLISH", buf1, buf2 };
        strcpy(buf1, channel);
        memcpy(buf2, msg, len);

        size_t argvlen[] = { strlen(argv[0]), strlen(argv[1]), len};
        m_redisClient.FreeReply(m_redisClient.SendRedisCmdArgv(argc, (const char**)argv, argvlen));
    }
    else
    {
        m_redisClient.FreeReply(m_redisClient.SendRedisCmd("PUBLISH %s %s", channel, msg));
    }

    return true;

}

bool RDBI::SoePublish(char *channel, char *msg, int len)
{
    if (len>0)
    {
        if(len > 10240)
        {
            printf("error:buf len too big,Publish return\n");
            return false;
        }
        const int argc = 3;
        static char buf1[50]={0}, buf2[10240]={0};
        static char *argv[3] = { (char*)"PUBLISH", buf1, buf2 };
        strcpy(buf1, channel);
        memcpy(buf2, msg, len);

        size_t argvlen[] = { strlen(argv[0]), strlen(argv[1]), len};

        redisReply *reply = NULL;

        if(m_listNodeConfig.size() > 0)
        {
            int ntype[3]={1,2,6};
            for(int j=0;j<3;j++)
            {
                for(int i=0;i<m_listNodeConfig.size() && i<MAXREDISCLIENT;i++)
                {
                    if(m_listNodeConfig[i].node_type == ntype[j])
                    {
                        {
                            if(!m_list_A_Redis[i].IsConnected())
                            {
                                m_list_A_Redis[i].m_host = m_listNodeConfig[i].node_ip1;
                                bool ret = m_list_A_Redis[i].Connect(m_listNodeConfig[i].node_ip1, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                                if(ret)
                                {
                                    printf("Connection master ip success:%s\n",m_listNodeConfig[i].node_ip1);
                                }
                            }
                        }
                        {
                            if(!m_list_B_Redis[i].IsConnected())
                            {
                                m_list_B_Redis[i].m_host = m_listNodeConfig[i].node_ip2;
                                bool ret = m_list_B_Redis[i].Connect(m_listNodeConfig[i].node_ip2, REDIS_PORT, REDIS_DEFAULT_TIMEOUT);
                                if(ret)
                                {
                                    printf("Connection slave ip success:%s\n",m_listNodeConfig[i].node_ip2);
                                }
                            }
                        }

                        if(m_list_A_Redis[i].IsConnected())
                        {
                            reply = m_list_A_Redis[i].SendRedisCmdArgv(argc, (const char**)argv, argvlen);
                            m_list_A_Redis[i].FreeReply(reply);
                        }
                        else if(m_list_B_Redis[i].IsConnected())
                        {
                            reply = m_list_B_Redis[i].SendRedisCmdArgv(argc, (const char**)argv, argvlen);
                            m_list_B_Redis[i].FreeReply(reply);
                        }
                    }

                }
            }

        }
    }
    else
    {
        return false;
    }

    return true;
}

RedisSubscriber::RedisSubscriber():pridata(nullptr),
    m_userCallback(nullptr),
    m_bStop(true)

{

}

RedisSubscriber::~RedisSubscriber()
{

}

int RedisSubscriber::Subscribe(T_SubscribeInfo &si)
{
    m_vecChannels.push_back(si);

    return m_vecChannels.size();
}

int RedisSubscriber::UnSubscribe()
{
    /*
    foreach (const T_SubscribeInfo& si, m_vecChannels)
    {
        if (si.type == TYPE_SUBSCRIBE)
        {
            m_redisClient.FreeReply(m_redisClient.SendRedisCmd("UNSUBSCRIBE %s", si.channelName.c_str()));
        }
        else if (si.type == TYPE_PSUBSCRIBE)
        {
            m_redisClient.FreeReply(m_redisClient.SendRedisCmd("PUNSUBSCRIBE %s", si.channelName.c_str()));
        }
    }

    m_vecChannels.clear();
    */
    return 0;
}

bool RedisSubscriber::Start()
{
    bool status = false;
    status = m_redisClient.Connect(LOCAL_SERVER,REDIS_PORT, REDIS_DEFAULT_TIMEOUT,1);

    if (status)
    {
        foreach(const T_SubscribeInfo& si, m_vecChannels)
        {
            if (si.type == TYPE_SUBSCRIBE)
            {
                m_redisClient.FreeReply(m_redisClient.SendRedisCmd("SUBSCRIBE %s", si.channelName.c_str()));
            }
            else if (si.type == TYPE_PSUBSCRIBE)
            {
                m_redisClient.FreeReply(m_redisClient.SendRedisCmd("PSUBSCRIBE %s", si.channelName.c_str()));
            }
        }
        pthread_create(&m_hThread, NULL, SubscribeProc, this);
        m_bStop = false;
        return true;
    }

    return false;
}

void RedisSubscriber::Stop()
{
    if(m_vecChannels.size() > 0)
    {
        if(!m_bStop)
        {
            foreach (const T_SubscribeInfo& si, m_vecChannels)
            {
                if (si.type == TYPE_SUBSCRIBE)
                {
                    m_redisClient.FreeReply(m_redisClient.SendRedisCmd("UNSUBSCRIBE %s", si.channelName.c_str()));
                }
                else if (si.type == TYPE_PSUBSCRIBE)
                {
                    m_redisClient.FreeReply(m_redisClient.SendRedisCmd("PUNSUBSCRIBE %s", si.channelName.c_str()));
                }
            }
            bool ret = m_redisClient.QuitRedis();
            if(ret)
            {
                m_bStop = true;
            }
            pthread_join(m_hThread, NULL);
        }

        m_vecChannels.clear();
    }

}

void *RedisSubscriber::SubscribeProc(void *p)
{
    RedisSubscriber* subscriber = (RedisSubscriber*)p;
    redisReply *reply = NULL;

    if(!subscriber->m_redisClient.IsConnected())
    {
        subscriber->m_redisClient.m_host = LOCAL_SERVER;
        bool ret = subscriber->m_redisClient.Connect(LOCAL_SERVER, REDIS_PORT, REDIS_DEFAULT_TIMEOUT,1);
        if(ret)
        {
            ;
        }
    }

    while (subscriber->m_redisClient.GetReply((void**)&reply) == REDIS_OK)
    {
        if (reply && reply->type == REDIS_REPLY_ARRAY && reply->elements >= 3 &&
                reply->element[0]->str && reply->element[1]->str &&
                reply->element[2]->str)
        {
            static const size_t messgeLen = strlen("message");
            static const size_t pmessgeLen = strlen("pmessage");

            if (reply->elements == 3 &&
                    strnicmp(reply->element[0]->str, "message", messgeLen) == 0)
            {
                if (subscriber->m_userCallback)
                {
                    subscriber->m_userCallback(reply->element[1]->str, reply->element[2]->str, reply->element[2]->len, subscriber->pridata);
                }
            }
            else if (reply->elements == 4 && reply->element[3]->str &&
                     strnicmp(reply->element[0]->str, "pmessage", pmessgeLen) == 0)
            {
                if (subscriber->m_userCallback)
                {
                    subscriber->m_userCallback(reply->element[2]->str, reply->element[3]->str, reply->element[3]->len, subscriber->pridata);
                }
            }
        }

        subscriber->m_redisClient.FreeReply(reply);
    }

    if (!subscriber->m_bStop)
    {
        subscriber->m_redisClient.Disconnect();
        bool ret = false;
        do
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(2000));
            ret = subscriber->Start();
        }
        while (!ret && !subscriber->m_bStop);
    }

    return NULL;
}

void RedisSubscriber::SetCallback(msgCallbackFn cb, void *p)
{
    m_userCallback = cb;
    pridata = p;
}

RedisPublisher::RedisPublisher()
{

}

RedisPublisher::~RedisPublisher()
{

}

void RedisPublisher::Publish(char *channel, char *msg, int len)
{
    char *subs = nullptr;
    //  subs = strstr(channel,"soe");
    //   if(subs)

    int port = 6379;
    int timeout = 200;
    VecNodeConfig listNodeConfig;

    CLibMySql   pSql;
    pSql.Load();

    bool ret = pSql.SelectNodeConfig(listNodeConfig);

    pSql.Unload();
    if(ret)
    {
        if(listNodeConfig.size() > 0)
        {
            int ntype[3]={1,2,6};
            for(int j=0;j<3;j++)
            {
                for(int i=0;i<listNodeConfig.size() && i<MAXREDISCLIENT;i++)
                {
                    if( listNodeConfig[i].node_type == ntype[j])
                    {
                        bool ret = m_list_A_Redis[i].Connect(listNodeConfig[i].node_ip1, port, timeout);
                        if(!ret)
                        {
                            ret = m_list_B_Redis[i].Connect(listNodeConfig[i].node_ip2, port, timeout);
                            if(ret)
                            {
                                break;
                            }

                        }
                        else
                        {
                            break;
                        }
                    }
                }
            }

        }

        if(len > 10240)
        {
            printf("error:buf len too big,Publish return\n");
            return;
        }
        const int argc = 3;
        static char buf1[50]={0}, buf2[10240]={0};
        static char *argv[3] = { (char*)"PUBLISH", buf1, buf2 };
        strcpy(buf1, channel);
        memcpy(buf2, msg, len);

        size_t argvlen[] = { strlen(argv[0]), strlen(argv[1]), len};

        redisReply *reply = NULL;

        if(listNodeConfig.size() > 0)
        {
            int ntype[3]={1,2,6};
            for(int j=0;j<3;j++)
            {
                for(int i=0;i<listNodeConfig.size() && i<MAXREDISCLIENT;i++)
                {
                    if(listNodeConfig[i].node_type == ntype[j])
                    {
                        if(m_list_A_Redis[i].IsConnected())
                        {
                            reply = m_list_A_Redis[i].SendRedisCmdArgv(argc, (const char**)argv, argvlen);
                            m_list_A_Redis[i].FreeReply(reply);
                        }
                        else if(m_list_B_Redis[i].IsConnected())
                        {
                            reply = m_list_B_Redis[i].SendRedisCmdArgv(argc, (const char**)argv, argvlen);
                            m_list_B_Redis[i].FreeReply(reply);
                        }
                    }

                }
            }

        }
    }
    else
    {
        return;
    }


    for(int i=0;i<listNodeConfig.size() && i<MAXREDISCLIENT;i++)
    {

        if(listNodeConfig[i].node_type == 1 || listNodeConfig[i].node_type == 2 || listNodeConfig[i].node_type == 6)
        {
            if(m_list_A_Redis[i].IsConnected())
            {
                m_list_A_Redis[i].Disconnect();
            }
            if(m_list_B_Redis[i].IsConnected())
            {
                m_list_B_Redis[i].Disconnect();
            }
        }

    }

}
