#ifndef RDBI_H
#define RDBI_H

#include <QtCore/qglobal.h>

#if defined(RDBI_LIBRARY)
#  define RDBISHARED_EXPORT Q_DECL_EXPORT
#else
#  define RDBISHARED_EXPORT Q_DECL_IMPORT
#endif

#include "db/rtdb.h"
#include "redisclient.h"

#define stricmp strcasecmp
#define strnicmp strncasecmp

#define MAXREDISCLIENT 10


using namespace std;

class RDBISHARED_EXPORT RDBI
{

public:
    RDBI();
    virtual ~RDBI();

public:
    //不带密码的连接
    bool Connect(const char* host = LOCAL_SERVER, int port = REDIS_PORT, int timeout = REDIS_DEFAULT_TIMEOUT);
    //带密码的连接
    bool ConnectWithpass(const char* host, int port, const char* pass,int timeout = REDIS_DEFAULT_TIMEOUT);

    //判断连接是否正常
    bool IsConnected();

    void ConnectedState(int *state_a,int *state_b);

    // 断开Redis服务器
    void Disconnect();

    // 直接发送Redis命令
    redisReply* SendRedisCmd(const char *format, ...);

    // 管道方式发送Redis命令
    int AppendCommand(const char *format, ...);

    // 释放Reply
    void FreeReply(void *reply);
    // 批量释放多个Reply内存，用于管道方式
    void FreeReplies(int nReply);
    // 发送心跳包
    bool SendKeepAlive(int nodeId, int type = NET_NODE_TYPE_SCADA, bool primary = true, int expireInSec = 10);

    bool sendPing();
    // 获取设置网络节点状态
    bool GetCurrentNodeStatus(uint8_t& status);
    bool GetScadaNodeStatus(int id, uint8_t& status);
    bool SetScadaNodeStatus(int id, uint8_t status);
    bool GetScadaNodeStatus(VecScadaNode& vecScadaNode);

    bool GetDeviceStatus(int id, uint8_t& status);
    bool SetDeviceStatus(int id, uint8_t status);

    // 遥测数据访问接口

    int SetYCRealValue(int rtuId, int dataId, double realValue, uint8_t status = DATA_STATUS_VALID);
    int SetYCRealValue(int rtuId, int dataId, double realValue, double calValue,uint8_t status = DATA_STATUS_VALID);

    //设置遥测值支持字符串，和支持字符串的GetYCRealValue配对使用
    int SetYCRealValue(int rtuId, int dataId, char *stringbuf,uint8_t status = DATA_STATUS_VALID);

    //设置本机遥测值
    int SetYCRealValueLocal(int rtuId, int dataId, double realValue, uint8_t status = DATA_STATUS_VALID);
    int SetYCRealValueLocal(int rtuId, int dataId, double realValue, double calValue,uint8_t status = DATA_STATUS_VALID);
    int SetYCRealValueLocal(int rtuId, int dataId, char *stringbuf,uint8_t status = DATA_STATUS_VALID);

    bool SetYCManualValue(int rtuId, int dataId, double manualValue);

    //获取遥测值支持字符串，和支持字符串的SetYCRealValue配对使用
    bool GetYCRealValue(int rtuId, int dataId, char *stringbuf,int buflen, uint8_t& status);
    bool GetYCRealValue(int rtuId, int dataId, double& realValue, uint8_t& status);
    bool GetYCRealValue(int rtuId, int dataId, double& realValue, double& calValue,uint8_t& status);

    //获取本机遥测值
    bool GetYCRealValueLocal(int rtuId, int dataId, char *stringbuf,int buflen, uint8_t& status);
    bool GetYCRealValueLocal(int rtuId, int dataId, double& realValue, uint8_t& status);
    bool GetYCRealValueLocal(int rtuId, int dataId, double& realValue, double& calValue,uint8_t& status);

    bool SetYCStatus(int rtuId, int dataId, uint8_t status);
    bool SetYCNoAlarm(int rtuId, int dataId, uint8_t enable = 1);
    bool ResetYCManualFlag(int rtuId, int dataId, uint8_t status = DATA_STATUS_VALID);
    bool GetYCValues(QVector<T_Analog>& vecYC);
    bool InvalidateYCData(QVector<T_Analog>& vecYC);

    // XB数据访问接口
    int SetXBRealValue(int rtuId, int dataId, double realValue, uint8_t status = DATA_STATUS_VALID);
    int SetXBRealValue(int rtuId, int dataId, double realValue, double calValue,uint8_t status = DATA_STATUS_VALID);

    bool SetXBManualValue(int rtuId, int dataId, double manualValue);

    bool GetXBRealValue(int rtuId, int dataId, double& realValue, uint8_t& status);
    bool GetXBRealValue(int rtuId, int dataId, double& realValue,double& calValue, uint8_t& status);
    bool SetXBStatus(int rtuId, int dataId, uint8_t status);
    bool SetXBNoAlarm(int rtuId, int dataId, uint8_t enable = 1);
    bool ResetXBManualFlag(int rtuId, int dataId, uint8_t status = DATA_STATUS_VALID);

    bool GetXBValues(QVector<T_SignalXB>& vecXB);
    bool InvalidateXBData(QVector<T_SignalXB>& vecXB);

    // 遥信数据访问接口
    int SetYXRealValue(int rtuId, int dataId, short realValue, uint8_t status = DATA_STATUS_VALID);
    int SetYXRealValue(int rtuId, int dataId, short realValue,short calValue, uint8_t status = DATA_STATUS_VALID);

    //设置本机遥信值
    int SetYXRealValueLocal(int rtuId, int dataId, short realValue, uint8_t status = DATA_STATUS_VALID);
    int SetYXRealValueLocal(int rtuId, int dataId, short realValue,short calValue, uint8_t status = DATA_STATUS_VALID);

    bool SetYXManualValue(int rtuId, int dataId, short manualValue, uint8_t enable = 1);

    bool GetYXRealValue(int rtuId, int dataId, short& realValue, uint8_t& status);
    bool GetYXRealValue(int rtuId, int dataId, short& realValue,short& calValue, uint8_t& status);

    //获取本机遥测值
    bool GetYXRealValueLocal(int rtuId, int dataId, short& realValue, uint8_t& status);
    bool GetYXRealValueLocal(int rtuId, int dataId, short& realValue,short& calValue, uint8_t& status);

    bool SetYXStatus(int rtuId, int dataId, uint8_t status);
    bool SetYXNoAlarm(int rtuId, int dataId, uint8_t enable = 1);
    bool ResetYXManualFlag(int rtuId, int dataId, uint8_t status = DATA_STATUS_VALID);

    bool SetFiveAntiLock(int rtuId, int dataId, uint8_t enable = 1);
    bool SetHangup(int rtuId, int dataId, uint8_t enable = 1);

    bool GetYXValues(QVector<T_Digital>& vecYX);
    bool InvalidateYXData(QVector<T_Digital>& vecYX);

    // 故障数据访问接口
    int SetGZRealValue(int rtuId, int dataId, short realValue, uint8_t status = DATA_STATUS_VALID);
    int SetGZRealValue(int rtuId, int dataId, short realValue, short calValue,uint8_t status = DATA_STATUS_VALID);
    bool SetGZManualValue(int rtuId, int dataId, short manualValue, uint8_t enable = 1);
    bool GetGZRealValue(int rtuId, int dataId, short& realValue, uint8_t& status);
    bool GetGZRealValue(int rtuId, int dataId, short& realValue,short& calValue, uint8_t& status);
    bool SetGZStatus(int rtuId, int dataId, uint8_t status);
    bool SetGZNoAlarm(int rtuId, int dataId, uint8_t enable = 1);
    bool ResetGZManualFlag(int rtuId, int dataId, uint8_t status = DATA_STATUS_VALID);

//    bool SetFiveAntiLock(int rtuId, int dataId, uint8_t enable = 1);
//    bool SetHangup(int rtuId, int dataId, uint8_t enable = 1);

    bool GetGZValues(QVector<T_SignalGZ>& vecGZ);
    bool InvalidateGZData(QVector<T_SignalGZ>& vecGZ);

    // 遥脉数据访问接口
    int SetYMRealValue(int rtuId, int dataId, double realValue, uint8_t status = DATA_STATUS_VALID);
    int SetYMRealValue(int rtuId, int dataId, double realValue, double calValue, uint8_t status = DATA_STATUS_VALID);
    bool SetYMManualValue(int rtuId, int dataId, double manualValue);
    bool GetYMRealValue(int rtuId, int dataId, double& realValue, uint8_t& status);
    bool GetYMRealValue(int rtuId, int dataId, double& realValue,double& calValue, uint8_t& status);
    bool SetYMStatus(int rtuId, int dataId, uint8_t status);
    bool SetYMNoAlarm(int rtuId, int dataId, uint8_t enable = 1);
    bool ResetYMManualFlag(int rtuId, int dataId, uint8_t status = DATA_STATUS_VALID);

    bool GetYMValues(QVector<T_Impulse>& vecYM);
    bool InvalidateYMData(QVector<T_Impulse>& vecYM);

    bool GetFepNodeStatus(int Id, uint8_t& status1, uint8_t& status2);
    bool SetFepNodeStatus(int Id, uint8_t status, bool bPrimary);
    bool GetFepChannelStatus(int Id, uint8_t& status1, uint8_t& status2);
    bool GetFepChannelStatus(VecFepChannel& vecFepChannel);
    bool SetFepChannelStatus(int Id, uint8_t status1, uint8_t status2);
    bool GetFepNodeStatus(VecFepNode& vecFepNode);

    ////静态函数版本发布相关函数(遥控遥调soe相关)
    static bool SendYK(REDIS_YK& yk, bool bAutoSetTimestamp = true);
    static bool SendYT(REDIS_YT& yt, bool bAutoSetTimestamp = true);
    static bool SendAdjustTime(REDIS_ADJUST_TIME& _time);

    static bool SendFEPSOE(REDIS_SOE& soe, bool bAutoSetTimestamp = true);

    static bool SendSOE(REDIS_SOE& soe, bool bAutoSetTimestamp = true);

    static bool SendSOEChannel(char *channel,REDIS_SOE& soe, bool bAutoSetTimestamp = true);

    static bool SendNetNodeSOE(REDIS_NETNODE_SOE& soe, bool bAutoSetTimestamp = true);
    //定值接口
//    int SetDZRealValue(int rtuId, int dataId, double realValue, uint8_t status = DATA_STATUS_VALID);
 //   bool GetDZRealValue(int rtuId, int dataId, double &realValue, unsigned char &status);
    //投退
 //   int SetTTRealValue(int rtuId, int dataId, short realValue, uint8_t status = DATA_STATUS_VALID);
 //   bool GetTTRealValue(int rtuId, int dataId, short& realValue, uint8_t& status);
    static bool SendDZ(REDIS_DZ &dz, bool bAutoSetTimestamp = true);
    static bool SendTT(REDIS_TT &tt, bool bAutoSetTimestamp = true);
    static bool SendGZSOE(REDIS_FAULT_SOE& soe, bool bAutoSetTimestamp = true);


    //类成员函数版本发布相关函数(遥控遥调soe相关)
    bool SendYKM(REDIS_YK& yk, bool bAutoSetTimestamp = true);
    bool SendYTM(REDIS_YT& yt, bool bAutoSetTimestamp = true);
    bool SendAdjustTimeM(REDIS_ADJUST_TIME& _time);

    bool SendFEPSOEM(REDIS_SOE& soe, bool bAutoSetTimestamp = true);

    bool SendSOEM(REDIS_SOE& soe, bool bAutoSetTimestamp = true);

    bool SendSOEChannelM(char *channel,REDIS_SOE& soe, bool bAutoSetTimestamp = true);

    bool SendNetNodeSOEM(REDIS_NETNODE_SOE& soe, bool bAutoSetTimestamp = true);
    //定值接口
    int SetDZRealValue(int rtuId, int dataId, double realValue, uint8_t status = DATA_STATUS_VALID);
    bool GetDZRealValue(int rtuId, int dataId, double &realValue, unsigned char &status);
    //投退
    int SetTTRealValue(int rtuId, int dataId, short realValue, uint8_t status = DATA_STATUS_VALID);
    bool GetTTRealValue(int rtuId, int dataId, short& realValue, uint8_t& status);
    bool SendDZM(REDIS_DZ &dz, bool bAutoSetTimestamp = true);
    bool SendTTM(REDIS_TT &tt, bool bAutoSetTimestamp = true);
    bool SendGZSOEM(REDIS_FAULT_SOE& soe, bool bAutoSetTimestamp = true);


 //   bool GetScadaActor();
 //   bool SetNetConfig(uint8_t status);
//    bool GetNodeFepStatus(int fepid, uint8_t &status_A, uint8_t &status_B, uint8_t bMaster);
    //获取节点的A网状态，B网状态，主备状态
    bool GetNodeFepStatus(int fepid, uint8_t &status_A, uint8_t &status_B, uint8_t &bMaster);
    //设置节点的A网状态，B网状态，主备状态,status_A status_A:1在线,0离线,bMaster:1主服务器,0备服务器
    bool SetNodeFepStatus(int fepid, uint8_t status_A, uint8_t status_B, uint8_t bMaster);
    //设置节点的bMaster:1主服务器,0备服务器
    bool SetNodeFepStatus(int fepid, uint8_t bMaster);

    //设置string生存时间
    bool SetStringTTL(char *skeys,int seconds);

    //设置string值
    bool SetStringValue(char *skeys,char *svalue);

    //获取string值
    bool GetStringValue(char *skeys,char *stringbuf,int buflen);

    //获取本地string值
    bool GetCurrStringValue(char *skeys,char *stringbuf,int buflen);

    //发送信息到队列，支持二进制数据
    bool SetQueueData(char *listkey,char *msg,int len);

    //从队列里取数据,listkey和SetQueueData里面的listkey配对
    bool GetQueueData(char *listkey,char *msg,int len,bool bblock=true,int blocktime = 0);

private:
    //type = 1本地发布，type = 2主备工作站都发布
    bool Publish(char* channel, char* msg, int len = 0,int type = 1);
    bool SoePublish(char* channel, char* msg, int len = 0);

private:
    RedisClient m_redisClient;
    VecNodeConfig m_listNodeConfig;
    RedisClient m_list_A_Redis[MAXREDISCLIENT];
    RedisClient m_list_B_Redis[MAXREDISCLIENT];
    uint8_t net_flag;

};

//Publish
class RedisPublisher
{
public:
    RedisPublisher();
    virtual ~RedisPublisher();

    void Publish(char* channel, char* msg, int len = 0);

private:
    RedisClient m_redisClient;
    RedisClient m_list_A_Redis[MAXREDISCLIENT];
    RedisClient m_list_B_Redis[MAXREDISCLIENT];
};


//subscribe

#define TYPE_SUBSCRIBE		0
#define TYPE_PSUBSCRIBE		1

#define EVENT_EXIT			    "exit"


typedef struct t_subscribeInfo
{
    int type;
    string channelName;
} T_SubscribeInfo;

typedef void(*msgCallbackFn)(char* channel, char* msg, int msglen, void* pridata);

class RedisSubscriber
{
public:
    RedisSubscriber();
    virtual ~RedisSubscriber();

    int Subscribe(T_SubscribeInfo& si);

    int UnSubscribe();

    bool Start();

    void Stop();

    void SetCallback(msgCallbackFn cb, void* p = 0);

private:

    static void* SubscribeProc(void* p);

public:
    RedisClient m_redisClient;
    bool m_bStop;

    pthread_t m_hThread;

    msgCallbackFn m_userCallback;
    void* pridata;

    long long int m_iStartCount;

private:
    vector <T_SubscribeInfo> m_vecChannels;

};

#endif // RDBI_H
