#include "redisclient.h"
#include <stdarg.h>

RedisClient::RedisClient():c(nullptr)
{
}

RedisClient::~RedisClient()
{
}

redisReply *RedisClient::SendRedisCmd(const char *format,...)
{

    if(c == nullptr)
    {
        return nullptr;
    }
    va_list ap;
    va_start(ap, format);
    redisReply *reply =  (redisReply *)redisvCommand(c, format, ap);
    va_end(ap);

    if (!reply)
    {
        if (c)
        {
            redisFree(c);
            c = nullptr;
        }
        Connect(m_host.toStdString().c_str());
    }
    else
    {
        m_bConnected = true;
    }

    return reply;
}

redisReply *RedisClient::SendRedisCmd(const char *format, va_list ap)
{

    if(c == nullptr)
    {
        return nullptr;
    }

    redisReply *reply =  (redisReply *)redisvCommand(c, format, ap);

    if (!reply)
    {
        if (c)
        {
            redisFree(c);
            c = nullptr;
        }
        Connect(m_host.toStdString().c_str());
    }
    else
    {
        m_bConnected = true;
    }

    return reply;
}

redisReply *RedisClient::SendRedisCmdArgv(int argc, const char **argv, const size_t *argvlen)
{

    if(c == nullptr)
    {
        return nullptr;
    }

    redisReply *reply = (redisReply *)redisCommandArgv(c, argc, argv, argvlen);
    if (!reply)
    {
        if (c)
        {
            redisFree(c);
            c = nullptr;
        }
        Connect(m_host.toStdString().c_str());
    }
    else
    {
        m_bConnected = true;
    }

    return reply;
}

int RedisClient::AppendCommand(const char *format, ...)
{

    if(c == nullptr)
    {
        return 0;
    }

    va_list ap;
    va_start(ap, format);
    int ret = redisvAppendCommand(c, format, ap);
    va_end(ap);

    return (ret == REDIS_OK) ? 1 : 0;
}

int RedisClient::AppendCommand(const char *format, va_list ap)
{

    if(c == nullptr)
    {
        return 0;
    }

    int ret = redisvAppendCommand(c, format, ap);

    return (ret == REDIS_OK) ? 1 : 0;
}

bool RedisClient::Connect(const char *host, int port, int timeout,int type)
{
    struct timeval tv;
    tv.tv_sec = timeout / 1000;
    tv.tv_usec = (timeout % 1000)*1000;

    Disconnect();

    if (host)
    {
        c = redisConnectWithTimeout(host, port,tv);
    }
    else
    {
        m_bConnected = false;
        return false;
    }

    if (c == nullptr || c->err)
    {
        if (c)
        {
            printf("Connection error: %s\n", c->errstr);
            redisFree(c);
            c = nullptr;
        }
        else
        {
            printf("Connection error: can't allocate redis context\n");
        }
        m_bConnected = false;
    }
    else
    {
        m_bConnected = true;
        redisEnableKeepAlive(c);
        if(type == 0)
        {
            struct timeval tvcmd;
            tvcmd.tv_sec = 0;
            tvcmd.tv_usec = 100*1000;
            redisSetTimeout(c,tvcmd);
        }

    }

    return m_bConnected;
}

void RedisClient::Disconnect()
{
    if (c)
    {
        redisFree(c);
    }

    c = nullptr;

    m_bConnected = false;
}

bool RedisClient::IsConnected()
{
    //return m_bConnected;
    if(c)
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool RedisClient::QuitRedis()
{
    bool ret = false;

    redisReply* reply = SendRedisCmd("QUIT");

    if (reply && reply->type == REDIS_REPLY_STATUS)
    {
        c = nullptr;
        m_bConnected = false;
        ret = true;
    }

    FreeReply(reply);

    return ret;
}

int RedisClient::GetReply(void **reply)
{
    if (!IsConnected())
    {
        return REDIS_ERR;
    }

    if(c == nullptr)
    {
        return 0;
    }

    int ret = redisGetReply(c, reply);
    if (ret == REDIS_ERR)
    {
        if (c)
        {
            redisFree(c);
            c = nullptr;
        }
        Connect(m_host.toStdString().c_str());
    }
    else
    {
        m_bConnected = true ;
    }

    return ret;
}

void RedisClient::FreeReply(void *reply)
{
    if (reply)
    {
        freeReplyObject(reply);
    }

    reply = nullptr;
}
