// RTDB_DEF.h: definition for the realtime data structs.
//
//////////////////////////////////////////////////////////////////////

#ifndef RTDB_DEF_H
#define RTDB_DEF_H

#if defined (__cplusplus)
#include <paramdb.h>
#include <QtCore/QHash>

#endif
#include <stdint.h>
#include <QDateTime>

//////////////////////////////////////////////////////////////////////
// 实时数据定义
//////////////////////////////////////////////////////////////////////

#pragma pack(1)

#define DATA_STATUS_INVALID	0x00 //无效
#define DATA_STATUS_VALID	0x01 //有效

// 遥信实时数据
typedef struct redis_yxdata
{
    int rtuId;                  // 装置 id
    int dataId;                 // 装置内序号

    unsigned short realValue;       // 实时值
    unsigned short manualValue;     // 人工置数值

    unsigned char manualFlag;       // 人工置数标志
    int64_t 	manualTime;         // 人工置数开始时间
    unsigned char status;           // 数据质量戳

    unsigned char noAlarm;          // 告警抑制
    int64_t updateTime;             // 最新来数时间

    unsigned char fiveAntilock;     // 五防标志
    unsigned char hangup;           // 挂牌标志
    int64_t  hangupTime;            // 挂牌开始时间

    int64_t alarmTime;              // 最近一次告警时间
    unsigned int onAlarmNum;        // 合告警次数
    unsigned int offAlarmNum;       // 分告警次数

} REDIS_YXDATA, *PREDIS_YXDATA;


// 遥测实时数据
typedef struct redis_ycdata
{
    int rtuId;                  // 装置 id
    int dataId;                 // 装置内序号

    double realValue;               // 实时值
    double manualValue;             // 人工置数值

    unsigned char manualFlag;       // 人工置数标志
    int64_t manualTime;             // 人工置数开始时间
    unsigned char status;           // 数据质量戳

    unsigned char noAlarm;          // 告警抑制
    int64_t updateTime;             // 最新来数时间

    double maxValue;                // 最大值
    int64_t maxValueTime;           // 最大值发生时间
    double minValue;                // 最小值
    int64_t minValueTime;           // 最小值发送时间

    unsigned int overUplimitNum;    // 越上限次数
    int64_t overUplimitTime;        // 最近一次越上限时间
    unsigned char overUplimitFlag;  // 越上限标志

    unsigned int overDownlimitNum;  // 越下限次数
    int64_t overDownlimitTime;      // 最近一次越下限时间
    unsigned char overDownlimitFlag;         // 越下限标志

} REDIS_YCDATA, *PREDIS_YCDATA;


// 遥脉实时数据
typedef struct redis_ymdata
{
    int rtuId;                  // 装置 id
    int dataId;                 // 装置内序号

    double realValue;               // 实时值
    double manualValue;             // 人工置数值

    unsigned char manualFlag;       // 人工置数标志
    int64_t manualTime;             // 人工置数开始时间
    unsigned char status;           // 数据质量戳

    unsigned char noAlarm;          // 告警抑制
    int64_t updateTime;             // 最新来数时间

    unsigned int overRangeNum;      // 越量程次数
    unsigned char overRangeFlag;    // 越量程标志
    int64_t overRangeTime;          // 最近一次越量程时间

} REDIS_YMDATA, *PREDIS_YMDATA;



// 频道名称
#define EVENT_SOE_FEP		    "soe:fep"           // 前置机产生的SOE
#define EVENT_SOE_SCADA	        "soe:scada"         // 由实时计算模块产生的SOE
#define EVENT_NETNODE_SOE       "netnode_soe:scada" // 网络节点SOE
#define EVENT_RC_SCADA_YK		"rc:scada:yk"		// 主控平台发送的遥控命令
#define EVENT_RC_SCADA_YT		"rc:scada:yt"		// 主控平台发送的遥调命令
#define EVENT_RC_SCADA_TIME		"rc:scada:time"		// 主控平台发送的遥控命令
#define EVENT_RC_FEP_YK			"rc:fep:yk"			// 前置机发送的遥控响应
#define EVENT_RC_FEP_YT			"rc:fep:yt"			// 前置机发送的遥调响应
#define EVENT_RC_FEP_TIME		"rc:fep:time"		// 前置机发送的对时响应

#define EVENT_SOE_SCADA	        "soe:scada"         // 由实时产生的SOE
#define EVENT_GZSOE_SCADA	        "gzsoe:scada"         // 由实时产生的GZSOE
#define EVENT_RC_SCADA_YK		"rc:scada:yk"		// 主控平台发送的遥控命令
#define EVENT_RC_SCADA_YT		"rc:scada:yt"		// 主控平台发送的遥调命令
#define EVENT_RC_SCADA_TIME		"rc:scada:time"		// 主控平台发送的遥控命令
#define EVENT_DZ_ORDER                  "rc:dz:order"    //定值设置
#define EVENT_TT_ORDER                  "rc:tt:order"   //投退设置

#define SOE_UNKNOW		0x00        //未定义的SOE类型

//SOE
#define SOE_YX			0x01		//遥信SOE
#define SOE_COMM		0x02		//装置通讯SOE
#define SOE_SOFT_YX_OPENTOCLOSE 0x21           //
#define SOE_SOFT_YX_CLOSETOOPEN 0x22



//GZSOE

#define SOE_YC_U		0x03    	//遥测越上限报警复归

//#define SOE_YC_UU		0x04		//遥测越上上限报警复归
#define SOE_YC_D		0x05		//遥测越下限报警复归
//#define SOE_YC_DD		0x06	    //遥测越下下限报警复归
//#define SOE_XB_U		0x07	    //谐波越上限报警复归
//#define SOE_XB_D		0x08		//谐波越下限报警复归
#define SOE_YM_OVER		0x09        //遥脉超量程报警复归
//#define SOE_FAULT		0x0A		//故障SOE

#define SOE_YC_U_ACT		0x83    	//遥测越上限报警
//#define SOE_YC_UU_ACT		0x84		//遥测越上上限报警
#define SOE_YC_D_ACT		0x85		//遥测越下限报警
//#define SOE_YC_DD_ACT		0x86	    //遥测越下下限报警
//#define SOE_XB_U_ACT		0x87	    //谐波越上限报警
//#define SOE_XB_D_ACT		0x88		//谐波越下限报警
#define SOE_YM_OVER_ACT		0x89        //遥脉超量程报警


#define GZ_SOE_GZSOE    0x0F
#define GZ_SOE_YK    0x10
#define GZ_SOE_YT    0x11
#define GZ_SOE_YC_U_ACT    0x12
#define GZ_SOE_YC_U    0x13
#define GZ_SOE_YC_D_ACT    0x14
#define GZ_SOE_YC_D   0x15

// SOE
typedef struct redis_soe
{
    int deviceId;
    int dataId;             //数据id
    unsigned char type;     //SOE类型,参见SOE类型宏定义
    double val;             //SOE值
    int64_t timestamp;

} REDIS_SOE, *PREDIS_SOE;
// SOE
typedef struct redis_fault_soe
{
    int deviceId;
    int dataId;             //数据id
    unsigned char type;     //SOE类型,参见SOE类型宏定义
    double val;             //SOE值
    char ExternData[255];    //故障记录示軪
    int64_t timestamp;

} REDIS_FAULT_SOE, *PREDIS_FAULT_SOE;

#define NET_NODE_STATUS_DISCONNCET		0x00 // 通讯异常
#define NET_NODE_STATUS_CONNCET			0x01 // 通讯正常

#define NET_NODE_SOE_CONNCET			0x0A // 主机通讯正常
#define NET_NODE_SOE_CONNCET2			0x0B // 备机通讯正常
#define NET_NODE_SOE_DISCONNCET			0x8A // 主机通讯异常
#define NET_NODE_SOE_DISCONNCET2		0x8B // 备机通讯异常

#define NET_NODE_TYPE_SCADA				0x00 // SCADA节点类型
#define NET_NODE_TYPE_FEP    			0x01 // FEP节点类型

typedef struct redis_netnode_soe
{
    unsigned char type;         // SOE类型

    unsigned char nodeType;     // 网络节类型
    int nodeId;	                // 网络节点Id

    int64_t timestamp;          // 时间戳

} REDIS_NETNODE_SOE, *PREDIS_NETNODE_SOE;


//命令类型
#define RC_CMD_UNKNOW			0x00	//未知
#define RC_CMD_YK				0x01    //遥控
#define RC_CMD_YT				0x02    //遥调
#define RC_CMD_DZ				0x03    //定值
#define RC_CMD_TT				0x04    //投退
#define RC_CMD_TIME				0x05    //对时
#define RC_CMD_YC_MDS			0x06	//遥测人工置数
#define RC_CMD_YX_MDS			0x07	//遥信人工置数
#define RC_CMD_YM_MDS			0x08	//遥脉人工置数
#define	RC_CMD_GZ_MDS			0x09    //故障人工置数
#define RC_CMD_HANDUP			0x0A	//挂牌
#define RC_CMD_CTRL_AUTHORITY	0x0B    //控制权限切换


//命令操作类型
#define RC_OPERATE_SELECT  0x01 //预置
#define RC_OPERATE_EXCUTE  0x03 //执行

//命令值类型
#define RC_VALUE_UNKOWN     0x00	//未知命令值类型
#define RC_VALUE_DOUBLE		0x01	//double 类型
#define RC_VALUE_INT		0x02	//int 类型
#define RC_VALUE_UCHAR		0x03	//unsigned char类型

//命令状态
#define RC_STATUS_UNKWON      0x00          //未知的命令状态
#define RC_STATUS_WAIT		  0x01			//命令等待提取
#define RC_STATUS_PROCESSING  0x02			//命令正在被处理
#define RC_STATUS_SUCCESS	  0x03			//命令成功
#define RC_STATUS_FAIL        0x04			//命令失败
#define RC_STATUS_BLOCK       0x05			//命令被闭锁
#define RC_STATUS_PARAM_ERROR 0x06			//命令参数错误
#define RC_STATUS_OVER_TIME   0x07			//命令超时

// 遥控命令
typedef struct redis_yk
{
    int deviceId;
    int dataId;             //数据id
    unsigned char operaterType; // 操作类型

    unsigned short val;			// 命令值
    unsigned char status;       // 命令状态

    int64_t timestamp;

} REDIS_YK, *PREDIS_YK;

// 定值命令
typedef struct redis_dz
{
    int deviceId;
    int dataId;             //数据id
    unsigned char operaterType; // 操作类型

    double val;			// 命令值
    unsigned char status;       // 命令状态

    int64_t timestamp;

} REDIS_DZ, *PREDIS_DZ;

// 定值命令
typedef struct redis_tt
{
    int deviceId;
    int dataId;             //数据id
    unsigned char operaterType; // 操作类型

    double val;			// 命令值
    unsigned char status;       // 命令状态

    int64_t timestamp;

} REDIS_TT, *PREDIS_TT;
// 遥调命令
typedef struct redis_yt
{
    int deviceId;
    int dataId;             //数据id
    double val;					// 命令值
    unsigned char status;       // 命令状态

    int64_t timestamp;

} REDIS_YT, *PREDIS_YT;

// 对时命令
typedef struct redis_adjust_time
{
    unsigned short Actor;//1-actor-A-adjtime
    int reserved;
    QDateTime dateTime;

} REDIS_ADJUST_TIME, *PREDIS_ADJUST_TIME;

#if defined (__cplusplus)

#define T_DigitalParam      RTUYX
#define T_AnalogParam       RTUYC
#define T_ImpulseParam      RTUYM

#define T_DigitalRtData     REDIS_YXDATA
#define T_AnalogRtData      REDIS_YCDATA
#define T_ImpulseRtData     REDIS_YMDATA

// RTU
typedef QHash<int, StationPara> HashRTU;


// Scada节点结构
typedef struct t_scadaNode
{
    SCADANode param;          // 参数数据

    unsigned char status;            // 前置机节点状态
    int64_t lastOnlineTime;

} T_ScadaNode;

typedef QVector<T_ScadaNode> VecScadaNode;
typedef QHash<int, T_ScadaNode> HashScadaNode;


// 前置机节点结构
typedef struct t_fepNode
{
    FEPNode param;          // 参数数据

    unsigned char status1;            // 主前置机节点状态
    int64_t lastOnlineTime1;

    unsigned char status2;            // 备前置机节点状态
    int64_t lastOnlineTime2;

} T_FepNode;

typedef QVector<T_FepNode> VecFepNode;
typedef QHash<int, T_FepNode> HashFepNode;


// 前置机采集通道结构
typedef struct t_fepChannel
{
    FEPChannel param;       // 参数数据

    unsigned char status1;            // 主通道状态
    int64_t lastOnlineTime1;

    unsigned char status2;            // 备通道状态
    int64_t lastOnlineTime2;

} T_FepChannel;

typedef QVector<T_FepChannel> VecFepChannel;
typedef QHash<int, T_FepChannel> HashFepChannel;


// 遥信数据结构
typedef struct t_digital
{
    para_yx param;   // 参数数据
    T_DigitalRtData data;   // 实时数据

} T_Digital;


// 遥测数据结构
typedef struct t_analog
{
    para_yc param;   // 参数数据
    T_AnalogRtData data;   // 实时数据

} T_Analog;

// 遥脉数据结构
typedef struct ym_data
{
    para_ym param;   // 参数数据
    T_ImpulseRtData data;   // 实时数据

} T_Impulse;

// 谐波数据结构
typedef struct xb_data
{
    para_xb param;   // 参数数据
    T_AnalogRtData data;   // 实时数据

} T_SignalXB;

// 故障数据结构
typedef struct gz_data
{
    para_gz param;   // 参数数据
    T_DigitalRtData data;   // 实时数据

} T_SignalGZ;

typedef QVector<T_Digital>		VecDigital;
typedef QVector<T_Analog>		VecAnalog;
typedef QVector<T_Impulse>		VecImpulse;

typedef QVector<T_SignalXB>		VecSignalXB;
typedef QVector<T_SignalGZ>		VecSignalGZ;


typedef QHash<int, T_Digital>	HashDigital;
typedef QHash<int, T_Analog>	HashAnalog;
typedef QHash<int, T_Impulse>	HashImpulse;

typedef QHash<int, T_SignalXB>	HashSignalXB;
typedef QHash<int, T_SignalGZ>	HashSignalGZ;

#endif

#pragma pack()


#endif
