﻿#ifndef STRUCTITEM_H
#define STRUCTITEM_H

#include <QMetaType>
#include <QtCore/QVector>

#define DATA_TEM_YC     0    //模板-遥测
#define DATA_TEM_YM	1    //模板-遥脉
#define DATA_TEM_YX	2    //模板-遥信
#define DATA_TEM_GZ	3    //模板-故障
#define DATA_TEM_XB     4    //模板-谐波
#define DATA_TEM_YK	5    //模板-遥控
#define DATA_TEM_YT     6    //模板-遥调
typedef enum{
    S_RED,
    S_BLUE,
    S_YELLOW,
} ItemStatus;

struct ItemData{
    QString name;
    QString tel;
};
Q_DECLARE_METATYPE(ItemData)


//站表(para_station)
struct StationPara{
    int  ID;
    char Name[200];
    int NodeID;
};
struct StationInfo
{
    int stationID;
    char IP1[50];
    char IP2[50];
    int Port;
    int AdjTime;

};
//装置表(PARA _DEVICE)
struct BoxPara                   //装置参数数据结构
{
    int   BoxID;                 //装置ID
    int   BoxTempletID;          //装置模板ID
    int   CommID;                //通讯设备ID
    int   StationID;             //站ID
    int   BoxAddress;            //装置地址
    char  BoxName[100];           //装置名称
    int   BoxPT;                 //PT变比
    int   BoxCT;                 //CT变比
    bool  ChangeAlarm;           //变位报警否
};
//通道表(para_channel)
struct CommPara                  //通讯设备参数数据结构
{
    int   CommID;                //通讯设备ID
    int   CommAddress;           //通讯设备地址
    char  CommName[50];          //通讯设备名称
    char  CommType[50];          //通讯设备类型
    char  CommData[50];          //通讯设备参数
    char  CommDescribe[100];     //通讯设备描述
    int   NodeID;                //节点ID
    int   ProtocolID;                  //规约ID
};
//通道驱动表(para_protocol)
struct ProtocolPara
{
    int  ID;             //通讯规约ID
    char Name[100];       //通讯规约名称
    char Version[50];    //通讯规约版本
    char Author[20];
};
//遥测量表（para_yc）
struct para_yc
{
    int ID;             //遥测序号
    int DeviceID;       //装置ID
    int DeviceIndex;    //装置内序号
    char Name[100];           //名称
    char ChartType[50]; //特征字
    char Unit[10];      //单位
    double Rate;        //变比
    double Offset;      //偏移
    double DeadRange;   //死区
    bool Locked; //封锁位
    bool Manual; //人工置数
    bool HighChecked; //越上限报警允许
    double HighCheckedValue;  //越上限报警值
    bool HHighChecked;//越上上限报警允许
    double HHighCheckedValue; //越上上限报警值
    bool LowChecked;  //越下限报警允许
    double LowCheckedValue;   //越下限报警值
    bool LLowChecked; //越下下限报警允许
    double LLowCheckedValue;  //越下下限报警值
    bool IsCalcued;   //计算量否
    char CalcuFormula[500];   //计算公式
    int YXID;                 //相关遥信ID
    bool AutoCall;    //语音寻呼
    double CollectVal;        //采样值
    double RtdbVal;           //实时值
};
//遥脉量表（para_ym）
struct para_ym
{
    int ID;             //遥测序号
    int DeviceID;       //装置ID
    int DeviceIndex;    //装置内序号
    char Name[100];           //名称
    char ChartType[50]; //特征字
    char Unit[10];      //单位
    double Rate;        //变比
    double Offset;      //偏移
    double DeadRange;   //死区
    bool Locked; //封锁位
    bool Manual; //人工置数
    bool HighChecked; //越上限报警允许
    double HighCheckedValue;  //越上限报警值
    bool HHighChecked;//越上上限报警允许
    double HHighCheckedValue; //越上上限报警值
    bool LowChecked;  //越下限报警允许
    double LowCheckedValue;   //越下限报警值
    bool LLowChecked; //越下下限报警允许
    double LLowCheckedValue;  //越下下限报警值
    bool IsCalcued;   //计算量否
    char CalcuFormula[500];   //计算公式
    int YXID;                 //相关遥信ID
    bool AutoCall;    //语音寻呼
    double CollectVal;        //采样值
    double RtdbVal;           //实时值
};

//谐波量表（para_xb）
struct para_xb
{
    int ID;             //遥测序号
    int DeviceID;       //装置ID
    int DeviceIndex;    //装置内序号
    char Name[100];           //名称
    char ChartType[50]; //特征字
    char Unit[10];      //单位
    double Rate;        //变比
    double Offset;      //偏移
    double DeadRange;   //死区
    bool Locked; //封锁位
    bool Manual; //人工置数
    bool HighChecked; //越上限报警允许
    double HighCheckedValue;  //越上限报警值
    bool HHighChecked;//越上上限报警允许
    double HHighCheckedValue; //越上上限报警值
    bool LowChecked;  //越下限报警允许
    double LowCheckedValue;   //越下限报警值
    bool LLowChecked; //越下下限报警允许
    double LLowCheckedValue;  //越下下限报警值
    bool IsCalcued;   //计算量否
    char CalcuFormula[500];   //计算公式
    int YXID;                 //相关遥信ID
    bool AutoCall;    //语音寻呼
    double CollectVal;        //采样值
    double RtdbVal;           //实时值
};

//遥信量表（para_yx）
struct para_yx
{
    int ID;             //遥测序号
    int DeviceID;       //装置ID
    int DeviceIndex;    //装置内序号
    char Name[100];           //名称
    char ChartType[50]; //特征字
    bool Locked; //封锁位
    bool Manual; //人工置数
    bool CloseAlarm; //分到合报警否
    bool OpenAlarm;//合到分报警否
    bool Reverse;  //反相
    char OpenName[20];   //分位名称
    char CloseName[20];   //合位名称
    bool IsCalcued;//计算量否
    char CalcuFormula[200];//计算公式
    int ChangeTimes;       //变位次数
    bool ChangeAutoAlarm;//变位时自动报警
    bool AutoCall;    //语音寻呼
    double CollectVal;        //采样值
    double RtdbVal;           //实时值
    int Status;
};

//故障量表（para_gz）
struct para_gz
{
    int ID;             //遥测序号
    int DeviceID;       //装置ID
    int DeviceIndex;    //装置内序号
    char Name[100];           //名称
    char ChartType[50]; //特征字
    bool Locked; //封锁位
    bool Manual; //人工置数
    bool CloseAlarm; //分到合报警否
    bool OpenAlarm;//合到分报警否
    bool Reverse;  //反相
    char OpenName[20];   //分位名称
    char CloseName[20];   //合位名称
    bool IsCalcued;//计算量否
    char CalcuFormula[200];//计算公式
    int ChangeTimes;       //变位次数
    bool ChangeAutoAlarm;//变位时自动报警
    bool AutoCall;    //语音寻呼
    double CollectVal;        //采样值
    double RtdbVal;           //实时值
    int Status;
};

//遥控量表（para_yk）
struct para_yk
{
    int ID;             //遥测序号
    int DeviceID;       //装置ID
    int DeviceIndex;    //装置内序号
    char Name[100];           //名称
    char ChartType[50]; //特征字
    int ReturnID; //遥控返回点ID
    int ResultTime; //结果判定时间,以秒为单位
    double HightVal;//上限值
    double LowVal;  //下限值
    double Xs;      //系数，默认：1.0
    char CRC[50];   //校验编号
    double CollectVal;//采样值
    double RtdbVal;  //实时值
    int Status;     //品质状态
};

//遥控量表（para_yt）
struct para_yt
{
    int ID;             //遥测序号
    int DeviceID;       //装置ID
    int DeviceIndex;    //装置内序号
    char Name[100];           //名称
    char ChartType[50]; //特征字
    int ReturnID; //遥调返回点ID
    int ResultTime; //结果判定时间,以秒为单位
    double HightVal;//上限值
    double LowVal;  //下限值
    double Xs;      //系数，默认：1.0
    char CRC[50];   //校验编号
    double CollectVal;//采样值
    double RtdbVal;  //实时值
    int Status;     //品质状态
};
//装置模板表(para_templete_device)
struct BoxTemlete{
     int TemleteID;
     char TemleteName[100];
     char version[20];//版本
     char marker[50];

};
//遥测、遥脉、遥信、故障、谐波量模板表（PARA_TEMPLETE_YC  PARA_TEMPLETE_YM   PARA_TEMPLETE_YX   PARA_TEMPLETE_GZ   PARA_TEMPLETE_XB）
struct BoxTemleteDataPara{
     int TemleteID;//装置模板ID
     int TempletIndex;//装置模板内序号
     char CharType[50];//特征字
     char Name[100];//名称
     char Unit[10];//单位
     double Xs;    //系数
     double CollectVal;//采用值
     double RtdbVal;   //实时值
};
//定值模板表(PARA _ TEMPLETE _DZ)
struct BoxTemleteDZPara{
    int TemleteID;//装置模板ID
    int TempletIndex;//装置模板内序号
    char Type[50];//类型
    char Name[100];//名称
    char Unit[10];//单位
    double HighValue;
    double MiddleValue;
    double LowValue;
    double Xs;    //系数
    double CollectVal;//采用值
    double RtdbVal;   //实时值
};
//定值投退表(PARA _ TEMPLETE _TT)
struct BoxTemleteTTPara{
    int TemleteID;//装置模板ID
    int TempletIndex;//装置模板内序号
    char Name[100];//名称
    char Unit[10];//单位
    double CollectVal;//采用值
    double RtdbVal;   //实时值
};
//用户表(para_user)
struct para_user{
   int UserID;
   char Name[50];
   char Password[255];
   char Tel1[50];
   char Tel2[50];
   int  GroupID;
   char Msg1[200];
   char Msg2[200];
   char Remark[200];
};
//用户表(para_user)
struct para_usergroup{
    int GroupID;
    char Name[50];
    int ControlAuth;//控制权限Bit0:监视权限,Bit1:遥控遥调控制权限,bit2:维护权限,Bit3:编辑操作
    int EditAuth;//编辑权限
    int ExtendAuth1;//扩展权限1
    int ExtendAuth2;//扩展权限2
    int ExtendAuth3;//扩展权限3
    char Remark[200];

};
enum SCADARoleType {
    MASTER_APPLICATION_SERVER =1, //主应用服务器
    SLAVE_APPLICATION_SERVER, //备应用服务器
    APPLICATION_DATABASE_MASTER_SERVER,//应用服务器兼数据库服务器主
    APPLICATION_DATABASE_LAVE_SERVER,//应用服务器兼数据库服务器备
    FRONT_END_MACHINE,//前置机
    WORKSTATION//工作站
};
struct NodeConfig {
    int id;                 // 节点ID
    char node_name[151];    // 节点名称，数据库中node_name字段定义为VARCHAR(150)
    int station_id;         // 站ID，数据库中station_id字段定义为INT
    char node_ip1[51];      // 节点IP1，数据库中node_ip1字段定义为VARCHAR(50)
    char node_ip2[51];      // 节点IP2，数据库中node_ip2字段定义为VARCHAR(50)
    int node_type;          // 节点类型，1.主应用服务器、2.备应用服务器、3.应用服务器兼数据库服务器主、4、应用服务器兼数据库服务器备、5.前置机、6、工作站
    char ext1[256];         // 备用字段，
    char ext2[256];         // 备用字段，

};
struct SystemParameters
{
    int id;
    int remote_control_time;
    int curve_save_interval;
    int report_save_interval;
    int running_days;
    char ext1[256];
    char ext2[256];
    char ext3[256];
    char ext4[256];
    char ext5[256];
    char ext6[256];
    char ext7[256];
    char ext8[256];
};


typedef struct _BoxCnfgPara
{
  qint32      iBoxID;                   //装置ID
  quint32    dAddr;                    //装置站号
  quint16     wBoxType;                 //装置类型
  quint16     wYcNum;                   //遥测数量
  quint16     wYxNum;                   //遥信数量
  quint16     wYmNum;                   //遥脉数量
  quint16     wGzNum;                   //故障数量
  quint16     wXbNum;                   //谐波数量
}_BoxCnfgPara;

typedef struct _CnfgFilePara
{
//  BYTE          nChannelID;
    quint8          nChannelID;        //by zfl 20190713
    quint8          nChannelSign;
    quint8          nChannelType;
    quint16          wComID;
    quint8          nChannelPara[10];    //
    quint16          wProtocolID;
    quint8          nBoxNum;
  _BoxCnfgPara  BoxPara[32];
}_CnfgFilePara;

struct _CnfPara
{

        qint16  StationID;			//调度站ID
        char StationName[48];		//调度站名称
//	char StationType[48];		//调度站类型
        int  StationTypeID;
        qint16 StationAddr;			//调度站地址
        char  DevicePara1[48];		//站设备参数1
        qint16 DeviceAddr1;			//站设备地址1
        char  DevicePara2[48];		//站设备参数2
        qint16 DeviceAddr2;			//站设备地址2
        qint16  BoxID;               //调度站装置ID
        char CommMode[48];			//通讯方式
        qint16 ParaCount;				//站参数个数
        qint16 CommDeviceID;			//通讯设备ID
        char ConfFileName[48];		//生成的配置文件名


        qint16        wYCNum;                   //远动遥测数量
        qint16		wYXNum;                   //远动遥信数量
        qint16		wYMNum;                   //远动遥脉数量
        qint16		wYKNum;                   //远动遥控数量
        qint16        wDZNum;                   //远动定值数量
        qint16        wTTNum;                   //远动投退数量
        qint16        wYTNum;                   //远动遥调数量

};
struct  StationParaSet
{
        int  ParaSn;
        char ParaName[48];
        char ParaValue[48];
};
struct DDStationOldPara
{
        int  StationID;				//调度站ID
        char StationName[50];		//调度站名称
        char StationType[50];		//调度站类型
        short StationAddr;			//调度站地址
        char  DevicePara1[50];		//站设备参数1
        short DeviceAddr1;			//站设备地址1
        char  DevicePara2[50];		//站设备参数2
        short DeviceAddr2;			//站设备地址2
        bool SendYc;				//是否传送一次值
//	CArray<StationParaSet,StationParaSet&> para;
};


//调度遥测数据对照表
struct DDYCPara
{
        qint16	StationID;				//调度站ID
        qint16	SNSend;					//发送序列号
        char	DataResource[20];		//数据源
        char	Type[20];				//特征字
        float	Xs;						//系数
//	char	Unit[50];				//单位
        qint16	DataTableID;			//数据表ID
        qint16	BoxID;					//装置ID
        int		BoxIndex;				//装置内序号
//	char	BName[256];				//别名
//	char	Extension1[256];		//扩展数据1
        int		Extension2;				//扩展数据2
        float	Extension3;				//扩展数据3
        float	DeadRange;				//死区值
};

//调度遥信数据对照表
struct DDYXPara
{
        qint16	StationID;				//调度站ID
        qint16	SNSend;					//发送序列号
        char	DataResource[20];		//数据源
        char	Type[20];				//特征字
        qint16	DataTableID;			//数据表ID
        qint16	BoxID;					//装置ID
        int		BoxIndex;				//装置内序号
//	char	BName[256];				//别名
//	char	Extension1[256];		//扩展数据1
        int	Extension2;				//扩展数据2
        float	Extension3;				//扩展数据3
        bool	GZReverse;				//反相

};

//调度遥脉数据对照表
struct DDYMPara
{
        qint16	StationID;				//调度站ID
        qint16	SNSend;					//发送序列号
        char	DataResource[20];		//数据源-
        char	Type[20];				//特征字
        float	Xs;						//系数
//	char	Unit[50];				//单位
        qint16	DataTableID;			//数据表ID
        qint16	BoxID;					//装置ID
        int		BoxIndex;				//装置内序号
//	char	BName[256];				//别名
//	char	Extension1[256];		//扩展数据1
        int		Extension2;				//扩展数据2
        float	Extension3;				//扩展数据3
        float	DeadRange;				//死区值
};

//调度遥控数据对照表
struct DDYKPara
{
        qint16	StationID;				//调度站ID
        qint16	SNSend;					//发送序列号
        char	DataResource[20];		//数据源
        qint16	OnDataTableID;			//合闸数据表ID
        qint16	OnBoxID;				//合闸装置ID
        int		OnBoxIndex;				//合闸装置内出口号
        qint16	OffDataTableID;			//分闸数据表ID
        qint16	OffBoxID;				//分闸装置ID
        int		OffBoxIndex;			//分闸装置内出口号
        bool	YKLocked;				//远方控制允许
        char	YKControlType[50];		//控制权限
        char	YKConMaskWord[100];		//远方控制屏蔽字
//	char	BName[256];				//别名
//	char	Extension1[256];		//扩展数据1
        int		Extension2;				//扩展数据2
        float	Extension3;				//扩展数据3
};

//调度遥调数据对照表
struct DDYTPara
{
        qint16	StationID;				//调度站ID
        qint16	SNSend;					//发送序列号
        char	DataResource[20];		//数据源
        char	Type[20];				//特征字
        float	Xs;						//系数
        qint16	DataTableID;			//数据表ID
        qint16	BoxID;					//装置ID
        int		BoxIndex;				//装置内序号
//	char	BName[256];				//别名
//	char	Extension1[256];		//扩展数据1
        int		Extension2;				//扩展数据2
        float	Extension3;				//扩展数据3
};

//调度谐波数据对照表
struct DDXBPara
{
        qint16	StationID;				//调度站ID
        qint16	SNSend;					//发送序列号
        char	DataResource[20];		//数据源
        char	Type[20];				//特征字
        float	Xs;						//系数
//	char	Unit[50];				//单位
        qint16	DataTableID;			//数据表ID
        qint16	BoxID;					//装置ID
        int		BoxIndex;				//装置内序号
//	char	BName[256];				//别名
//	char	Extension1[256];		//扩展数据1
        int		Extension2;				//扩展数据2
        float	Extension3;				//扩展数据3
        float	DeadRange;				//死区值
};


//调度故障数据对照表
struct DDGZPara
{
        qint16	StationID;				//调度站ID
        qint16	SNSend;					//发送序号
        char	DataResource[20];		//数据源
        char	Type[20];				//特征字
        qint16	DataTableID;			//数据表ID
        qint16	BoxID;					//装置ID
        int		BoxIndex;				//装置内序号
//	char	BName[256];				//别名
//	char	Extension1[256];		//扩展数据1
        int		Extension2;				//扩展数据2
        float	Extension3;				//扩展数据3
        bool	GZReverse;				//反相
};
//struct SCADANode
//{
//        unsigned int    id;                     /*id*/
//        unsigned int    actor;                   /*节点角色类型*/
//        char			hostName[256];                   /*名称*/

//        char			netAIP[128];	/*A网IP地址*/
//        char			netBIP[128];	/*B网IP地址*/
//};
//// 前置机节点结构
///*==============================================================*/
///*前置机采集通道                                            */
///*==============================================================*/

//struct FEPChannel
//{
//        unsigned int    id;                         /*id*/
//        char			name[128];                        /*名称*/

//        unsigned int	fepId; //前置机id
//        unsigned int	rtuId; //需要映射数据的厂站id


//        unsigned int	rtuYCBeginId; //厂站遥测表开始id
//        unsigned int	rtuYCEndId; //厂站遥测表结束id

//        unsigned int	rtuYXBeginId; //厂站遥信表开始id
//        unsigned int	rtuYXEndId; //厂站遥信表结束id

//        unsigned int	rtuYTBeginId; //厂站遥调表开始id
//        unsigned int	rtuYTEndId; //厂站遥调表结束id

//        unsigned int	rtuYKBeginId; //厂站遥控表开始id
//        unsigned int	rtuYKEndId; //厂站遥控表结束id


//        unsigned int	rtuYMBeginId; //厂站遥脉表开始id
//        unsigned int	rtuYMEndId; //厂站遥脉表结束id

//};

//struct FEPNode
//{
//        unsigned int        id;                     /*id*/

//        char name[256];		//前置机名称

//        char hostName1[128];//主前置机计算机名称
//        char netA1IP[128];//主前置机A网IP地址
//        char netB1IP[128];//主前置机B网IP地址

//        char hostName2[128];//备前置机计算机名称
//        char netA2IP[128];//备前置机A网IP地址
//        char netB2IP[128];//备前置机B网IP地址

//};
///*==============================================================*/
///* Table: 厂站遥测                                          */
///*==============================================================*/
//struct RTUYC
//{
//        unsigned int    id;                    /*id*/
//        unsigned int    rtuId;                 /*设备ID*/
//        char			name[256];                    /*遥测名称*/

//        double			upLimit;                   /*越上限动作值*/
//        double			upLimitReset;			/*越上限复归值*/
//        unsigned int	enableUpAlarm; /*使能越上限报警*/
//        char			upAlarmVoiceText[512];			/*越上限动作告警语音文本*/
//        char			upAlarmResetVoiceText[512];			/*越上限复归告警语音文本*/
//        unsigned int	upAlarmPrint; /*越上限打印*/

//        double			downLimit;                  /*越下限动作值*/
//        double			downLimitReset;                  /*越下限复归值*/
//        unsigned int	enableDownAlarm;		/*使能越下限报警*/
//        char			downAlarmVoiceText[512]; /*越下限动作告警语音文本*/
//        char			downAlarmResetVoiceText[512]; /*越下限复归告警语音文本*/
//        unsigned int	downAlarmPrint; /*越下限打印*/


//        unsigned int    typeId;                  /*遥测类型*/
//        unsigned int    unitId;             /*单位*/

//        double			factor;                     /*系数*/
//        double			offset;                    /*偏移值*/

//        unsigned int	enableInitValue; /*使能初始值*/
//        double			initValue; /*初始值*/

//        unsigned int    enableCalculate;               /*是否计算*/
//        char			calcFormular[512];                 /*计算公式*/

//        unsigned int    storage;                     /*是否保存*/

//        unsigned int  saveAlarm;	//告警入库
//};


///*==============================================================*/
///* 厂站遥控                                         */
///* 2017年9月12日09:46:01 在表中增加验证开关编号字段
///*==============================================================*/
//struct RTUYK
//{
//        unsigned int        id;                          /*id*/
//        unsigned int        rtuId;                      /*设备ID*/
//        char				name[256];                 /*遥控名称*/

//        unsigned int        type;                          /*类型*/
//        unsigned int		verifySerialNumber; //验证开关编号
//        char				serialNumber[256]; //开关编号

//        char				remark[256]; //说明

//};

///*==============================================================*/
///* 厂站遥调                                         */
///*==============================================================*/
//struct RTUYT
//{
//        unsigned int        id;                          /*id*/
//        unsigned int        rtuId;                      /*设备ID*/
//        char				name[256];                 /*遥调名称*/
//        unsigned int		unitId;						/*单位*/
//        double				factor;                       /*命令下发系数*/

//        char				remark[256]; //说明

//};

///*==============================================================*/
///* 厂站遥信                                         */
///* 2017年9月12日10:14:48 增加合分报警弹窗字段
///*==============================================================*/
//struct RTUYX
//{
//        unsigned int		id;                         /*id*/
//        unsigned int		rtuId;                  /*厂站ID*/
//        char				name[256];                      /*遥信名称*/
//        unsigned int		alarmPrint;                  /*变位打印*/
//        unsigned int		typeId;                        /*遥信类型*/

//        unsigned int		onAlarm;                   /*合报警*/
//        char				onAlarmVoiceText[512]; /*合报警语音文本*/
//        unsigned int        onAlarmPopupWindow;		//合报警弹窗

//        unsigned int        offAlarm;                   /*分报警*/
//        char				offAlarmVoiceText[512]; /*分语音报警文本*/
//        unsigned int		offAlarmPopupWindow;	//分报警弹窗


//        unsigned int        enableCalculate;            /*是否计算*/
//        char				calcFormular[512];               /*计算公式*/

//        unsigned int        reverse;               /*是否取反*/

//        unsigned int        storage;                 /*是否保存实时数据*/

//        unsigned int		enableInitValue; /*初始值开关*/
//        unsigned short int		initValue; /*初始值*/

//        unsigned int		virtualSOE; //虚拟SOE

//        unsigned int  saveAlarm;	//告警入库
//};


///*==============================================================*/
///* 厂站遥脉                                        */
///*==============================================================*/
//struct  RTUYM
//{
//        unsigned int    id;                          /*id*/
//        unsigned int    rtuId;                   /*设备ID*/
//        char			name[256];                        /*电度量名称*/
//        unsigned int    unitId;                    /*单位*/
//        unsigned int    typeId;                     /*类型*/
//        double			baseValue;                  /*基值*/

//        unsigned int    enableMaxValue; /*使能越量程检查*/
//        double			maxValue;                      /*量程*/
//        unsigned int    maxAlarmPrint;          /*越量程打印*/
//        char			maxAlarmVoiceText[512]; /*越量程动作语音文本*/
//        char			maxAlarmResetVoiceText[512]; /*越量程复归语音文本*/

//        double			factor;                   /*系数*/
//        double			offset;                    /*偏移值*/

//        unsigned int    enableCalculate;                /*是否计算*/
//        char			calcFormular[512];                   /*计算公式*/

//        unsigned int    storage;                   /*是否保存*/

//        unsigned int  saveAlarm;	//告警入库
//};

#pragma pack()
typedef QVector<StationPara>            VecStation;   //容器-站
typedef QVector<StationInfo>            VecStationInfo;
typedef QVector<NodeConfig>             VecNodeConfig;//容器-角色
//typedef QVector<SystemParameters>       VecSystemParameter;
typedef QVector<para_user>              VecUser;      //容器-用户
typedef QVector<para_usergroup>         VecUserGroup; //容器-用户组
typedef QVector<BoxPara>                VecBoxPara;   //容器-装置
typedef QVector<CommPara>               VecCommPara;  //容器-通道
typedef QVector<ProtocolPara>           VecProtocolPara;//容器-规约
typedef QVector<BoxTemlete>             VecBoxTemlete;  //容器-装置模板表
typedef QVector<BoxTemleteDataPara>     VecBoxTemleteDataPara;//容器-装置模板数据表
typedef QVector<para_yc>            VecParaYC;
typedef QVector<para_ym>            VecParaYM;
typedef QVector<para_xb>            VecParaXB;
typedef QVector<para_yt>            VecParaYT;
typedef QVector<para_yk>            VecParaYK;
typedef QVector<para_yx>            VecParaYX;
typedef QVector<para_gz>            VecParaGZ;
typedef QVector<BoxTemleteDZPara>     VecBoxTemleteDZPara;//容器-装置模板数据表
typedef QVector<BoxTemleteTTPara>     VecBoxTemleteTTPara;//容器-装置模板数据表
Q_DECLARE_METATYPE(StationPara)
#endif // STRUCTITEM_H
