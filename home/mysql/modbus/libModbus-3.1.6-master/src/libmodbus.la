# libmodbus.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.6.42-b88ce
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libmodbus.so.5'

# Names of this library.
library_names='libmodbus.so.5.1.0 libmodbus.so.5 libmodbus.so'

# The name of the static archive.
old_library=''

# Linker flags that cannot go in dependency_libs.
inherited_linker_flags=''

# Libraries that this one depends upon.
dependency_libs=''

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libmodbus.
current=6
age=1
revision=0

# Is this an already installed library?
installed=no

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/home/<USER>/home/<USER>/modbus/libmodbus_install/lib'
