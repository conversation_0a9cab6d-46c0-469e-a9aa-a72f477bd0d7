#-------------------------------------------------
#
# Project created by QtCreator 2019-01-07T09:43:10
#
#-------------------------------------------------

QT       -= core gui

TARGET = Modbus
TEMPLATE = lib

DEFINES += LIBMODBUS_LIBRARY

CONFIG += console
CONFIG -= app_bundle
CONFIG -= qt
CONFIG += dll

LIBS += -L$$PWD -lws2_32
# The following define makes your compiler emit warnings if you use
# any feature of Qt which has been marked as deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if you use deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    modbus-over-tcp.c \
    modbus.c \
    modbus-data.c \
    modbus-rtu.c \
    modbus-tcp.c

HEADERS += \
        libmodbus_global.h \
        modbus-over-tcp-private.h \
    config.h \
    modbus-over-tcp.h \
    modbus.h \
    modbus-private.h \
    modbus-rtu.h \
    modbus-rtu-private.h \
    modbus-tcp.h \
    modbus-tcp-private.h \
    modbus-version.h

unix {
    target.path = /usr/lib
    INSTALLS += target
}
