modbus_rtu_get_rts(3)
=====================


NAME
----
modbus_rtu_get_rts - get the current RTS mode in RTU


SYNOPSIS
--------
*int modbus_rtu_get_rts(modbus_t *'ctx');*


DESCRIPTION
-----------
The *modbus_rtu_get_rts()* function shall get the current Request To Send mode
of the libmodbus context _ctx_. The possible returned values are:

* MODBUS_RTU_RTS_NONE
* MODBUS_RTU_RTS_UP
* MODBUS_RTU_RTS_DOWN

This function can only be used with a context using a RTU backend.


RETURN VALUE
------------
The function shall return the current RTS mode if successful. Otherwise it shall
return -1 and set errno.


ERRORS
------
*EINVAL*::
The libmodbus backend is not RTU.


SEE ALSO
--------
linkmb:modbus_rtu_set_rts[3]


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
