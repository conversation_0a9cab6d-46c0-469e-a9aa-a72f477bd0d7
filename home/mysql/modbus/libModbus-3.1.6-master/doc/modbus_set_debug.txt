modbus_set_debug(3)
===================

NAME
----
modbus_set_debug - set debug flag of the context


SYNOPSIS
--------
*int modbus_set_debug(modbus_t *'ctx', int 'flag');*


DESCRIPTION
-----------
The *modbus_set_debug()* function shall set the debug flag of the *modbus_t*
context by using the argument _flag_. By default, the boolean flag is set to
`FALSE`. When the _flag_ value is set to `TRUE`, many verbose messages are
displayed on stdout and stderr. For example, this flag is useful to display the
bytes of the Modbus messages.

[verse]
___________________
[00][14][00][00][00][06][12][03][00][6B][00][03]
Waiting for a confirmation...
<00><14><00><00><00><09><12><03><06><02><2B><00><00><00><00>
___________________


RETURN VALUE
------------
The function shall return 0 if successful. Otherwise it shall return -1 and set errno.


AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>
