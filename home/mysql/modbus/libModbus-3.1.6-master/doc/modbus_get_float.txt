modbus_get_float(3)
===================


NAME
----
modbus_get_float - get a float value from 2 registers


SYNOPSIS
--------
*float modbus_get_float(const uint16_t *'src');*

Warning, this function is *deprecated* since libmodbus v3.2.0 and has been
replaced by *modbus_get_float_dcba()*.

DESCRIPTION
-----------
The *modbus_get_float()* function shall get a float from 4 bytes in Modbus
format (DCBA byte order). The _src_ array must be a pointer on two 16 bits
values, for example, if the first word is set to 0x4465 and the second to
0x229a, the float value will be 916.540649.


RETURN VALUE
------------
The function shall return a float.


SEE ALSO
--------
linkmb:modbus_set_float[3]
linkmb:modbus_set_float_dcba[3]
linkmb:modbus_get_float_dcba[3]

AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
