modbus_set_response_timeout(3)
==============================


NAME
----
modbus_set_response_timeout - set timeout for response


SYNOPSIS
--------
*int modbus_set_response_timeout(modbus_t *'ctx', uint32_t 'to_sec', uint32_t 'to_usec');*


DESCRIPTION
-----------
The *modbus_set_response_timeout()* function shall set the timeout interval used
to wait for a response. When a byte timeout is set, if elapsed time for the
first byte of response is longer than the given timeout, an `ETIMEDOUT` error
will be raised by the function waiting for a response. When byte timeout is
disabled, the full confirmation response must be received before expiration of
the response timeout.

The value of _to_usec_ argument must be in the range 0 to 999999.


RETURN VALUE
------------
The function shall return 0 if successful. Otherwise it shall return -1 and set
errno.


ERRORS
------
*EINVAL*::
The argument _ctx_ is NULL, or both _to_sec_ and _to_usec_ are zero, or _to_usec_
is larger than 1000000.


EXAMPLE
-------
[source,c]
-------------------
uint32_t old_response_to_sec;
uint32_t old_response_to_usec;

/* Save original timeout */
modbus_get_response_timeout(ctx, &old_response_to_sec, &old_response_to_usec);

/* Define a new timeout of 200ms */
modbus_set_response_timeout(ctx, 0, 200000);
-------------------


SEE ALSO
--------
linkmb:modbus_get_response_timeout[3]
linkmb:modbus_get_byte_timeout[3]
linkmb:modbus_set_byte_timeout[3]


AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>
