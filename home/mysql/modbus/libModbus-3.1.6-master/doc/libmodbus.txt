libmodbus(7)
============


NAME
----
libmodbus - a fast and portable Modbus library


SYNOPSIS
--------
*#include <modbus.h>*

*cc* \`pkg-config --cflags --libs libmodbus` 'files'


DESCRIPTION
-----------
libmodbus is a library to send/receive data with a device which respects the
Modbus protocol. This library contains various backends to communicate over
different networks (eg. serial in RTU mode or Ethernet in TCP/IPv6). The
http://www.modbus.org site provides documentation about the protocol at
http://www.modbus.org/specs.php.

libmodbus provides an abstraction of the lower communication layers and offers
the same API on all supported platforms.

This documentation presents an overview of libmodbus concepts, describes how
libmodbus abstracts Modbus communication with different hardware and platforms
and provides a reference manual for the functions provided by the libmodbus
library.


Contexts
~~~~~~~~
The Modbus protocol contains many variants (eg. serial RTU or Ethernet TCP), to
ease the implementation of a variant, the library was designed to use a backend
for each variant. The backends are also a convenient way to fulfill other
requirements (eg. real-time operations). Each backend offers a specific function
to create a new 'modbus_t' context. The 'modbus_t' context is an opaque
structure containing all necessary information to establish a connection with
other Modbus devices according to the selected variant.

You can choose the best context for your needs among:

RTU Context
^^^^^^^^^^^
The RTU backend (Remote Terminal Unit) is used in serial communication and makes
use of a compact, binary representation of the data for protocol
communication. The RTU format follows the commands/data with a cyclic redundancy
check checksum as an error check mechanism to ensure the reliability of
data. Modbus RTU is the most common implementation available for Modbus. A
Modbus RTU message must be transmitted continuously without inter-character
hesitations (extract from Wikipedia, Modbus, http://en.wikipedia.org/wiki/Modbus
(as of Mar. 13, 2011, 20:51 GMT).

The Modbus RTU framing calls a slave, a device/service which handle Modbus
requests, and a master, a client which send requests. The communication is
always initiated by the master.

Many Modbus devices can be connected together on the same physical link so
before sending a message, you must set the slave (receiver) with
linkmb:modbus_set_slave[3]. If you're running a slave, its slave number will be
used to filter received messages.

The libmodbus implementation of RTU isn't time based as stated in original
Modbus specification, instead all bytes are sent as fast as possible and a
response or an indication is considered complete when all expected characters
have been received. This implementation offers very fast communication but you
must take care to set a response timeout of slaves less than response timeout of
master (ortherwise other slaves may ignore master requests when one of the slave
is not responding).

Create a Modbus RTU context::
    linkmb:modbus_new_rtu[3]


Set the serial mode::
    linkmb:modbus_rtu_get_serial_mode[3]
    linkmb:modbus_rtu_set_serial_mode[3]
    linkmb:modbus_rtu_get_rts[3]
    linkmb:modbus_rtu_set_rts[3]
    linkmb:modbus_rtu_set_custom_rts[3]
    linkmb:modbus_rtu_get_rts_delay[3]
    linkmb:modbus_rtu_set_rts_delay[3]


TCP (IPv4) Context
^^^^^^^^^^^^^^^^^^
The TCP backend implements a Modbus variant used for communications over
TCP/IPv4 networks. It does not require a checksum calculation as lower layer
takes care of the same.

Create a Modbus TCP context::
    linkmb:modbus_new_tcp[3]


TCP PI (IPv4 and IPv6) Context
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
The TCP PI (Protocol Independent) backend implements a Modbus variant used for
communications over TCP IPv4 and IPv6 networks. It does not require a checksum
calculation as lower layer takes care of the same.

Contrary to the TCP IPv4 only backend, the TCP PI backend offers hostname
resolution but it consumes about 1Kb of additional memory.

Create a Modbus TCP context::
    linkmb:modbus_new_tcp_pi[3]


Common
^^^^^^
Before using any libmodbus functions, the caller must allocate and initialize a
'modbus_t' context with functions explained above, then the following functions
are provided to modify and free a 'context':

Free libmodbus context::
    linkmb:modbus_free[3]

Set slave ID::
    linkmb:modbus_set_slave[3]

Enable debug mode::
    linkmb:modbus_set_debug[3]

Timeout settings::
    linkmb:modbus_get_byte_timeout[3]
    linkmb:modbus_set_byte_timeout[3]
    linkmb:modbus_get_response_timeout[3]
    linkmb:modbus_set_response_timeout[3]
    linkmb:modbus_get_indication_timeout[3]
    linkmb:modbus_set_indication_timeout[3]

Error recovery mode::
    linkmb:modbus_set_error_recovery[3]

Setter/getter of internal socket::
    linkmb:modbus_set_socket[3]
    linkmb:modbus_get_socket[3]

Information about header::
    linkmb:modbus_get_header_length[3]

Macros for data manipulation::

- MODBUS_GET_HIGH_BYTE(data), extracts the high byte from a byte
- MODBUS_GET_LOW_BYTE(data), extracts the low byte from a byte
- MODBUS_GET_INT64_FROM_INT16(tab_int16, index), builds an int64 from the four
  first int16 starting at tab_int16[index]
- MODBUS_GET_INT32_FROM_INT16(tab_int16, index), builds an int32 from the two
  first int16 starting at tab_int16[index]
- MODBUS_GET_INT16_FROM_INT8(tab_int8, index), builds an int16 from the two
  first int8 starting at tab_int8[index]
- MODBUS_SET_INT16_TO_INT8(tab_int8, index, value), set an int16 value into
  the two first bytes starting at tab_int8[index]
- MODBUS_SET_INT32_TO_INT16(tab_int16, index, value), set an int32 value into
  the two first int16 starting at tab_int16[index]
- MODBUS_SET_INT64_TO_INT16(tab_int16, index, value), set an int64 value into
  the four first int16 starting at tab_int16[index]

Handling of bits and bytes::
    linkmb:modbus_set_bits_from_byte[3]
    linkmb:modbus_set_bits_from_bytes[3]
    linkmb:modbus_get_byte_from_bits[3]

Set or get float numbers::
    linkmb:modbus_get_float_abcd[3]
    linkmb:modbus_set_float_abcd[3]
    linkmb:modbus_get_float_badc[3]
    linkmb:modbus_set_float_badc[3]
    linkmb:modbus_get_float_cdab[3]
    linkmb:modbus_set_float_cdab[3]
    linkmb:modbus_get_float_dcba[3]
    linkmb:modbus_set_float_dcba[3]
    linkmb:modbus_get_float[3] (deprecated)
    linkmb:modbus_set_float[3] (deprecated)



Connection
~~~~~~~~~~
The following functions are provided to establish and close a connection with
Modbus devices:

Establish a connection::
    linkmb:modbus_connect[3]

Close a connection::
    linkmb:modbus_close[3]

Flush a connection::
    linkmb:modbus_flush[3]


Client
~~~~~~
The Modbus protocol defines different data types and functions to read and write
them from/to remote devices. The following functions are used by the clients to
send Modbus requests:

Read data::
     linkmb:modbus_read_bits[3]
     linkmb:modbus_read_input_bits[3]
     linkmb:modbus_read_registers[3]
     linkmb:modbus_read_input_registers[3]
     linkmb:modbus_report_slave_id[3]

Write data::
     linkmb:modbus_write_bit[3]
     linkmb:modbus_write_register[3]
     linkmb:modbus_write_bits[3]
     linkmb:modbus_write_registers[3]

Write and read data::
      linkmb:modbus_write_and_read_registers[3]

Raw requests::
    linkmb:modbus_send_raw_request[3]
    linkmb:modbus_receive_confirmation[3]

Reply an exception::
    linkmb:modbus_reply_exception[3]


Server
~~~~~~
The server is waiting for request from clients and must answer when it is
concerned by the request.

In TCP mode, you must not use the usual linkmb:modbus_connect[3] to establish the connection but a pair of accept/listen calls::
     linkmb:modbus_tcp_listen[3]
     linkmb:modbus_tcp_accept[3]
     linkmb:modbus_tcp_pi_listen[3]
     linkmb:modbus_tcp_pi_accept[3]

then the data can be received with::
     linkmb:modbus_receive[3]

and a response can be send with::
     linkmb:modbus_reply[3]
     linkmb:modbus_reply_exception[3]

To handle the mapping of your Modbus data, you must use:
     linkmb:modbus_mapping_new[3]
     linkmb:modbus_mapping_free[3]


ERROR HANDLING
--------------
The libmodbus functions handle errors using the standard conventions found on
POSIX systems. Generally, this means that upon failure a libmodbus function
shall return either a NULL value (if returning a pointer) or a negative value
(if returning an integer), and the actual error code shall be stored in the
'errno' variable.

The *modbus_strerror()* function is provided to translate libmodbus-specific
error codes into error message strings; for details refer to
linkmb:modbus_strerror[3].


MISCELLANEOUS
-------------
The _LIBMODBUS_VERSION_STRING_ constant indicates the libmodbus version the
program has been compiled against. The variables 'libmodbus_version_major',
'libmodbus_version_minor', 'libmodbus_version_micro' give the version the
program is linked against.


AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>


RESOURCES
---------
Main web site: <http://www.libmodbus.org/>

Report bugs on the issue tracker at
<http://github.com/stephane/libmodbus/issues>.


COPYING
-------
Free use of this software is granted under the terms of the GNU Lesser General
Public License (LGPL v2.1+). For details see the file `COPYING.LESSER` included
with the libmodbus distribution.
