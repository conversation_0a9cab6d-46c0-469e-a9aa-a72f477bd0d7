modbus_set_bits_from_bytes(3)
============================


NAME
----
modbus_set_bits_from_bytes - set many bits from an array of bytes


SYNOPSIS
--------
*void modbus_set_bits_from_bytes(uint8_t *'dest', int 'index', unsigned int 'nb_bits', const uint8_t *'tab_byte');*


DESCRIPTION
-----------
The *modbus_set_bits_from_bytes* function shall set bits by reading an array of
bytes. All the bits of the bytes read from the first position of the array
_tab_byte_ are written as bits in the _dest_ array starting at position _index_.


RETURN VALUE
------------
There is no return values.


SEE ALSO
--------
linkmb:modbus_set_bits_from_byte[3]
linkmb:modbus_get_byte_from_bits[3]


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
