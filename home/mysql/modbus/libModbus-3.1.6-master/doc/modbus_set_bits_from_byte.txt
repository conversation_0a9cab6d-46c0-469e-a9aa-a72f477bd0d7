modbus_set_bits_from_byte(3)
============================


NAME
----
modbus_set_bits_from_byte - set many bits from a single byte value


SYNOPSIS
--------
*void modbus_set_bits_from_byte(uint8_t *'dest', int 'index', const uint8_t 'value');*


DESCRIPTION
-----------
The *modbus_set_bits_from_byte()* function shall set many bits from a single byte.
All 8 bits from the byte _value_ will be written to _dest_ array starting at
_index_ position.


RETURN VALUE
------------
There is no return values.


SEE ALSO
--------
linkmb:modbus_set_bits_from_byte[3]
linkmb:modbus_set_bits_from_bytes[3]


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
