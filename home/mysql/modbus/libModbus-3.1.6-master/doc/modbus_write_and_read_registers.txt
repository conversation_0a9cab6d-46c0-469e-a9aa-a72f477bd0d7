modbus_write_and_read_registers(3)
==================================


NAME
----
modbus_write_and_read_registers - write and read many registers in a single transaction


SYNOPSIS
--------
*int modbus_write_and_read_registers(modbus_t *'ctx', int 'write_addr', int 'write_nb', const uint16_t *'src', int 'read_addr', int 'read_nb', const uint16_t *'dest');*


DESCRIPTION
-----------
The *modbus_write_and_read_registers()* function shall write the content of the
_write_nb_ holding registers from the array 'src' to the address _write_addr_ of
the remote device then shall read the content of the _read_nb_ holding registers
to the address _read_addr_ of the remote device. The result of reading is stored
in _dest_ array as word values (16 bits).

You must take care to allocate enough memory to store the results in _dest_
(at least _nb_ * sizeof(uint16_t)).

The function uses the Modbus function code 0x17 (write/read registers).


RETURN VALUE
------------
The function shall return the number of read registers if successful. Otherwise
it shall return -1 and set errno.


ERRORS
------
*EMBMDATA*::
Too many registers requested, Too many registers to write


SEE ALSO
--------
linkmb:modbus_read_registers[3]
linkmb:modbus_write_register[3]
linkmb:modbus_write_registers[3]


AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>
