modbus_flush(3)
===============


NAME
----
modbus_flush - flush non-transmitted data


SYNOPSIS
--------
*int modbus_flush(modbus_t *'ctx');*


DESCRIPTION
-----------
The *modbus_flush()* function shall discard data received but not read to the
socket or file descriptor associated to the context 'ctx'.


RETURN VALUE
------------
The function shall return 0 or the number of flushed bytes if
successful. Otherwise it shall return -1 and set errno.


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
