modbus_write_bit(3)
===================


NAME
----
modbus_write_bit - write a single bit


SYNOPSIS
--------
*int modbus_write_bit(modbus_t *'ctx', int 'addr', int 'status');*


DESCRIPTION
-----------
The *modbus_write_bit()* function shall write the status of _status_ at the
address _addr_ of the remote device. The value must be set to `TRUE` or `FALSE`.

The function uses the Modbus function code 0x05 (force single coil).


RETURN VALUE
------------
The function shall return 1 if successful. Otherwise it shall return -1 and set
errno.


SEE ALSO
--------
linkmb:modbus_read_bits[3]
linkmb:modbus_write_bits[3]


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
