modbus_close(3)
===============


NAME
----
modbus_close - close a Modbus connection


SYNOPSIS
--------
*void modbus_close(modbus_t *'ctx');*


DESCRIPTION
-----------
The *modbus_close()* function shall close the connection established with the
backend set in the context.


RETURN VALUE
------------
There is no return value.


EXAMPLE
-------
[source,c]
-------------------
modbus_t *ctx;

ctx = modbus_new_tcp("127.0.0.1", 502);
if (modbus_connect(ctx) == -1) {
    fprintf(stderr, "Connection failed: %s\n", modbus_strerror(errno));
    modbus_free(ctx);
    return -1;
}

modbus_close(ctx);
modbus_free(ctx);
-------------------

SEE ALSO
--------
linkmb:modbus_connect[3]


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<stephane.rai<PERSON><PERSON>@gmail.com>
