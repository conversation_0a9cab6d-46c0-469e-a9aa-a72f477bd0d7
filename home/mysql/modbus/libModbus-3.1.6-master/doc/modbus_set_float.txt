modbus_set_float(3)
===================

NAME
----
modbus_set_float - set a float value from 2 registers


SYNOPSIS
--------
*void modbus_set_float(float 'f', uint16_t *'dest');*

Warning, this function is *deprecated* since libmodbus v3.2.0 and has been
replaced by *modbus_set_float_dcba()*.

DESCRIPTION
-----------
The *modbus_set_float()* function shall set a float to 4 bytes in Modbus format
(ABCD). The _dest_ array must be pointer on two 16 bits values to be able to
store the full result of the conversion.


RETURN VALUE
------------
There is no return values.


SEE ALSO
--------
linkmb:modbus_get_float[3]
linkmb:modbus_set_float_dcba[3]

AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
