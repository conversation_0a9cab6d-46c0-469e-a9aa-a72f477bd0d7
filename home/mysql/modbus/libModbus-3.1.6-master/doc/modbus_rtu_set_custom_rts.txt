modbus_rtu_set_custom_rts(3)
============================


NAME
----
modbus_rtu_set_custom_rts - set a function to be used for custom RTS implementation


SYNOPSIS
--------
*int modbus_rtu_set_custom_rts(modbus_t *'ctx', void (*'set_rts') (modbus_t *ctx, int on))*


DESCRIPTION
-----------
The _modbus_rtu_set_custom_rts()_ function shall set a custom function to be
called when the RTS pin is to be set before and after a transmission. By default
this is set to an internal function that toggles the RTS pin using an ioctl
call.

Note that this function adheres to the RTS mode, the values MODBUS_RTU_RTS_UP or
MODBUS_RTU_RTS_DOWN must be used for the function to be called.

This function can only be used with a context using a RTU backend.


RETURN VALUE
------------
The _modbus_rtu_set_custom_rts()_ function shall return 0 if successful.
Otherwise it shall return -1 and set errno to one of the values defined below.


ERRORS
------
*EINVAL*::
The libmodbus backend is not RTU.


AUTHORS
-------
<PERSON>st<PERSON> <<EMAIL>>

The libmodbus documentation was written by Stéphane Raimbault
<step<PERSON>.<EMAIL>>
