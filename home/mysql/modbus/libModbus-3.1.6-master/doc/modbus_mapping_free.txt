modbus_mapping_free(3)
=====================


NAME
----
modbus_mapping_free - free a modbus_mapping_t structure


SYNOPSIS
--------
*void modbus_mapping_free(modbus_mapping_t *'mb_mapping');*


DESCRIPTION
-----------
The function shall free the four arrays of mb_mapping_t structure and finally
the mb_mapping_t referenced by _mb_mapping_.


RETURN VALUE
------------
There is no return values.


SEE ALSO
--------
linkmb:modbus_mapping_new[3]


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
