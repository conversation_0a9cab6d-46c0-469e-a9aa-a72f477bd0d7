modbus_mapping_new_start_address(3)
===================================


NAME
----
modbus_mapping_new_start_address - allocate four arrays of bits and registers accessible from their starting addresses


SYNOPSIS
--------
*modbus_mapping_t* modbus_mapping_new_start_address(int 'start_bits', int 'nb_bits',
                                                    int 'start_input_bits', int 'nb_input_bits',
                                                    int 'start_registers', int 'nb_registers',
                                                    int 'start_input_registers', int 'nb_input_registers');*


DESCRIPTION
-----------
The _modbus_mapping_new_start_address()_ function shall allocate four arrays to
store bits, input bits, registers and inputs registers. The pointers are stored
in modbus_mapping_t structure. All values of the arrays are initialized to zero.

The different starting adresses make it possible to place the mapping at any
address in each address space. This way, you can give access to values stored
at high adresses without allocating memory from the address zero, for eg. to
make available registers from 10000 to 10009, you can use:

[source,c]
-------------------
mb_mapping = modbus_mapping_new_start_address(0, 0, 0, 0, 10000, 10, 0, 0);
-------------------

With this code, only 10 registers (`uint16_t`) are allocated.

If it isn't necessary to allocate an array for a specific type of data, you can
pass the zero value in argument, the associated pointer will be NULL.

This function is convenient to handle requests in a Modbus server/slave.


RETURN VALUE
------------
The _modbus_mapping_new_start_address()_ function shall return the new allocated structure if
successful. Otherwise it shall return NULL and set errno.


ERRORS
------
ENOMEM::
Not enough memory


EXAMPLE
-------
[source,c]
-------------------
/* The first value of each array is accessible at the defined address.
   The end address is ADDRESS + NB - 1. */
mb_mapping = modbus_mapping_new_start_address(BITS_ADDRESS, BITS_NB,
                                INPUT_BITS_ADDRESS, INPUT_BITS_NB,
                                REGISTERS_ADDRESS, REGISTERS_NB,
                                INPUT_REGISTERS_ADDRESS, INPUT_REGISTERS_NB);
if (mb_mapping == NULL) {
    fprintf(stderr, "Failed to allocate the mapping: %s\n",
            modbus_strerror(errno));
    modbus_free(ctx);
    return -1;
}
-------------------

SEE ALSO
--------
linkmb:modbus_mapping_new[3]
linkmb:modbus_mapping_free[3]


AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>
