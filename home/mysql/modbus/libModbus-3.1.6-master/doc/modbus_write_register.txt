modbus_write_register(3)
========================


NAME
----
modbus_write_register - write a single register


SYNOPSIS
--------
*int modbus_write_register(modbus_t *'ctx', int 'addr', const uint16_t 'value');*


DESCRIPTION
-----------
The *modbus_write_register()* function shall write the value of _value_
holding registers at the address _addr_ of the remote device.

The function uses the Modbus function code 0x06 (preset single register).


RETURN VALUE
------------
The function shall return 1 if successful. Otherwise it shall return -1 and set
errno.


SEE ALSO
--------
linkmb:modbus_read_registers[3]
linkmb:modbus_write_registers[3]


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
