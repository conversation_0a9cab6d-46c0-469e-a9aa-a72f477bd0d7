modbus_free(3)
==============


NAME
----
modbus_free - free a libmodbus context


SYNOPSIS
--------
*void modbus_free(modbus_t *'ctx');*


DESCRIPTION
-----------
The *modbus_free()* function shall free an allocated modbus_t structure.


RETURN VALUE
------------
There is no return values.


SEE ALSO
--------
linkmb:libmodbus[3]


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
