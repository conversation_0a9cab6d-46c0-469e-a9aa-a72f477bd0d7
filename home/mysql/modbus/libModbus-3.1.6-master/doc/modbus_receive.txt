modbus_receive(3)
=================


NAME
----
modbus_receive - receive an indication request


SYNOPSIS
--------
*int modbus_receive(modbus_t *'ctx', uint8_t *'req');*


DESCRIPTION
-----------
The *modbus_receive()* function shall receive an indication request from the
socket of the context _ctx_. This function is used by Modbus slave/server to
receive and analyze indication request sent by the masters/clients.

If you need to use another socket or file descriptor than the one defined in the
context _ctx_, see the function linkmb:modbus_set_socket[3].


RETURN VALUE
------------
The function shall store the indication request in _req_ and return the request
length if successful. The returned request length can be zero if the indication
request is ignored (eg. a query for another slave in RTU mode). Otherwise it
shall return -1 and set errno.


SEE ALSO
--------
linkmb:modbus_set_socket[3]
linkmb:modbus_reply[3]


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
