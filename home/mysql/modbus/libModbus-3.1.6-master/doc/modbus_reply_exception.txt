modbus_reply_exception(3)
=========================

NAME
----
modbus_reply_exception - send an exception reponse


SYNOPSIS
--------
*int modbus_reply_exception(modbus_t *'ctx', const uint8_t *'req', unsigned int 'exception_code');


DESCRIPTION
-----------
The *modbus_reply_exception()* function shall send an exception response based
on the 'exception_code' in argument.

The libmodbus provides the following exception codes:

* MODBUS_EXCEPTION_ILLEGAL_FUNCTION (1)
* MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS (2)
* MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE (3)
* MODBUS_EXCEPTION_SLAVE_OR_SERVER_FAILURE (4)
* MODBUS_EXCEPTION_ACKNOWLEDGE (5)
* MODBUS_EXCEPTION_SLAVE_OR_SERVER_BUSY (6)
* MODBUS_EXCEPTION_NEGATIVE_ACKNOWLEDGE (7)
* MODBUS_EXCEPTION_MEMORY_PARITY (8)
* MODBUS_EXCEPTION_NOT_DEFINED (9)
* MODBUS_EXCEPTION_GATEWAY_PATH (10)
* MODBUS_EXCEPTION_GATEWAY_TARGET (11)

The initial request _req_ is required to build a valid response.


RETURN VALUE
------------
The function shall return the length of the response sent if
successful. Otherwise it shall return -1 and set errno.


ERRORS
------
*EINVAL*::
The exception code is invalid


SEE ALSO
--------
linkmb:modbus_reply[3]
linkmb:libmodbus[7]


AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>
