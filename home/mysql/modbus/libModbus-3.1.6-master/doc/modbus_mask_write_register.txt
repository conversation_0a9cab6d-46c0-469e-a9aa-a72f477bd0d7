modbus_mask_write_register(3)
=============================


NAME
----
modbus_mask_write_register - mask a single register


SYNOPSIS
--------
*int modbus_mask_write_register(modbus_t *'ctx', int 'addr', uint16_t 'and', uint16_t 'or');*


DESCRIPTION
-----------
The *modbus_mask_write_register()* function shall modify the value of the
holding register at the address 'addr' of the remote device using the algorithm:

  new value = (current value AND 'and') OR ('or' AND (NOT 'and'))

The function uses the Modbus function code 0x16 (mask single register).


RETURN VALUE
------------
The function shall return 1 if successful. Otherwise it shall return -1 and set
errno.


SEE ALSO
--------
linkmb:modbus_read_registers[3]
linkmb:modbus_write_registers[3]


AUTHORS
-------
<PERSON><PERSON><PERSON> <<EMAIL>>
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
