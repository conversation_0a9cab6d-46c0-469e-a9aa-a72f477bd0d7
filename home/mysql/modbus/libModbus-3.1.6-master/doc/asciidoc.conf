[paradef-default]
literal-style=template="literalparagraph"

[macros]
(?su)[\\]?(?P<name>linkmb):(?P<target>\S*?)\[(?P<attrlist>.*?)\]=

ifdef::backend-docbook[]
[linkmb-inlinemacro]
{0%{target}}
{0#<citerefentry>}
{0#<refentrytitle>{target}</refentrytitle><manvolnum>{0}</manvolnum>}
{0#</citerefentry>}
endif::backend-docbook[]

ifdef::backend-xhtml11[]
[linkmb-inlinemacro]
<a href="{target}.html">{target}{0?({0})}</a>
endif::backend-xhtml11[]

ifdef::doctype-manpage[]
ifdef::backend-docbook[]
[header]
template::[header-declarations]
<refentry>
<refmeta>
<refentrytitle>{mantitle}</refentrytitle>
<manvolnum>{manvolnum}</manvolnum>
<refmiscinfo class="source">libmodbus</refmiscinfo>
<refmiscinfo class="version">v{libmodbus_version}</refmiscinfo>
<refmiscinfo class="manual">libmodbus Manual</refmiscinfo>
</refmeta>
<refnamediv>
  <refname>{manname}</refname>
  <refpurpose>{manpurpose}</refpurpose>
</refnamediv>
endif::backend-docbook[]
endif::doctype-manpage[]

ifdef::backend-xhtml11[]
[footer]
</div>
{disable-javascript%<div id="footnotes"><hr /></div>}
<div id="footer">
<div id="footer-text">
libmodbus {libmodbus_version}<br />
Last updated {docdate} {doctime}
</div>
</div>
</body>
</html>
endif::backend-xhtml11[]
