This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by libmodbus configure 3.1.6, which was
generated by GNU Autoconf 2.69.  Invocation command line was

  $ ./configure --prefix=/home/<USER>/home/<USER>/modbus/libmodbus_install

## --------- ##
## Platform. ##
## --------- ##

hostname = root
uname -m = x86_64
uname -r = 6.14.0-24-generic
uname -s = Linux
uname -v = #24~24.04.3-Ubuntu SMP PREEMPT_DYNAMIC Mon Jul  7 16:39:17 UTC 2

/usr/bin/uname -p = x86_64
/bin/uname -X     = unknown

/bin/arch              = x86_64
/usr/bin/arch -k       = unknown
/usr/convex/getsysinfo = unknown
/usr/bin/hostinfo      = unknown
/bin/machine           = unknown
/usr/bin/oslevel       = unknown
/bin/universe          = unknown

PATH: /home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin
PATH: /home/<USER>/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/remote-cli
PATH: /home/<USER>/.nvm/versions/node/v20.19.3/bin
PATH: /home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin
PATH: /usr/local/sbin
PATH: /usr/local/bin
PATH: /usr/sbin
PATH: /usr/bin
PATH: /sbin
PATH: /bin
PATH: /usr/games
PATH: /usr/local/games
PATH: /snap/bin
PATH: /home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand


## ----------- ##
## Core tests. ##
## ----------- ##

configure:2649: checking for a BSD-compatible install
configure:2717: result: /usr/bin/install -c
configure:2728: checking whether build environment is sane
configure:2783: result: yes
configure:2934: checking for a thread-safe mkdir -p
configure:2973: result: /usr/bin/mkdir -p
configure:2980: checking for gawk
configure:3010: result: no
configure:2980: checking for mawk
configure:2996: found /usr/bin/mawk
configure:3007: result: mawk
configure:3018: checking whether make sets $(MAKE)
configure:3040: result: yes
configure:3069: checking whether make supports nested variables
configure:3086: result: yes
configure:3165: checking how to create a pax tar archive
configure:3176: tar --version
tar (GNU tar) 1.35
Copyright (C) 2023 Free Software Foundation, Inc.
License GPLv3+: GNU GPL version 3 or later <https://gnu.org/licenses/gpl.html>.
This is free software: you are free to change and redistribute it.
There is NO WARRANTY, to the extent permitted by law.

Written by John Gilmore and Jay Fenlason.
configure:3179: $? = 0
configure:3219: tardir=conftest.dir && eval tar --format=posix -chf - "$tardir" >conftest.tar
configure:3222: $? = 0
configure:3226: tar -xf - <conftest.tar
configure:3229: $? = 0
configure:3231: cat conftest.dir/file
GrepMe
configure:3234: $? = 0
configure:3247: result: gnutar
configure:3301: checking whether make supports the include directive
configure:3316: make -f confmf.GNU && cat confinc.out
this is the am__doit target
configure:3319: $? = 0
configure:3338: result: yes (GNU style)
configure:3408: checking for gcc
configure:3424: found /usr/bin/gcc
configure:3435: result: gcc
configure:3664: checking for C compiler version
configure:3673: gcc --version >&5
gcc (Ubuntu 13.3.0-6ubuntu2~24.04) 13.3.0
Copyright (C) 2023 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:3684: $? = 0
configure:3673: gcc -v >&5
Using built-in specs.
COLLECT_GCC=gcc
COLLECT_LTO_WRAPPER=/usr/libexec/gcc/x86_64-linux-gnu/13/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 13.3.0-6ubuntu2~24.04' --with-bugurl=file:///usr/share/doc/gcc-13/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-13 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/libexec --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-libstdcxx-backtrace --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-13-fG75Ri/gcc-13-13.3.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/gcc-13-fG75Ri/gcc-13-13.3.0/debian/tmp-gcn/usr --enable-offload-defaulted --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 13.3.0 (Ubuntu 13.3.0-6ubuntu2~24.04) 
... rest of stderr output deleted ...
configure:3684: $? = 0
configure:3673: gcc -V >&5
gcc: error: unrecognized command-line option '-V'
gcc: fatal error: no input files
compilation terminated.
configure:3684: $? = 1
configure:3673: gcc -qversion >&5
gcc: error: unrecognized command-line option '-qversion'; did you mean '--version'?
gcc: fatal error: no input files
compilation terminated.
configure:3684: $? = 1
configure:3704: checking whether the C compiler works
configure:3726: gcc    conftest.c  >&5
configure:3730: $? = 0
configure:3778: result: yes
configure:3781: checking for C compiler default output file name
configure:3783: result: a.out
configure:3789: checking for suffix of executables
configure:3796: gcc -o conftest    conftest.c  >&5
configure:3800: $? = 0
configure:3822: result: 
configure:3844: checking whether we are cross compiling
configure:3852: gcc -o conftest    conftest.c  >&5
configure:3856: $? = 0
configure:3863: ./conftest
configure:3867: $? = 0
configure:3882: result: no
configure:3887: checking for suffix of object files
configure:3909: gcc -c   conftest.c >&5
configure:3913: $? = 0
configure:3934: result: o
configure:3938: checking whether we are using the GNU C compiler
configure:3957: gcc -c   conftest.c >&5
configure:3957: $? = 0
configure:3966: result: yes
configure:3975: checking whether gcc accepts -g
configure:3995: gcc -c -g  conftest.c >&5
configure:3995: $? = 0
configure:4036: result: yes
configure:4053: checking for gcc option to accept ISO C89
configure:4116: gcc  -c -g -O2  conftest.c >&5
configure:4116: $? = 0
configure:4129: result: none needed
configure:4154: checking whether gcc understands -c and -o together
configure:4176: gcc -c conftest.c -o conftest2.o
configure:4179: $? = 0
configure:4176: gcc -c conftest.c -o conftest2.o
configure:4179: $? = 0
configure:4191: result: yes
configure:4210: checking dependency style of gcc
configure:4321: result: gcc3
configure:4340: checking for gcc option to accept ISO C99
configure:4489: gcc  -c -g -O2  conftest.c >&5
configure:4489: $? = 0
configure:4502: result: none needed
configure:4610: checking for gcc option to accept ISO Standard C
configure:4621: result: none needed
configure:4634: checking how to run the C preprocessor
configure:4665: gcc -E  conftest.c
configure:4665: $? = 0
configure:4679: gcc -E  conftest.c
conftest.c:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:4679: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libmodbus"
| #define PACKAGE_TARNAME "libmodbus"
| #define PACKAGE_VERSION "3.1.6"
| #define PACKAGE_STRING "libmodbus 3.1.6"
| #define PACKAGE_BUGREPORT "https://github.com/stephane/libmodbus/issues"
| #define PACKAGE_URL "http://libmodbus.org/"
| #define PACKAGE "libmodbus"
| #define VERSION "3.1.6"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:4704: result: gcc -E
configure:4724: gcc -E  conftest.c
configure:4724: $? = 0
configure:4738: gcc -E  conftest.c
conftest.c:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:4738: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libmodbus"
| #define PACKAGE_TARNAME "libmodbus"
| #define PACKAGE_VERSION "3.1.6"
| #define PACKAGE_STRING "libmodbus 3.1.6"
| #define PACKAGE_BUGREPORT "https://github.com/stephane/libmodbus/issues"
| #define PACKAGE_URL "http://libmodbus.org/"
| #define PACKAGE "libmodbus"
| #define VERSION "3.1.6"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:4767: checking for grep that handles long lines and -e
configure:4825: result: /usr/bin/grep
configure:4830: checking for egrep
configure:4892: result: /usr/bin/grep -E
configure:4897: checking for ANSI C header files
configure:4917: gcc -c -g -O2  conftest.c >&5
configure:4917: $? = 0
configure:4990: gcc -o conftest -g -O2   conftest.c  >&5
configure:4990: $? = 0
configure:4990: ./conftest
configure:4990: $? = 0
configure:5001: result: yes
configure:5014: checking for sys/types.h
configure:5014: gcc -c -g -O2  conftest.c >&5
configure:5014: $? = 0
configure:5014: result: yes
configure:5014: checking for sys/stat.h
configure:5014: gcc -c -g -O2  conftest.c >&5
configure:5014: $? = 0
configure:5014: result: yes
configure:5014: checking for stdlib.h
configure:5014: gcc -c -g -O2  conftest.c >&5
configure:5014: $? = 0
configure:5014: result: yes
configure:5014: checking for string.h
configure:5014: gcc -c -g -O2  conftest.c >&5
configure:5014: $? = 0
configure:5014: result: yes
configure:5014: checking for memory.h
configure:5014: gcc -c -g -O2  conftest.c >&5
configure:5014: $? = 0
configure:5014: result: yes
configure:5014: checking for strings.h
configure:5014: gcc -c -g -O2  conftest.c >&5
configure:5014: $? = 0
configure:5014: result: yes
configure:5014: checking for inttypes.h
configure:5014: gcc -c -g -O2  conftest.c >&5
configure:5014: $? = 0
configure:5014: result: yes
configure:5014: checking for stdint.h
configure:5014: gcc -c -g -O2  conftest.c >&5
configure:5014: $? = 0
configure:5014: result: yes
configure:5014: checking for unistd.h
configure:5014: gcc -c -g -O2  conftest.c >&5
configure:5014: $? = 0
configure:5014: result: yes
configure:5027: checking minix/config.h usability
configure:5027: gcc -c -g -O2  conftest.c >&5
conftest.c:54:10: fatal error: minix/config.h: No such file or directory
   54 | #include <minix/config.h>
      |          ^~~~~~~~~~~~~~~~
compilation terminated.
configure:5027: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libmodbus"
| #define PACKAGE_TARNAME "libmodbus"
| #define PACKAGE_VERSION "3.1.6"
| #define PACKAGE_STRING "libmodbus 3.1.6"
| #define PACKAGE_BUGREPORT "https://github.com/stephane/libmodbus/issues"
| #define PACKAGE_URL "http://libmodbus.org/"
| #define PACKAGE "libmodbus"
| #define VERSION "3.1.6"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| /* end confdefs.h.  */
| #include <stdio.h>
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef STDC_HEADERS
| # include <stdlib.h>
| # include <stddef.h>
| #else
| # ifdef HAVE_STDLIB_H
| #  include <stdlib.h>
| # endif
| #endif
| #ifdef HAVE_STRING_H
| # if !defined STDC_HEADERS && defined HAVE_MEMORY_H
| #  include <memory.h>
| # endif
| # include <string.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| #include <minix/config.h>
configure:5027: result: no
configure:5027: checking minix/config.h presence
configure:5027: gcc -E  conftest.c
conftest.c:21:10: fatal error: minix/config.h: No such file or directory
   21 | #include <minix/config.h>
      |          ^~~~~~~~~~~~~~~~
compilation terminated.
configure:5027: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libmodbus"
| #define PACKAGE_TARNAME "libmodbus"
| #define PACKAGE_VERSION "3.1.6"
| #define PACKAGE_STRING "libmodbus 3.1.6"
| #define PACKAGE_BUGREPORT "https://github.com/stephane/libmodbus/issues"
| #define PACKAGE_URL "http://libmodbus.org/"
| #define PACKAGE "libmodbus"
| #define VERSION "3.1.6"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| /* end confdefs.h.  */
| #include <minix/config.h>
configure:5027: result: no
configure:5027: checking for minix/config.h
configure:5027: result: no
configure:5048: checking whether it is safe to define __EXTENSIONS__
configure:5066: gcc -c -g -O2  conftest.c >&5
configure:5066: $? = 0
configure:5073: result: yes
configure:5094: checking for special C compiler options needed for large files
configure:5139: result: no
configure:5145: checking for _FILE_OFFSET_BITS value needed for large files
configure:5170: gcc -c -g -O2  conftest.c >&5
configure:5170: $? = 0
configure:5202: result: no
configure:5299: checking whether make supports nested variables
configure:5316: result: yes
configure:5349: checking build system type
configure:5363: result: x86_64-pc-linux-gnu
configure:5383: checking host system type
configure:5396: result: x86_64-pc-linux-gnu
configure:5495: checking how to print strings
configure:5522: result: printf
configure:5543: checking for a sed that does not truncate output
configure:5607: result: /usr/bin/sed
configure:5625: checking for fgrep
configure:5687: result: /usr/bin/grep -F
configure:5722: checking for ld used by gcc
configure:5789: result: /usr/bin/ld
configure:5796: checking if the linker (/usr/bin/ld) is GNU ld
configure:5811: result: yes
configure:5823: checking for BSD- or MS-compatible name lister (nm)
configure:5877: result: /usr/bin/nm -B
configure:6007: checking the name lister (/usr/bin/nm -B) interface
configure:6014: gcc -c -g -O2  conftest.c >&5
configure:6017: /usr/bin/nm -B "conftest.o"
configure:6020: output
0000000000000000 B some_variable
configure:6027: result: BSD nm
configure:6030: checking whether ln -s works
configure:6034: result: yes
configure:6042: checking the maximum length of command line arguments
configure:6173: result: 1572864
configure:6221: checking how to convert x86_64-pc-linux-gnu file names to x86_64-pc-linux-gnu format
configure:6261: result: func_convert_file_noop
configure:6268: checking how to convert x86_64-pc-linux-gnu file names to toolchain format
configure:6288: result: func_convert_file_noop
configure:6295: checking for /usr/bin/ld option to reload object files
configure:6302: result: -r
configure:6376: checking for objdump
configure:6392: found /usr/bin/objdump
configure:6403: result: objdump
configure:6432: checking how to recognize dependent libraries
configure:6632: result: pass_all
configure:6717: checking for dlltool
configure:6747: result: no
configure:6774: checking how to associate runtime and link libraries
configure:6801: result: printf %s\n
configure:6861: checking for ar
configure:6877: found /usr/bin/ar
configure:6888: result: ar
configure:6941: checking for archiver @FILE support
configure:6958: gcc -c -g -O2  conftest.c >&5
configure:6958: $? = 0
configure:6961: ar cr libconftest.a @conftest.lst >&5
configure:6964: $? = 0
configure:6969: ar cr libconftest.a @conftest.lst >&5
ar: conftest.o: No such file or directory
configure:6972: $? = 1
configure:6984: result: @
configure:7042: checking for strip
configure:7058: found /usr/bin/strip
configure:7069: result: strip
configure:7141: checking for ranlib
configure:7157: found /usr/bin/ranlib
configure:7168: result: ranlib
configure:7270: checking command to parse /usr/bin/nm -B output from gcc object
configure:7423: gcc -c -g -O2  conftest.c >&5
configure:7426: $? = 0
configure:7430: /usr/bin/nm -B conftest.o \| sed -n -e 's/^.*[ ]\([ABCDGIRSTW][ABCDGIRSTW]*\)[ ][ ]*\([_A-Za-z][_A-Za-z0-9]*\)$/\1 \2 \2/p' | sed '/ __gnu_lto/d' \> conftest.nm
configure:7433: $? = 0
configure:7499: gcc -o conftest -g -O2   conftest.c conftstm.o >&5
configure:7502: $? = 0
configure:7540: result: ok
configure:7587: checking for sysroot
configure:7617: result: no
configure:7624: checking for a working dd
configure:7662: result: /usr/bin/dd
configure:7666: checking how to truncate binary pipes
configure:7681: result: /usr/bin/dd bs=4096 count=1
configure:7817: gcc -c -g -O2  conftest.c >&5
configure:7820: $? = 0
configure:8010: checking for mt
configure:8026: found /usr/bin/mt
configure:8037: result: mt
configure:8060: checking if mt is a manifest tool
configure:8066: mt '-?'
configure:8074: result: no
configure:8748: checking for dlfcn.h
configure:8748: gcc -c -g -O2  conftest.c >&5
configure:8748: $? = 0
configure:8748: result: yes
configure:9300: checking for objdir
configure:9315: result: .libs
configure:9579: checking if gcc supports -fno-rtti -fno-exceptions
configure:9597: gcc -c -g -O2  -fno-rtti -fno-exceptions conftest.c >&5
cc1: warning: command-line option '-fno-rtti' is valid for C++/D/ObjC++ but not for C
configure:9601: $? = 0
configure:9614: result: no
configure:9972: checking for gcc option to produce PIC
configure:9979: result: -fPIC -DPIC
configure:9987: checking if gcc PIC flag -fPIC -DPIC works
configure:10005: gcc -c -g -O2  -fPIC -DPIC -DPIC conftest.c >&5
configure:10009: $? = 0
configure:10022: result: yes
configure:10051: checking if gcc static flag -static works
configure:10079: result: yes
configure:10094: checking if gcc supports -c -o file.o
configure:10115: gcc -c -g -O2  -o out/conftest2.o conftest.c >&5
configure:10119: $? = 0
configure:10141: result: yes
configure:10149: checking if gcc supports -c -o file.o
configure:10196: result: yes
configure:10229: checking whether the gcc linker (/usr/bin/ld -m elf_x86_64) supports shared libraries
configure:11490: result: yes
configure:11527: checking whether -lc should be explicitly linked in
configure:11535: gcc -c -g -O2  conftest.c >&5
configure:11538: $? = 0
configure:11553: gcc -shared  -fPIC -DPIC conftest.o  -v -Wl,-soname -Wl,conftest -o conftest 2\>\&1 \| /usr/bin/grep  -lc  \>/dev/null 2\>\&1
configure:11556: $? = 0
configure:11570: result: no
configure:11730: checking dynamic linker characteristics
configure:12311: gcc -o conftest -g -O2   -Wl,-rpath -Wl,/foo conftest.c  >&5
configure:12311: $? = 0
configure:12548: result: GNU/Linux ld.so
configure:12670: checking how to hardcode library paths into programs
configure:12695: result: immediate
configure:13243: checking whether stripping libraries is possible
configure:13252: result: yes
configure:13294: checking if libtool supports shared libraries
configure:13296: result: yes
configure:13299: checking whether to build shared libraries
configure:13324: result: yes
configure:13327: checking whether to build static libraries
configure:13331: result: no
configure:13389: checking arpa/inet.h usability
configure:13389: gcc -c -g -O2  conftest.c >&5
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking arpa/inet.h presence
configure:13389: gcc -E  conftest.c
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking for arpa/inet.h
configure:13389: result: yes
configure:13389: checking byteswap.h usability
configure:13389: gcc -c -g -O2  conftest.c >&5
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking byteswap.h presence
configure:13389: gcc -E  conftest.c
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking for byteswap.h
configure:13389: result: yes
configure:13389: checking errno.h usability
configure:13389: gcc -c -g -O2  conftest.c >&5
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking errno.h presence
configure:13389: gcc -E  conftest.c
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking for errno.h
configure:13389: result: yes
configure:13389: checking fcntl.h usability
configure:13389: gcc -c -g -O2  conftest.c >&5
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking fcntl.h presence
configure:13389: gcc -E  conftest.c
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking for fcntl.h
configure:13389: result: yes
configure:13389: checking limits.h usability
configure:13389: gcc -c -g -O2  conftest.c >&5
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking limits.h presence
configure:13389: gcc -E  conftest.c
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking for limits.h
configure:13389: result: yes
configure:13389: checking linux/serial.h usability
configure:13389: gcc -c -g -O2  conftest.c >&5
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking linux/serial.h presence
configure:13389: gcc -E  conftest.c
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking for linux/serial.h
configure:13389: result: yes
configure:13389: checking netdb.h usability
configure:13389: gcc -c -g -O2  conftest.c >&5
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking netdb.h presence
configure:13389: gcc -E  conftest.c
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking for netdb.h
configure:13389: result: yes
configure:13389: checking netinet/in.h usability
configure:13389: gcc -c -g -O2  conftest.c >&5
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking netinet/in.h presence
configure:13389: gcc -E  conftest.c
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking for netinet/in.h
configure:13389: result: yes
configure:13389: checking netinet/tcp.h usability
configure:13389: gcc -c -g -O2  conftest.c >&5
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking netinet/tcp.h presence
configure:13389: gcc -E  conftest.c
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking for netinet/tcp.h
configure:13389: result: yes
configure:13389: checking sys/ioctl.h usability
configure:13389: gcc -c -g -O2  conftest.c >&5
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking sys/ioctl.h presence
configure:13389: gcc -E  conftest.c
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking for sys/ioctl.h
configure:13389: result: yes
configure:13389: checking sys/params.h usability
configure:13389: gcc -c -g -O2  conftest.c >&5
conftest.c:71:10: fatal error: sys/params.h: No such file or directory
   71 | #include <sys/params.h>
      |          ^~~~~~~~~~~~~~
compilation terminated.
configure:13389: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libmodbus"
| #define PACKAGE_TARNAME "libmodbus"
| #define PACKAGE_VERSION "3.1.6"
| #define PACKAGE_STRING "libmodbus 3.1.6"
| #define PACKAGE_BUGREPORT "https://github.com/stephane/libmodbus/issues"
| #define PACKAGE_URL "http://libmodbus.org/"
| #define PACKAGE "libmodbus"
| #define VERSION "3.1.6"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define HAVE_ARPA_INET_H 1
| #define HAVE_BYTESWAP_H 1
| #define HAVE_ERRNO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_LINUX_SERIAL_H 1
| #define HAVE_NETDB_H 1
| #define HAVE_NETINET_IN_H 1
| #define HAVE_NETINET_TCP_H 1
| #define HAVE_SYS_IOCTL_H 1
| /* end confdefs.h.  */
| #include <stdio.h>
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef STDC_HEADERS
| # include <stdlib.h>
| # include <stddef.h>
| #else
| # ifdef HAVE_STDLIB_H
| #  include <stdlib.h>
| # endif
| #endif
| #ifdef HAVE_STRING_H
| # if !defined STDC_HEADERS && defined HAVE_MEMORY_H
| #  include <memory.h>
| # endif
| # include <string.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| #include <sys/params.h>
configure:13389: result: no
configure:13389: checking sys/params.h presence
configure:13389: gcc -E  conftest.c
conftest.c:38:10: fatal error: sys/params.h: No such file or directory
   38 | #include <sys/params.h>
      |          ^~~~~~~~~~~~~~
compilation terminated.
configure:13389: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libmodbus"
| #define PACKAGE_TARNAME "libmodbus"
| #define PACKAGE_VERSION "3.1.6"
| #define PACKAGE_STRING "libmodbus 3.1.6"
| #define PACKAGE_BUGREPORT "https://github.com/stephane/libmodbus/issues"
| #define PACKAGE_URL "http://libmodbus.org/"
| #define PACKAGE "libmodbus"
| #define VERSION "3.1.6"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define HAVE_ARPA_INET_H 1
| #define HAVE_BYTESWAP_H 1
| #define HAVE_ERRNO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_LINUX_SERIAL_H 1
| #define HAVE_NETDB_H 1
| #define HAVE_NETINET_IN_H 1
| #define HAVE_NETINET_TCP_H 1
| #define HAVE_SYS_IOCTL_H 1
| /* end confdefs.h.  */
| #include <sys/params.h>
configure:13389: result: no
configure:13389: checking for sys/params.h
configure:13389: result: no
configure:13389: checking sys/socket.h usability
configure:13389: gcc -c -g -O2  conftest.c >&5
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking sys/socket.h presence
configure:13389: gcc -E  conftest.c
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking for sys/socket.h
configure:13389: result: yes
configure:13389: checking sys/time.h usability
configure:13389: gcc -c -g -O2  conftest.c >&5
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking sys/time.h presence
configure:13389: gcc -E  conftest.c
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking for sys/time.h
configure:13389: result: yes
configure:13389: checking for sys/types.h
configure:13389: result: yes
configure:13389: checking termios.h usability
configure:13389: gcc -c -g -O2  conftest.c >&5
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking termios.h presence
configure:13389: gcc -E  conftest.c
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking for termios.h
configure:13389: result: yes
configure:13389: checking time.h usability
configure:13389: gcc -c -g -O2  conftest.c >&5
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking time.h presence
configure:13389: gcc -E  conftest.c
configure:13389: $? = 0
configure:13389: result: yes
configure:13389: checking for time.h
configure:13389: result: yes
configure:13389: checking for unistd.h
configure:13389: result: yes
configure:13418: checking for asciidoc
configure:13446: result: no
configure:13456: checking for xmlto
configure:13484: result: no
configure:13497: checking whether to build documentation
configure:13499: result: no
configure:13502: WARNING: The tools to build the documentation aren't installed
configure:13517: checking whether __CYGWIN__ is declared
configure:13517: gcc -c -g -O2  conftest.c >&5
conftest.c: In function 'main':
conftest.c:84:10: error: '__CYGWIN__' undeclared (first use in this function)
   84 |   (void) __CYGWIN__;
      |          ^~~~~~~~~~
conftest.c:84:10: note: each undeclared identifier is reported only once for each function it appears in
configure:13517: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libmodbus"
| #define PACKAGE_TARNAME "libmodbus"
| #define PACKAGE_VERSION "3.1.6"
| #define PACKAGE_STRING "libmodbus 3.1.6"
| #define PACKAGE_BUGREPORT "https://github.com/stephane/libmodbus/issues"
| #define PACKAGE_URL "http://libmodbus.org/"
| #define PACKAGE "libmodbus"
| #define VERSION "3.1.6"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define HAVE_ARPA_INET_H 1
| #define HAVE_BYTESWAP_H 1
| #define HAVE_ERRNO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_LINUX_SERIAL_H 1
| #define HAVE_NETDB_H 1
| #define HAVE_NETINET_IN_H 1
| #define HAVE_NETINET_TCP_H 1
| #define HAVE_SYS_IOCTL_H 1
| #define HAVE_SYS_SOCKET_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_TERMIOS_H 1
| #define HAVE_TIME_H 1
| #define HAVE_UNISTD_H 1
| /* end confdefs.h.  */
| #include <stdio.h>
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef STDC_HEADERS
| # include <stdlib.h>
| # include <stddef.h>
| #else
| # ifdef HAVE_STDLIB_H
| #  include <stdlib.h>
| # endif
| #endif
| #ifdef HAVE_STRING_H
| # if !defined STDC_HEADERS && defined HAVE_MEMORY_H
| #  include <memory.h>
| # endif
| # include <string.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main ()
| {
| #ifndef __CYGWIN__
| #ifdef __cplusplus
|   (void) __CYGWIN__;
| #else
|   (void) __CYGWIN__;
| #endif
| #endif
| 
|   ;
|   return 0;
| }
configure:13517: result: no
configure:13533: checking for accept4
configure:13533: gcc -o conftest -g -O2   conftest.c  >&5
configure:13533: $? = 0
configure:13533: result: yes
configure:13533: checking for getaddrinfo
configure:13533: gcc -o conftest -g -O2   conftest.c  >&5
configure:13533: $? = 0
configure:13533: result: yes
configure:13533: checking for gettimeofday
configure:13533: gcc -o conftest -g -O2   conftest.c  >&5
configure:13533: $? = 0
configure:13533: result: yes
configure:13533: checking for inet_ntoa
configure:13533: gcc -o conftest -g -O2   conftest.c  >&5
configure:13533: $? = 0
configure:13533: result: yes
configure:13533: checking for select
configure:13533: gcc -o conftest -g -O2   conftest.c  >&5
configure:13533: $? = 0
configure:13533: result: yes
configure:13533: checking for socket
configure:13533: gcc -o conftest -g -O2   conftest.c  >&5
configure:13533: $? = 0
configure:13533: result: yes
configure:13533: checking for strerror
configure:13533: gcc -o conftest -g -O2   conftest.c  >&5
configure:13533: $? = 0
configure:13533: result: yes
configure:13533: checking for strlcpy
configure:13533: gcc -o conftest -g -O2   conftest.c  >&5
configure:13533: $? = 0
configure:13533: result: yes
configure:13549: checking for inline
configure:13565: gcc -c -g -O2  conftest.c >&5
configure:13565: $? = 0
configure:13573: result: inline
configure:13651: checking for g++
configure:13667: found /usr/bin/g++
configure:13678: result: g++
configure:13705: checking for C++ compiler version
configure:13714: g++ --version >&5
g++ (Ubuntu 13.3.0-6ubuntu2~24.04) 13.3.0
Copyright (C) 2023 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:13725: $? = 0
configure:13714: g++ -v >&5
Using built-in specs.
COLLECT_GCC=g++
COLLECT_LTO_WRAPPER=/usr/libexec/gcc/x86_64-linux-gnu/13/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 13.3.0-6ubuntu2~24.04' --with-bugurl=file:///usr/share/doc/gcc-13/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-13 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/libexec --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-libstdcxx-backtrace --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-13-fG75Ri/gcc-13-13.3.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/gcc-13-fG75Ri/gcc-13-13.3.0/debian/tmp-gcn/usr --enable-offload-defaulted --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 13.3.0 (Ubuntu 13.3.0-6ubuntu2~24.04) 
... rest of stderr output deleted ...
configure:13725: $? = 0
configure:13714: g++ -V >&5
g++: error: unrecognized command-line option '-V'
g++: fatal error: no input files
compilation terminated.
configure:13725: $? = 1
configure:13714: g++ -qversion >&5
g++: error: unrecognized command-line option '-qversion'; did you mean '--version'?
g++: fatal error: no input files
compilation terminated.
configure:13725: $? = 1
configure:13729: checking whether we are using the GNU C++ compiler
configure:13748: g++ -c   conftest.cpp >&5
configure:13748: $? = 0
configure:13757: result: yes
configure:13766: checking whether g++ accepts -g
configure:13786: g++ -c -g  conftest.cpp >&5
configure:13786: $? = 0
configure:13827: result: yes
configure:13852: checking dependency style of g++
configure:13963: result: gcc3
configure:13996: checking how to run the C++ preprocessor
configure:14023: g++ -E  conftest.cpp
configure:14023: $? = 0
configure:14037: g++ -E  conftest.cpp
conftest.cpp:54:10: fatal error: ac_nonexistent.h: No such file or directory
   54 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:14037: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libmodbus"
| #define PACKAGE_TARNAME "libmodbus"
| #define PACKAGE_VERSION "3.1.6"
| #define PACKAGE_STRING "libmodbus 3.1.6"
| #define PACKAGE_BUGREPORT "https://github.com/stephane/libmodbus/issues"
| #define PACKAGE_URL "http://libmodbus.org/"
| #define PACKAGE "libmodbus"
| #define VERSION "3.1.6"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define HAVE_ARPA_INET_H 1
| #define HAVE_BYTESWAP_H 1
| #define HAVE_ERRNO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_LINUX_SERIAL_H 1
| #define HAVE_NETDB_H 1
| #define HAVE_NETINET_IN_H 1
| #define HAVE_NETINET_TCP_H 1
| #define HAVE_SYS_IOCTL_H 1
| #define HAVE_SYS_SOCKET_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_TERMIOS_H 1
| #define HAVE_TIME_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_DECL___CYGWIN__ 0
| #define HAVE_ACCEPT4 1
| #define HAVE_GETADDRINFO 1
| #define HAVE_GETTIMEOFDAY 1
| #define HAVE_INET_NTOA 1
| #define HAVE_SELECT 1
| #define HAVE_SOCKET 1
| #define HAVE_STRERROR 1
| #define HAVE_STRLCPY 1
| #define WINVER 0x0501
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:14062: result: g++ -E
configure:14082: g++ -E  conftest.cpp
configure:14082: $? = 0
configure:14096: g++ -E  conftest.cpp
conftest.cpp:54:10: fatal error: ac_nonexistent.h: No such file or directory
   54 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:14096: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libmodbus"
| #define PACKAGE_TARNAME "libmodbus"
| #define PACKAGE_VERSION "3.1.6"
| #define PACKAGE_STRING "libmodbus 3.1.6"
| #define PACKAGE_BUGREPORT "https://github.com/stephane/libmodbus/issues"
| #define PACKAGE_URL "http://libmodbus.org/"
| #define PACKAGE "libmodbus"
| #define VERSION "3.1.6"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define HAVE_ARPA_INET_H 1
| #define HAVE_BYTESWAP_H 1
| #define HAVE_ERRNO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_LINUX_SERIAL_H 1
| #define HAVE_NETDB_H 1
| #define HAVE_NETINET_IN_H 1
| #define HAVE_NETINET_TCP_H 1
| #define HAVE_SYS_IOCTL_H 1
| #define HAVE_SYS_SOCKET_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_TERMIOS_H 1
| #define HAVE_TIME_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_DECL___CYGWIN__ 0
| #define HAVE_ACCEPT4 1
| #define HAVE_GETADDRINFO 1
| #define HAVE_GETTIMEOFDAY 1
| #define HAVE_INET_NTOA 1
| #define HAVE_SELECT 1
| #define HAVE_SOCKET 1
| #define HAVE_STRERROR 1
| #define HAVE_STRLCPY 1
| #define WINVER 0x0501
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:14258: checking for ld used by g++
configure:14325: result: /usr/bin/ld -m elf_x86_64
configure:14332: checking if the linker (/usr/bin/ld -m elf_x86_64) is GNU ld
configure:14347: result: yes
configure:14402: checking whether the g++ linker (/usr/bin/ld -m elf_x86_64) supports shared libraries
configure:15476: result: yes
configure:15512: g++ -c -g -O2  conftest.cpp >&5
configure:15515: $? = 0
configure:15996: checking for g++ option to produce PIC
configure:16003: result: -fPIC -DPIC
configure:16011: checking if g++ PIC flag -fPIC -DPIC works
configure:16029: g++ -c -g -O2  -fPIC -DPIC -DPIC conftest.cpp >&5
configure:16033: $? = 0
configure:16046: result: yes
configure:16069: checking if g++ static flag -static works
configure:16097: result: yes
configure:16109: checking if g++ supports -c -o file.o
configure:16130: g++ -c -g -O2  -o out/conftest2.o conftest.cpp >&5
configure:16134: $? = 0
configure:16156: result: yes
configure:16161: checking if g++ supports -c -o file.o
configure:16208: result: yes
configure:16238: checking whether the g++ linker (/usr/bin/ld -m elf_x86_64) supports shared libraries
configure:16278: result: yes
configure:16419: checking dynamic linker characteristics
configure:17164: result: GNU/Linux ld.so
configure:17229: checking how to hardcode library paths into programs
configure:17254: result: immediate
configure:17296: checking for int64_t
configure:17296: gcc -c -g -O2  conftest.c >&5
configure:17296: $? = 0
configure:17296: gcc -c -g -O2  conftest.c >&5
conftest.c: In function 'main':
conftest.c:92:67: warning: integer overflow in expression of type 'long int' results in '-9223372036854775808' [-Woverflow]
   92 |                  < (int64_t) (((((int64_t) 1 << N) << N) - 1) * 2 + 2))];
      |                                                                   ^
conftest.c:91:12: error: storage size of 'test_array' isn't constant
   91 | static int test_array [1 - 2 * !((int64_t) (((((int64_t) 1 << N) << N) - 1) * 2 + 1)
      |            ^~~~~~~~~~
configure:17296: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libmodbus"
| #define PACKAGE_TARNAME "libmodbus"
| #define PACKAGE_VERSION "3.1.6"
| #define PACKAGE_STRING "libmodbus 3.1.6"
| #define PACKAGE_BUGREPORT "https://github.com/stephane/libmodbus/issues"
| #define PACKAGE_URL "http://libmodbus.org/"
| #define PACKAGE "libmodbus"
| #define VERSION "3.1.6"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define HAVE_ARPA_INET_H 1
| #define HAVE_BYTESWAP_H 1
| #define HAVE_ERRNO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_LINUX_SERIAL_H 1
| #define HAVE_NETDB_H 1
| #define HAVE_NETINET_IN_H 1
| #define HAVE_NETINET_TCP_H 1
| #define HAVE_SYS_IOCTL_H 1
| #define HAVE_SYS_SOCKET_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_TERMIOS_H 1
| #define HAVE_TIME_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_DECL___CYGWIN__ 0
| #define HAVE_ACCEPT4 1
| #define HAVE_GETADDRINFO 1
| #define HAVE_GETTIMEOFDAY 1
| #define HAVE_INET_NTOA 1
| #define HAVE_SELECT 1
| #define HAVE_SOCKET 1
| #define HAVE_STRERROR 1
| #define HAVE_STRLCPY 1
| #define WINVER 0x0501
| /* end confdefs.h.  */
| #include <stdio.h>
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef STDC_HEADERS
| # include <stdlib.h>
| # include <stddef.h>
| #else
| # ifdef HAVE_STDLIB_H
| #  include <stdlib.h>
| # endif
| #endif
| #ifdef HAVE_STRING_H
| # if !defined STDC_HEADERS && defined HAVE_MEMORY_H
| #  include <memory.h>
| # endif
| # include <string.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| 	        enum { N = 64 / 2 - 1 };
| int
| main ()
| {
| static int test_array [1 - 2 * !((int64_t) (((((int64_t) 1 << N) << N) - 1) * 2 + 1)
| 		 < (int64_t) (((((int64_t) 1 << N) << N) - 1) * 2 + 2))];
| test_array [0] = 0;
| return test_array [0];
| 
|   ;
|   return 0;
| }
configure:17296: result: yes
configure:17307: checking for size_t
configure:17307: gcc -c -g -O2  conftest.c >&5
configure:17307: $? = 0
configure:17307: gcc -c -g -O2  conftest.c >&5
conftest.c: In function 'main':
conftest.c:90:21: error: expected expression before ')' token
   90 | if (sizeof ((size_t)))
      |                     ^
configure:17307: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libmodbus"
| #define PACKAGE_TARNAME "libmodbus"
| #define PACKAGE_VERSION "3.1.6"
| #define PACKAGE_STRING "libmodbus 3.1.6"
| #define PACKAGE_BUGREPORT "https://github.com/stephane/libmodbus/issues"
| #define PACKAGE_URL "http://libmodbus.org/"
| #define PACKAGE "libmodbus"
| #define VERSION "3.1.6"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define HAVE_ARPA_INET_H 1
| #define HAVE_BYTESWAP_H 1
| #define HAVE_ERRNO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_LINUX_SERIAL_H 1
| #define HAVE_NETDB_H 1
| #define HAVE_NETINET_IN_H 1
| #define HAVE_NETINET_TCP_H 1
| #define HAVE_SYS_IOCTL_H 1
| #define HAVE_SYS_SOCKET_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_TERMIOS_H 1
| #define HAVE_TIME_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_DECL___CYGWIN__ 0
| #define HAVE_ACCEPT4 1
| #define HAVE_GETADDRINFO 1
| #define HAVE_GETTIMEOFDAY 1
| #define HAVE_INET_NTOA 1
| #define HAVE_SELECT 1
| #define HAVE_SOCKET 1
| #define HAVE_STRERROR 1
| #define HAVE_STRLCPY 1
| #define WINVER 0x0501
| /* end confdefs.h.  */
| #include <stdio.h>
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef STDC_HEADERS
| # include <stdlib.h>
| # include <stddef.h>
| #else
| # ifdef HAVE_STDLIB_H
| #  include <stdlib.h>
| # endif
| #endif
| #ifdef HAVE_STRING_H
| # if !defined STDC_HEADERS && defined HAVE_MEMORY_H
| #  include <memory.h>
| # endif
| # include <string.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main ()
| {
| if (sizeof ((size_t)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:17307: result: yes
configure:17318: checking for ssize_t
configure:17318: gcc -c -g -O2  conftest.c >&5
configure:17318: $? = 0
configure:17318: gcc -c -g -O2  conftest.c >&5
conftest.c: In function 'main':
conftest.c:90:22: error: expected expression before ')' token
   90 | if (sizeof ((ssize_t)))
      |                      ^
configure:17318: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libmodbus"
| #define PACKAGE_TARNAME "libmodbus"
| #define PACKAGE_VERSION "3.1.6"
| #define PACKAGE_STRING "libmodbus 3.1.6"
| #define PACKAGE_BUGREPORT "https://github.com/stephane/libmodbus/issues"
| #define PACKAGE_URL "http://libmodbus.org/"
| #define PACKAGE "libmodbus"
| #define VERSION "3.1.6"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define HAVE_ARPA_INET_H 1
| #define HAVE_BYTESWAP_H 1
| #define HAVE_ERRNO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_LINUX_SERIAL_H 1
| #define HAVE_NETDB_H 1
| #define HAVE_NETINET_IN_H 1
| #define HAVE_NETINET_TCP_H 1
| #define HAVE_SYS_IOCTL_H 1
| #define HAVE_SYS_SOCKET_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_TERMIOS_H 1
| #define HAVE_TIME_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_DECL___CYGWIN__ 0
| #define HAVE_ACCEPT4 1
| #define HAVE_GETADDRINFO 1
| #define HAVE_GETTIMEOFDAY 1
| #define HAVE_INET_NTOA 1
| #define HAVE_SELECT 1
| #define HAVE_SOCKET 1
| #define HAVE_STRERROR 1
| #define HAVE_STRLCPY 1
| #define WINVER 0x0501
| /* end confdefs.h.  */
| #include <stdio.h>
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef STDC_HEADERS
| # include <stdlib.h>
| # include <stddef.h>
| #else
| # ifdef HAVE_STDLIB_H
| #  include <stdlib.h>
| # endif
| #endif
| #ifdef HAVE_STRING_H
| # if !defined STDC_HEADERS && defined HAVE_MEMORY_H
| #  include <memory.h>
| # endif
| # include <string.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main ()
| {
| if (sizeof ((ssize_t)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:17318: result: yes
configure:17329: checking for uint16_t
configure:17329: gcc -c -g -O2  conftest.c >&5
configure:17329: $? = 0
configure:17329: result: yes
configure:17341: checking for uint32_t
configure:17341: gcc -c -g -O2  conftest.c >&5
configure:17341: $? = 0
configure:17341: result: yes
configure:17355: checking for uint8_t
configure:17355: gcc -c -g -O2  conftest.c >&5
configure:17355: $? = 0
configure:17355: result: yes
configure:17374: checking winsock2.h usability
configure:17374: gcc -c -g -O2  conftest.c >&5
conftest.c:87:10: fatal error: winsock2.h: No such file or directory
   87 | #include <winsock2.h>
      |          ^~~~~~~~~~~~
compilation terminated.
configure:17374: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libmodbus"
| #define PACKAGE_TARNAME "libmodbus"
| #define PACKAGE_VERSION "3.1.6"
| #define PACKAGE_STRING "libmodbus 3.1.6"
| #define PACKAGE_BUGREPORT "https://github.com/stephane/libmodbus/issues"
| #define PACKAGE_URL "http://libmodbus.org/"
| #define PACKAGE "libmodbus"
| #define VERSION "3.1.6"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define HAVE_ARPA_INET_H 1
| #define HAVE_BYTESWAP_H 1
| #define HAVE_ERRNO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_LINUX_SERIAL_H 1
| #define HAVE_NETDB_H 1
| #define HAVE_NETINET_IN_H 1
| #define HAVE_NETINET_TCP_H 1
| #define HAVE_SYS_IOCTL_H 1
| #define HAVE_SYS_SOCKET_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_TERMIOS_H 1
| #define HAVE_TIME_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_DECL___CYGWIN__ 0
| #define HAVE_ACCEPT4 1
| #define HAVE_GETADDRINFO 1
| #define HAVE_GETTIMEOFDAY 1
| #define HAVE_INET_NTOA 1
| #define HAVE_SELECT 1
| #define HAVE_SOCKET 1
| #define HAVE_STRERROR 1
| #define HAVE_STRLCPY 1
| #define WINVER 0x0501
| /* end confdefs.h.  */
| #include <stdio.h>
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef STDC_HEADERS
| # include <stdlib.h>
| # include <stddef.h>
| #else
| # ifdef HAVE_STDLIB_H
| #  include <stdlib.h>
| # endif
| #endif
| #ifdef HAVE_STRING_H
| # if !defined STDC_HEADERS && defined HAVE_MEMORY_H
| #  include <memory.h>
| # endif
| # include <string.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| #include <winsock2.h>
configure:17374: result: no
configure:17374: checking winsock2.h presence
configure:17374: gcc -E  conftest.c
conftest.c:54:10: fatal error: winsock2.h: No such file or directory
   54 | #include <winsock2.h>
      |          ^~~~~~~~~~~~
compilation terminated.
configure:17374: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libmodbus"
| #define PACKAGE_TARNAME "libmodbus"
| #define PACKAGE_VERSION "3.1.6"
| #define PACKAGE_STRING "libmodbus 3.1.6"
| #define PACKAGE_BUGREPORT "https://github.com/stephane/libmodbus/issues"
| #define PACKAGE_URL "http://libmodbus.org/"
| #define PACKAGE "libmodbus"
| #define VERSION "3.1.6"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define HAVE_ARPA_INET_H 1
| #define HAVE_BYTESWAP_H 1
| #define HAVE_ERRNO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_LINUX_SERIAL_H 1
| #define HAVE_NETDB_H 1
| #define HAVE_NETINET_IN_H 1
| #define HAVE_NETINET_TCP_H 1
| #define HAVE_SYS_IOCTL_H 1
| #define HAVE_SYS_SOCKET_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_TERMIOS_H 1
| #define HAVE_TIME_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_DECL___CYGWIN__ 0
| #define HAVE_ACCEPT4 1
| #define HAVE_GETADDRINFO 1
| #define HAVE_GETTIMEOFDAY 1
| #define HAVE_INET_NTOA 1
| #define HAVE_SELECT 1
| #define HAVE_SOCKET 1
| #define HAVE_STRERROR 1
| #define HAVE_STRLCPY 1
| #define WINVER 0x0501
| /* end confdefs.h.  */
| #include <winsock2.h>
configure:17374: result: no
configure:17374: checking for winsock2.h
configure:17374: result: no
configure:17391: checking whether TIOCSRS485 is declared
configure:17391: gcc -c -g -O2  conftest.c >&5
configure:17391: $? = 0
configure:17391: result: yes
configure:17404: checking whether TIOCM_RTS is declared
configure:17404: gcc -c -g -O2  conftest.c >&5
configure:17404: $? = 0
configure:17404: result: yes
configure:17549: checking that generated files are newer than configure
configure:17555: result: done
configure:17598: creating ./config.status

## ---------------------- ##
## Running config.status. ##
## ---------------------- ##

This file was extended by libmodbus config.status 3.1.6, which was
generated by GNU Autoconf 2.69.  Invocation command line was

  CONFIG_FILES    = 
  CONFIG_HEADERS  = 
  CONFIG_LINKS    = 
  CONFIG_COMMANDS = 
  $ ./config.status 

on root

config.status:1207: creating Makefile
config.status:1207: creating src/Makefile
config.status:1207: creating src/modbus-version.h
config.status:1207: creating src/win32/modbus.dll.manifest
config.status:1207: creating tests/Makefile
config.status:1207: creating doc/Makefile
config.status:1207: creating libmodbus.pc
config.status:1207: creating config.h
config.status:1207: creating tests/unit-test.h
config.status:1388: tests/unit-test.h is unchanged
config.status:1436: executing depfiles commands
config.status:1513: cd src       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
config.status:1518: $? = 0
config.status:1513: cd tests       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
config.status:1518: $? = 0
config.status:1436: executing libtool commands
configure:20038: result:
        libmodbus 3.1.6
        ===============

        prefix:                 /home/<USER>/home/<USER>/modbus/libmodbus_install
        sysconfdir:             ${prefix}/etc
        libdir:                 ${exec_prefix}/lib
        includedir:             ${prefix}/include

        compiler:               gcc
        cflags:                 -g -O2
        ldflags:                

        documentation:          no
        tests:                  yes


## ---------------- ##
## Cache variables. ##
## ---------------- ##

ac_cv_build=x86_64-pc-linux-gnu
ac_cv_c_compiler_gnu=yes
ac_cv_c_inline=inline
ac_cv_c_int64_t=yes
ac_cv_c_uint16_t=yes
ac_cv_c_uint32_t=yes
ac_cv_c_uint8_t=yes
ac_cv_cxx_compiler_gnu=yes
ac_cv_env_CCC_set=
ac_cv_env_CCC_value=
ac_cv_env_CC_set=
ac_cv_env_CC_value=
ac_cv_env_CFLAGS_set=
ac_cv_env_CFLAGS_value=
ac_cv_env_CPPFLAGS_set=
ac_cv_env_CPPFLAGS_value=
ac_cv_env_CPP_set=
ac_cv_env_CPP_value=
ac_cv_env_CXXCPP_set=
ac_cv_env_CXXCPP_value=
ac_cv_env_CXXFLAGS_set=
ac_cv_env_CXXFLAGS_value=
ac_cv_env_CXX_set=
ac_cv_env_CXX_value=
ac_cv_env_LDFLAGS_set=
ac_cv_env_LDFLAGS_value=
ac_cv_env_LIBS_set=
ac_cv_env_LIBS_value=
ac_cv_env_LT_SYS_LIBRARY_PATH_set=
ac_cv_env_LT_SYS_LIBRARY_PATH_value=
ac_cv_env_build_alias_set=
ac_cv_env_build_alias_value=
ac_cv_env_host_alias_set=
ac_cv_env_host_alias_value=
ac_cv_env_target_alias_set=
ac_cv_env_target_alias_value=
ac_cv_func_accept4=yes
ac_cv_func_getaddrinfo=yes
ac_cv_func_gettimeofday=yes
ac_cv_func_inet_ntoa=yes
ac_cv_func_select=yes
ac_cv_func_socket=yes
ac_cv_func_strerror=yes
ac_cv_func_strlcpy=yes
ac_cv_have_decl_TIOCM_RTS=yes
ac_cv_have_decl_TIOCSRS485=yes
ac_cv_have_decl___CYGWIN__=no
ac_cv_header_arpa_inet_h=yes
ac_cv_header_byteswap_h=yes
ac_cv_header_dlfcn_h=yes
ac_cv_header_errno_h=yes
ac_cv_header_fcntl_h=yes
ac_cv_header_inttypes_h=yes
ac_cv_header_limits_h=yes
ac_cv_header_linux_serial_h=yes
ac_cv_header_memory_h=yes
ac_cv_header_minix_config_h=no
ac_cv_header_netdb_h=yes
ac_cv_header_netinet_in_h=yes
ac_cv_header_netinet_tcp_h=yes
ac_cv_header_stdc=yes
ac_cv_header_stdint_h=yes
ac_cv_header_stdlib_h=yes
ac_cv_header_string_h=yes
ac_cv_header_strings_h=yes
ac_cv_header_sys_ioctl_h=yes
ac_cv_header_sys_params_h=no
ac_cv_header_sys_socket_h=yes
ac_cv_header_sys_stat_h=yes
ac_cv_header_sys_time_h=yes
ac_cv_header_sys_types_h=yes
ac_cv_header_termios_h=yes
ac_cv_header_time_h=yes
ac_cv_header_unistd_h=yes
ac_cv_header_winsock2_h=no
ac_cv_host=x86_64-pc-linux-gnu
ac_cv_objext=o
ac_cv_path_EGREP='/usr/bin/grep -E'
ac_cv_path_FGREP='/usr/bin/grep -F'
ac_cv_path_GREP=/usr/bin/grep
ac_cv_path_SED=/usr/bin/sed
ac_cv_path_install='/usr/bin/install -c'
ac_cv_path_lt_DD=/usr/bin/dd
ac_cv_path_mkdir=/usr/bin/mkdir
ac_cv_prog_AWK=mawk
ac_cv_prog_CPP='gcc -E'
ac_cv_prog_CXXCPP='g++ -E'
ac_cv_prog_ac_ct_AR=ar
ac_cv_prog_ac_ct_CC=gcc
ac_cv_prog_ac_ct_CXX=g++
ac_cv_prog_ac_ct_MANIFEST_TOOL=mt
ac_cv_prog_ac_ct_OBJDUMP=objdump
ac_cv_prog_ac_ct_RANLIB=ranlib
ac_cv_prog_ac_ct_STRIP=strip
ac_cv_prog_ac_libmodbus_have_asciidoc=no
ac_cv_prog_ac_libmodbus_have_xmlto=no
ac_cv_prog_cc_c89=
ac_cv_prog_cc_c99=
ac_cv_prog_cc_g=yes
ac_cv_prog_cc_stdc=
ac_cv_prog_cxx_g=yes
ac_cv_prog_make_make_set=yes
ac_cv_safe_to_define___extensions__=yes
ac_cv_sys_file_offset_bits=no
ac_cv_sys_largefile_CC=no
ac_cv_type_size_t=yes
ac_cv_type_ssize_t=yes
am_cv_CC_dependencies_compiler_type=gcc3
am_cv_CXX_dependencies_compiler_type=gcc3
am_cv_make_support_nested_variables=yes
am_cv_prog_cc_c_o=yes
am_cv_prog_tar_pax=gnutar
lt_cv_ar_at_file=@
lt_cv_archive_cmds_need_lc=no
lt_cv_deplibs_check_method=pass_all
lt_cv_file_magic_cmd='$MAGIC_CMD'
lt_cv_file_magic_test_file=
lt_cv_ld_reload_flag=-r
lt_cv_nm_interface='BSD nm'
lt_cv_objdir=.libs
lt_cv_path_LD=/usr/bin/ld
lt_cv_path_LDCXX='/usr/bin/ld -m elf_x86_64'
lt_cv_path_NM='/usr/bin/nm -B'
lt_cv_path_mainfest_tool=no
lt_cv_prog_compiler_c_o=yes
lt_cv_prog_compiler_c_o_CXX=yes
lt_cv_prog_compiler_pic='-fPIC -DPIC'
lt_cv_prog_compiler_pic_CXX='-fPIC -DPIC'
lt_cv_prog_compiler_pic_works=yes
lt_cv_prog_compiler_pic_works_CXX=yes
lt_cv_prog_compiler_rtti_exceptions=no
lt_cv_prog_compiler_static_works=yes
lt_cv_prog_compiler_static_works_CXX=yes
lt_cv_prog_gnu_ld=yes
lt_cv_prog_gnu_ldcxx=yes
lt_cv_sharedlib_from_linklib_cmd='printf %s\n'
lt_cv_shlibpath_overrides_runpath=yes
lt_cv_sys_global_symbol_pipe='sed -n -e '\''s/^.*[	 ]\([ABCDGIRSTW][ABCDGIRSTW]*\)[	 ][	 ]*\([_A-Za-z][_A-Za-z0-9]*\)$/\1 \2 \2/p'\'' | sed '\''/ __gnu_lto/d'\'''
lt_cv_sys_global_symbol_to_c_name_address='sed -n -e '\''s/^: \(.*\) .*$/  {"\1", (void *) 0},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/  {"\1", (void *) \&\1},/p'\'''
lt_cv_sys_global_symbol_to_c_name_address_lib_prefix='sed -n -e '\''s/^: \(.*\) .*$/  {"\1", (void *) 0},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(lib.*\)$/  {"\1", (void *) \&\1},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/  {"lib\1", (void *) \&\1},/p'\'''
lt_cv_sys_global_symbol_to_cdecl='sed -n -e '\''s/^T .* \(.*\)$/extern int \1();/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/extern char \1;/p'\'''
lt_cv_sys_global_symbol_to_import=
lt_cv_sys_max_cmd_len=1572864
lt_cv_to_host_file_cmd=func_convert_file_noop
lt_cv_to_tool_file_cmd=func_convert_file_noop
lt_cv_truncate_bin='/usr/bin/dd bs=4096 count=1'

## ----------------- ##
## Output variables. ##
## ----------------- ##

ACLOCAL='${SHELL} /home/<USER>/home/<USER>/modbus/libModbus-3.1.6-master/build-aux/missing aclocal-1.16'
AMDEPBACKSLASH='\'
AMDEP_FALSE='#'
AMDEP_TRUE=''
AMTAR='$${TAR-tar}'
AM_BACKSLASH='\'
AM_DEFAULT_V='$(AM_DEFAULT_VERBOSITY)'
AM_DEFAULT_VERBOSITY='0'
AM_V='$(V)'
AR='ar'
AS='as'
AUTOCONF='${SHELL} /home/<USER>/home/<USER>/modbus/libModbus-3.1.6-master/build-aux/missing autoconf'
AUTOHEADER='${SHELL} /home/<USER>/home/<USER>/modbus/libModbus-3.1.6-master/build-aux/missing autoheader'
AUTOMAKE='${SHELL} /home/<USER>/home/<USER>/modbus/libModbus-3.1.6-master/build-aux/missing automake-1.16'
AWK='mawk'
BUILD_DOC_FALSE=''
BUILD_DOC_TRUE='#'
BUILD_TESTS_FALSE='#'
BUILD_TESTS_TRUE=''
CC='gcc'
CCDEPMODE='depmode=gcc3'
CFLAGS='-g -O2'
CPP='gcc -E'
CPPFLAGS=''
CXX='g++'
CXXCPP='g++ -E'
CXXDEPMODE='depmode=gcc3'
CXXFLAGS='-g -O2'
CYGPATH_W='echo'
DEFS='-DHAVE_CONFIG_H'
DEPDIR='.deps'
DLLTOOL='false'
DSYMUTIL=''
DUMPBIN=''
ECHO_C=''
ECHO_N='-n'
ECHO_T=''
EGREP='/usr/bin/grep -E'
EXEEXT=''
FGREP='/usr/bin/grep -F'
GREP='/usr/bin/grep'
INSTALL_DATA='${INSTALL} -m 644'
INSTALL_PROGRAM='${INSTALL}'
INSTALL_SCRIPT='${INSTALL}'
INSTALL_STRIP_PROGRAM='$(install_sh) -c -s'
LD='/usr/bin/ld -m elf_x86_64'
LDFLAGS=''
LIBMODBUS_LT_VERSION_INFO='6:0:1'
LIBMODBUS_VERSION='3.1.6'
LIBMODBUS_VERSION_MAJOR='3'
LIBMODBUS_VERSION_MICRO='6'
LIBMODBUS_VERSION_MINOR='1'
LIBOBJS=''
LIBS=''
LIBTOOL='$(SHELL) $(top_builddir)/libtool'
LIPO=''
LN_S='ln -s'
LTLIBOBJS=''
LT_SYS_LIBRARY_PATH=''
MAKEINFO='${SHELL} /home/<USER>/home/<USER>/modbus/libModbus-3.1.6-master/build-aux/missing makeinfo'
MANIFEST_TOOL=':'
MKDIR_P='/usr/bin/mkdir -p'
NM='/usr/bin/nm -B'
NMEDIT=''
OBJDUMP='objdump'
OBJEXT='o'
OS_QNX_FALSE=''
OS_QNX_TRUE='#'
OS_WIN32_FALSE=''
OS_WIN32_TRUE='#'
OTOOL64=''
OTOOL=''
PACKAGE='libmodbus'
PACKAGE_BUGREPORT='https://github.com/stephane/libmodbus/issues'
PACKAGE_NAME='libmodbus'
PACKAGE_STRING='libmodbus 3.1.6'
PACKAGE_TARNAME='libmodbus'
PACKAGE_URL='http://libmodbus.org/'
PACKAGE_VERSION='3.1.6'
PATH_SEPARATOR=':'
RANLIB='ranlib'
SED='/usr/bin/sed'
SET_MAKE=''
SHELL='/bin/bash'
STRIP='strip'
VERSION='3.1.6'
ac_ct_AR='ar'
ac_ct_CC='gcc'
ac_ct_CXX='g++'
ac_ct_DUMPBIN=''
ac_libmodbus_have_asciidoc='no'
ac_libmodbus_have_xmlto='no'
am__EXEEXT_FALSE=''
am__EXEEXT_TRUE='#'
am__fastdepCC_FALSE='#'
am__fastdepCC_TRUE=''
am__fastdepCXX_FALSE='#'
am__fastdepCXX_TRUE=''
am__include='include'
am__isrc=''
am__leading_dot='.'
am__nodep='_no'
am__quote=''
am__tar='tar --format=posix -chf - "$$tardir"'
am__untar='tar -xf -'
bindir='${exec_prefix}/bin'
build='x86_64-pc-linux-gnu'
build_alias=''
build_cpu='x86_64'
build_os='linux-gnu'
build_vendor='pc'
datadir='${datarootdir}'
datarootdir='${prefix}/share'
docdir='${datarootdir}/doc/${PACKAGE_TARNAME}'
dvidir='${docdir}'
exec_prefix='${prefix}'
host='x86_64-pc-linux-gnu'
host_alias=''
host_cpu='x86_64'
host_os='linux-gnu'
host_vendor='pc'
htmldir='${docdir}'
includedir='${prefix}/include'
infodir='${datarootdir}/info'
install_sh='${SHELL} /home/<USER>/home/<USER>/modbus/libModbus-3.1.6-master/build-aux/install-sh'
libdir='${exec_prefix}/lib'
libexecdir='${exec_prefix}/libexec'
localedir='${datarootdir}/locale'
localstatedir='${prefix}/var'
mandir='${datarootdir}/man'
mkdir_p='$(MKDIR_P)'
my_CFLAGS='-Wall -Wmissing-declarations -Wmissing-prototypes -Wnested-externs -Wpointer-arith -Wpointer-arith -Wsign-compare -Wchar-subscripts -Wstrict-prototypes -Wshadow -Wformat-security'
oldincludedir='/usr/include'
pdfdir='${docdir}'
prefix='/home/<USER>/home/<USER>/modbus/libmodbus_install'
program_transform_name='s,x,x,'
psdir='${docdir}'
sbindir='${exec_prefix}/sbin'
sharedstatedir='${prefix}/com'
sysconfdir='${prefix}/etc'
target_alias=''

## ----------- ##
## confdefs.h. ##
## ----------- ##

/* confdefs.h */
#define PACKAGE_NAME "libmodbus"
#define PACKAGE_TARNAME "libmodbus"
#define PACKAGE_VERSION "3.1.6"
#define PACKAGE_STRING "libmodbus 3.1.6"
#define PACKAGE_BUGREPORT "https://github.com/stephane/libmodbus/issues"
#define PACKAGE_URL "http://libmodbus.org/"
#define PACKAGE "libmodbus"
#define VERSION "3.1.6"
#define STDC_HEADERS 1
#define HAVE_SYS_TYPES_H 1
#define HAVE_SYS_STAT_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_MEMORY_H 1
#define HAVE_STRINGS_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_STDINT_H 1
#define HAVE_UNISTD_H 1
#define __EXTENSIONS__ 1
#define _ALL_SOURCE 1
#define _GNU_SOURCE 1
#define _POSIX_PTHREAD_SEMANTICS 1
#define _TANDEM_SOURCE 1
#define HAVE_DLFCN_H 1
#define LT_OBJDIR ".libs/"
#define HAVE_ARPA_INET_H 1
#define HAVE_BYTESWAP_H 1
#define HAVE_ERRNO_H 1
#define HAVE_FCNTL_H 1
#define HAVE_LIMITS_H 1
#define HAVE_LINUX_SERIAL_H 1
#define HAVE_NETDB_H 1
#define HAVE_NETINET_IN_H 1
#define HAVE_NETINET_TCP_H 1
#define HAVE_SYS_IOCTL_H 1
#define HAVE_SYS_SOCKET_H 1
#define HAVE_SYS_TIME_H 1
#define HAVE_SYS_TYPES_H 1
#define HAVE_TERMIOS_H 1
#define HAVE_TIME_H 1
#define HAVE_UNISTD_H 1
#define HAVE_DECL___CYGWIN__ 0
#define HAVE_ACCEPT4 1
#define HAVE_GETADDRINFO 1
#define HAVE_GETTIMEOFDAY 1
#define HAVE_INET_NTOA 1
#define HAVE_SELECT 1
#define HAVE_SOCKET 1
#define HAVE_STRERROR 1
#define HAVE_STRLCPY 1
#define WINVER 0x0501
#define HAVE_DECL_TIOCSRS485 1
#define HAVE_DECL_TIOCM_RTS 1

configure: exit 0
