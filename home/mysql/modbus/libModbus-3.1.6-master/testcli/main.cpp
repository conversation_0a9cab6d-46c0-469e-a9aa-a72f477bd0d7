#include <QCoreApplication>
#include "modbus.h"
#include <QDebug>

int main(int argc, char *argv[])
{
    QCoreApplication a(argc, argv);

    auto ctx = modbus_new_over_tcp("127.0.0.1", 502);
    modbus_connect(ctx);
    modbus_set_slave(ctx, 1);
    QVector<uint16_t> dest;
    dest.resize(10);
    QVector<uint8_t> bits;
    bits.resize(10);
    qDebug() << modbus_read_registers(ctx, 0, 1, dest.data());
    qDebug() << dest;
    qDebug() << modbus_read_bits(ctx, 0, 10, bits.data());
    qDebug() << bits;
    bits[1] = 2;
    modbus_write_bit(ctx, 1, 2);
    modbus_free(ctx);


    return a.exec();
}
