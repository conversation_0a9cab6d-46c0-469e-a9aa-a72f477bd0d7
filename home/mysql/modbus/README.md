# Modbus 协议服务

基于 libmodbus-3.1.6 构建的完整 Modbus 协议服务，融合了现有 xj_svg 系统的业务逻辑模式，支持串口通信、Redis 发布/订阅和多设备管理。

## 特性

- **完整的 Modbus 支持**: 基于 libmodbus 库，支持 RTU 和 TCP 两种通信模式
- **兼容现有系统**: 与 xj_svg 系统的配置格式和业务逻辑完全兼容
- **Redis 集成**: 支持 Redis 发布/订阅机制，实现实时数据交换
- **多设备管理**: 支持同时管理多个 Modbus 设备
- **数据类型支持**: 支持遥测(YC)、遥信(YX)、遥调(YT)、遥控(YK)四种数据类型
- **自动重连**: 内置连接监控和自动重连机制
- **线程安全**: 全面的多线程支持和线程安全设计
- **配置驱动**: 基于 INI 配置文件的灵活配置系统
- **日志系统**: 完整的异步日志系统，支持多种输出格式

## 系统架构

```
src/
├── types/          # 数据类型定义
├── utils/          # 工具类 (日志、线程池等)
├── comm/           # 通信层 (RTU/TCP)
├── data/           # 数据处理层
├── redis/          # Redis 集成层
├── config/         # 配置管理
├── core/           # 核心服务
├── examples/       # 示例程序
└── tests/          # 测试程序
```

## 依赖库

### 必需依赖
- **libmodbus**: Modbus 协议实现库
- **pthread**: 多线程支持
- **C++17**: 编译器支持

### 可选依赖
- **hiredis**: Redis 客户端库 (用于 Redis 集成)

## 编译安装

### 1. 安装依赖

#### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install build-essential cmake
sudo apt-get install libmodbus-dev libhiredis-dev
```

#### CentOS/RHEL
```bash
sudo yum install gcc-c++ cmake
sudo yum install libmodbus-devel hiredis-devel
```

### 2. 编译 libmodbus (如果系统没有)

```bash
cd libModbus-3.1.6-master
./configure
make
sudo make install
```

### 3. 编译项目

```bash
mkdir build
cd build
cmake ..
make
```

### 4. 运行测试

```bash
make test
```

## 配置文件

### 主配置文件 (modbus_config.ini)

```ini
[Service]
RedisIP = 127.0.0.1
RedisPort = 6379
ReportTime = 200

[Device1]
DeviceID = 1
DeviceName = XJ-SVG-1
CommType = 0        # 0=RTU, 1=TCP
PointFile = points.ini
Device = /dev/ttyS1
Baud = 9600
Parity = N
DataBit = 8
StopBit = 1
Timeout = 1000
```

### 点表配置文件 (points.ini)

```ini
[PointList]
# 格式: 数据类型:点号 = 功能码,从站,地址,数量,比例,偏移,描述
1:00001 = 3,1,0,10,1.0,0.0,电压测量值
2:00001 = 1,1,0,16,1.0,0.0,开关状态
3:00001 = 6,1,100,1,1.0,0.0,电压设定值
4:00001 = 5,1,1000,1,1.0,0.0,主开关控制
```

## 使用示例

### 基础 RTU 通信

```cpp
#include "comm/modbus_rtu_comm.h"

// 配置 RTU 参数
RtuCommParam rtu_param;
rtu_param.device = "/dev/ttyS1";
rtu_param.baud = 9600;
rtu_param.parity = 'N';

// 创建通信实例
ModbusRtuComm rtu_comm(rtu_param);

// 初始化和连接
rtu_comm.Initialize();
rtu_comm.Connect();

// 设置从站地址
rtu_comm.SetSlaveId(1);

// 读取保持寄存器
auto result = rtu_comm.ReadHoldingRegisters(0, 10);
if (result.IsSuccess()) {
    for (auto value : result.data) {
        std::cout << value << " ";
    }
}
```

### 基础 TCP 通信

```cpp
#include "comm/modbus_tcp_comm.h"

// 配置 TCP 参数
TcpCommParam tcp_param;
tcp_param.ip = "*************";
tcp_param.port = 502;
tcp_param.timeout_ms = 1000;

// 创建通信实例
ModbusTcpComm tcp_comm(tcp_param);

// 初始化和连接
tcp_comm.Initialize();
tcp_comm.Connect();

// 设置从站地址
tcp_comm.SetSlaveId(1);

// 读取保持寄存器
auto result = tcp_comm.ReadHoldingRegisters(0, 10);
if (result.IsSuccess()) {
    for (auto value : result.data) {
        std::cout << value << " ";
    }
}

// TCP 特有功能：多连接管理
tcp_comm.AddConnection("*************", 502);
tcp_comm.SwitchConnection("*************", 502);
```

### 配置管理

```cpp
#include "config/config_manager.h"

ConfigManager config;
config.LoadConfig("modbus_config.ini");

// 获取服务参数
auto service_param = config.GetServiceParam();

// 获取设备参数
auto device_param = config.GetDeviceParam(1);
```

### 点表加载

```cpp
#include "config/point_table_loader.h"

PointTableLoader loader;
auto points = loader.LoadPointTable("points.ini");

// 按数据类型过滤
auto yc_points = loader.FilterByDataType(points.data, DataType::YC);
```

## 运行示例

### 配置解析示例
```bash
./build/bin/example_config_parser
```

### RTU 通信示例
```bash
./build/bin/example_rtu_basic
```

### TCP 通信示例
```bash
./build/bin/example_tcp_basic
```

### 日志系统测试
```bash
./build/bin/test_logger
```

### 线程池测试
```bash
./build/bin/test_thread_pool
```

## API 文档

### 核心类

#### ModbusCommInterface
Modbus 通信接口基类，定义了统一的通信接口。

主要方法：
- `Initialize()`: 初始化通信
- `Connect()`: 连接设备
- `SetSlaveId(int)`: 设置从站地址
- `ReadHoldingRegisters(int, int)`: 读取保持寄存器
- `WriteSingleRegister(int, uint16_t)`: 写单个寄存器

#### ModbusTcpComm
TCP 通信实现类，支持网络通信和多连接管理。

主要方法：
- `AddConnection(string, int)`: 添加连接
- `RemoveConnection(string, int)`: 移除连接
- `SwitchConnection(string, int)`: 切换连接
- `GetActiveConnections()`: 获取活跃连接列表
- `SetMaxConnections(int)`: 设置最大连接数

#### ConfigManager
配置管理器，负责加载和解析配置文件。

主要方法：
- `LoadConfig(string)`: 加载配置文件
- `GetServiceParam()`: 获取服务参数
- `GetDeviceParam(int)`: 获取设备参数

#### PointTableLoader
点表加载器，负责加载和解析数据点配置。

主要方法：
- `LoadPointTable(string)`: 加载点表文件
- `FilterByDataType(DataType)`: 按数据类型过滤
- `ValidatePointTable()`: 验证点表

## 故障排除

### 常见问题

1. **编译错误: libmodbus not found**
   - 确保已安装 libmodbus 开发包
   - 检查 CMakeLists.txt 中的路径设置

2. **串口权限错误**
   ```bash
   sudo chmod 666 /dev/ttyS1
   # 或者将用户添加到 dialout 组
   sudo usermod -a -G dialout $USER
   ```

3. **Redis 连接失败**
   - 确保 Redis 服务正在运行
   - 检查防火墙设置
   - 验证 IP 地址和端口配置

### 调试技巧

1. **启用详细日志**
   ```cpp
   Logger::GetInstance().SetLevel(LogLevel::DEBUG);
   ```

2. **检查通信统计**
   ```cpp
   int success, error, timeout;
   comm.GetCommStats(success, error, timeout);
   ```

3. **使用模拟器测试**
   - 推荐使用 ModbusPal 或 QModMaster 进行测试

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目基于 LGPL-2.1+ 许可证发布，与 libmodbus 保持一致。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 技术讨论群

---

**注意**: 本项目基于现有 xj_svg 系统的业务逻辑模式构建，确保与现有系统的完全兼容性。
