#ifndef MODBUS_SERVICE_H
#define MODBUS_SERVICE_H

#include "../device/device_manager.h"
#include "../redis/redis_manager.h"
#include "../config/config_manager.h"
#include "../utils/thread_pool.h"
#include "../utils/logger.h"
#include <memory>
#include <atomic>
#include <string>

namespace modbus {

// 服务配置
struct ServiceConfig {
    // 基本配置
    std::string service_name = "ModbusProtocolService";
    std::string version = "1.0.0";
    std::string config_file = "modbus_config.json";
    
    // 日志配置
    LogLevel log_level = LogLevel::INFO;
    LogTarget log_target = LogTarget::FILE;
    std::string log_file = "modbus_service.log";
    int max_log_file_size_mb = 100;
    int max_log_files = 10;
    
    // Redis 配置
    bool enable_redis = true;
    RedisManagerConfig redis_config;
    
    // 设备配置
    std::string device_config_file = "devices.json";
    bool auto_start_devices = true;
    
    // 性能配置
    int thread_pool_size = 4;
    int max_concurrent_operations = 100;
    
    // 监控配置
    bool enable_monitoring = true;
    int monitoring_interval_ms = 5000;
    bool enable_health_check = true;
    int health_check_interval_ms = 30000;
    
    ServiceConfig() = default;
};

// 服务状态
enum class ServiceStatus {
    STOPPED = 0,
    STARTING = 1,
    RUNNING = 2,
    STOPPING = 3,
    ERROR = 4
};

// 服务统计信息
struct ServiceStatistics {
    ServiceStatus status = ServiceStatus::STOPPED;
    uint64_t start_time = 0;
    uint64_t uptime_seconds = 0;
    
    // 设备统计
    int total_devices = 0;
    int running_devices = 0;
    int connected_devices = 0;
    int error_devices = 0;
    
    // 数据统计
    int total_data_points = 0;
    int total_scan_count = 0;
    int total_success_count = 0;
    int total_error_count = 0;
    double overall_success_rate = 0.0;
    
    // Redis 统计
    bool redis_connected = false;
    int redis_publish_count = 0;
    int redis_subscribe_count = 0;
    int redis_error_count = 0;
    
    // 系统资源
    double cpu_usage = 0.0;
    double memory_usage_mb = 0.0;
    int thread_count = 0;
    
    uint64_t last_update_time = 0;
};

// Modbus 协议服务主类
class ModbusService {
public:
    explicit ModbusService(const ServiceConfig& config = ServiceConfig());
    virtual ~ModbusService();
    
    // 禁止拷贝和赋值
    ModbusService(const ModbusService&) = delete;
    ModbusService& operator=(const ModbusService&) = delete;
    
    // 服务控制
    Result<bool> Initialize();
    void Shutdown();
    Result<bool> Start();
    void Stop();
    bool IsRunning() const { return status_ == ServiceStatus::RUNNING; }
    
    // 配置管理
    void SetConfig(const ServiceConfig& config);
    ServiceConfig GetConfig() const { return config_; }
    Result<bool> LoadConfigFromFile(const std::string& config_file);
    Result<bool> SaveConfigToFile(const std::string& config_file) const;
    
    // 状态查询
    ServiceStatus GetStatus() const { return status_; }
    ServiceStatistics GetStatistics() const;
    std::string GetStatusString() const;
    
    // 组件访问
    DeviceManager& GetDeviceManager() { return DEVICE_MANAGER(); }
    RedisManager& GetRedisManager() { return REDIS_MANAGER(); }
    ConfigManager& GetConfigManager() { return CONFIG_MANAGER(); }
    
    // 设备管理
    Result<bool> AddDevice(const DeviceConfig& device_config);
    Result<bool> RemoveDevice(int device_id);
    Result<bool> StartDevice(int device_id);
    Result<bool> StopDevice(int device_id);
    std::vector<DeviceStatusInfo> GetAllDeviceStatus() const;
    
    // 数据操作
    Result<DataPointValue> ReadDataPoint(int device_id, const TypeIndex& type_idx);
    Result<bool> WriteDataPoint(int device_id, const TypeIndex& type_idx, double value);
    Result<std::vector<DataPointValue>> ReadAllDataPoints(int device_id);
    
    // Redis 操作
    Result<bool> PublishData(int device_id, const TypeIndex& type_idx, double value);
    Result<bool> PublishDeviceStatus(int device_id, DeviceStatus status, const std::string& message = "");
    
    // 监控和健康检查
    void EnableMonitoring(bool enable = true);
    bool IsMonitoringEnabled() const { return monitoring_enabled_; }
    void EnableHealthCheck(bool enable = true);
    bool IsHealthCheckEnabled() const { return health_check_enabled_; }
    
    // 事件回调
    using StatusChangeCallback = std::function<void(ServiceStatus old_status, ServiceStatus new_status)>;
    using DeviceEventCallback = std::function<void(int device_id, const std::string& event, const std::string& message)>;
    using DataEventCallback = std::function<void(int device_id, const DataPointValue& value)>;
    using ErrorCallback = std::function<void(const std::string& error_message)>;
    
    void SetStatusChangeCallback(StatusChangeCallback callback);
    void SetDeviceEventCallback(DeviceEventCallback callback);
    void SetDataEventCallback(DataEventCallback callback);
    void SetErrorCallback(ErrorCallback callback);
    
    // 服务信息
    std::string GetServiceName() const { return config_.service_name; }
    std::string GetVersion() const { return config_.version; }
    uint64_t GetUptime() const;
    
    // 优雅关闭
    void RequestShutdown();
    bool IsShutdownRequested() const { return shutdown_requested_; }
    
private:
    // 内部方法
    Result<bool> InitializeLogging();
    Result<bool> InitializeRedis();
    Result<bool> InitializeDevices();
    void SetupCallbacks();
    
    // 监控线程
    void StartMonitoring();
    void StopMonitoring();
    void MonitoringThread();
    void UpdateStatistics();
    
    // 健康检查
    void StartHealthCheck();
    void StopHealthCheck();
    void HealthCheckThread();
    bool PerformHealthCheck();
    
    // 状态管理
    void SetStatus(ServiceStatus new_status);
    void HandleError(const std::string& error_message);
    
    // 回调处理
    void HandleDeviceStatusChange(int device_id, DeviceStatus old_status, DeviceStatus new_status);
    void HandleDeviceDataChange(int device_id, const DataPointValue& value);
    void HandleDeviceAlarm(int device_id, const std::string& alarm_message);
    
private:
    ServiceConfig config_;
    std::atomic<ServiceStatus> status_{ServiceStatus::STOPPED};
    std::atomic<bool> shutdown_requested_{false};
    std::atomic<bool> monitoring_enabled_{true};
    std::atomic<bool> health_check_enabled_{true};
    
    // 统计信息
    mutable ServiceStatistics stats_;
    mutable std::mutex stats_mutex_;
    
    // 监控线程
    std::unique_ptr<std::thread> monitoring_thread_;
    std::unique_ptr<std::thread> health_check_thread_;
    std::atomic<bool> monitoring_running_{false};
    std::atomic<bool> health_check_running_{false};
    Event stop_event_;
    
    // 回调函数
    StatusChangeCallback status_callback_;
    DeviceEventCallback device_callback_;
    DataEventCallback data_callback_;
    ErrorCallback error_callback_;
    std::mutex callback_mutex_;
};

// 服务工厂
class ModbusServiceFactory {
public:
    static std::unique_ptr<ModbusService> CreateService(const ServiceConfig& config = ServiceConfig());
    static std::unique_ptr<ModbusService> CreateServiceFromConfig(const std::string& config_file);
    
    // 预定义配置
    static ServiceConfig GetDefaultConfig();
    static ServiceConfig GetProductionConfig();
    static ServiceConfig GetDevelopmentConfig();
    static ServiceConfig GetTestConfig();
};

// 全局服务实例
class GlobalModbusService {
public:
    static ModbusService& GetInstance();
    static bool Initialize(const ServiceConfig& config = ServiceConfig());
    static void Shutdown();
    
private:
    GlobalModbusService() = default;
    ~GlobalModbusService() = default;
    
    GlobalModbusService(const GlobalModbusService&) = delete;
    GlobalModbusService& operator=(const GlobalModbusService&) = delete;
    
private:
    static std::unique_ptr<ModbusService> instance_;
    static std::mutex instance_mutex_;
};

// 便捷宏定义
#define MODBUS_SERVICE() modbus::GlobalModbusService::GetInstance()

// 服务启动器 - 用于命令行启动服务
class ServiceLauncher {
public:
    static int Run(int argc, char* argv[]);
    static void PrintUsage();
    static void PrintVersion();
    static void InstallSignalHandlers();
    
private:
    static void SignalHandler(int signal);
    static std::atomic<bool> shutdown_signal_received_;
};

} // namespace modbus

#endif // MODBUS_SERVICE_H
