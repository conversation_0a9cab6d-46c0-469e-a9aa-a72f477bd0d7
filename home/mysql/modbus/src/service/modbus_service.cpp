#include "modbus_service.h"
#include "../utils/utils.h"
#include "../config/config_manager.h"
#include <csignal>
#include <iostream>

namespace modbus {

// 静态成员初始化
std::unique_ptr<ModbusService> GlobalModbusService::instance_;
std::mutex GlobalModbusService::instance_mutex_;
std::atomic<bool> ServiceLauncher::shutdown_signal_received_{false};

// ModbusService 实现
ModbusService::ModbusService(const ServiceConfig& config)
    : config_(config)
    , stop_event_(true) {  // manual reset event
}

ModbusService::~ModbusService() {
    Shutdown();
}

Result<bool> ModbusService::Initialize() {
    SetStatus(ServiceStatus::STARTING);
    
    // 初始化日志系统
    auto log_result = InitializeLogging();
    if (!log_result.IsSuccess()) {
        HandleError("Failed to initialize logging: " + log_result.error_message);
        return log_result;
    }
    
    WRITE_INFO_LOG("正在初始化 Modbus 协议服务 %s", config_.version.c_str());
    
    // 初始化 Redis
    if (config_.enable_redis) {
        auto redis_result = InitializeRedis();
        if (!redis_result.IsSuccess()) {
            WRITE_WARN_LOG("Redis 初始化失败: %s", redis_result.error_message.c_str());
        }
    }
    
    // 初始化设备管理器
    auto device_result = InitializeDevices();
    if (!device_result.IsSuccess()) {
        HandleError("设备初始化失败: " + device_result.error_message);
        return device_result;
    }
    
    // 设置回调
    SetupCallbacks();
    
    // 更新统计信息
    {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.start_time = utils::TimeUtils::GetCurrentTimestamp();
    }
    
    SetStatus(ServiceStatus::STOPPED);
    
    WRITE_INFO_LOG("Modbus 协议服务初始化成功");
    return Result<bool>(true);
}

void ModbusService::Shutdown() {
    WRITE_INFO_LOG("正在关闭 Modbus 协议服务");
    
    SetStatus(ServiceStatus::STOPPING);
    
    // 停止监控
    StopMonitoring();
    StopHealthCheck();
    
    // 停止设备管理器
    DEVICE_MANAGER().Shutdown();
    
    // 停止 Redis 管理器
    if (config_.enable_redis) {
        REDIS_MANAGER().Shutdown();
    }
    
    SetStatus(ServiceStatus::STOPPED);
    
    WRITE_INFO_LOG("Modbus 协议服务关闭完成");
    
    // 关闭日志系统
    Logger::GetInstance().Shutdown();
}

Result<bool> ModbusService::Start() {
    if (status_ == ServiceStatus::RUNNING) {
        return Result<bool>(true);
    }
    
    WRITE_INFO_LOG("正在启动 Modbus 协议服务");
    
    SetStatus(ServiceStatus::STARTING);
    
    // 启动 Redis 管理器
    if (config_.enable_redis) {
        auto redis_result = REDIS_MANAGER().Start();
        if (!redis_result.IsSuccess()) {
            WRITE_WARN_LOG("Redis 管理器启动失败: %s", redis_result.error_message.c_str());
        }
    }
    
    // 启动设备管理器
    auto device_result = DEVICE_MANAGER().Start();
    if (!device_result.IsSuccess()) {
        HandleError("设备管理器启动失败: " + device_result.error_message);
        return device_result;
    }
    
    // 自动启动设备
    if (config_.auto_start_devices) {
        auto start_result = DEVICE_MANAGER().StartAllDevices();
        if (!start_result.IsSuccess()) {
            WRITE_WARN_LOG("部分设备启动失败: %s", start_result.error_message.c_str());
        }
    }
    
    // 启动监控
    if (monitoring_enabled_) {
        StartMonitoring();
    }
    
    // 启动健康检查
    if (health_check_enabled_) {
        StartHealthCheck();
    }
    
    SetStatus(ServiceStatus::RUNNING);
    
    WRITE_INFO_LOG("Modbus 协议服务启动成功");
    return Result<bool>(true);
}

void ModbusService::Stop() {
    if (status_ == ServiceStatus::STOPPED) {
        return;
    }
    
    WRITE_INFO_LOG("正在停止 Modbus 协议服务");
    
    SetStatus(ServiceStatus::STOPPING);
    
    // 停止监控
    StopMonitoring();
    StopHealthCheck();
    
    // 停止所有设备
    DEVICE_MANAGER().StopAllDevices();
    
    // 停止设备管理器
    DEVICE_MANAGER().Stop();
    
    // 停止 Redis 管理器
    if (config_.enable_redis) {
        REDIS_MANAGER().Stop();
    }
    
    SetStatus(ServiceStatus::STOPPED);
    
    WRITE_INFO_LOG("Modbus Protocol Service stopped");
}

void ModbusService::SetConfig(const ServiceConfig& config) {
    bool was_running = IsRunning();
    
    if (was_running) {
        Stop();
    }
    
    config_ = config;
    
    if (was_running) {
        Initialize();
        Start();
    }
}

Result<bool> ModbusService::LoadConfigFromFile(const std::string& config_file) {
    WRITE_INFO_LOG("Loading configuration from file: %s", config_file.c_str());

    // 使用配置管理器加载配置文件
    auto& config_manager = CONFIG_MANAGER();
    auto load_result = config_manager.LoadConfig(config_file);

    if (!load_result.IsSuccess()) {
        WRITE_ERROR_LOG("Failed to load config file: %s", load_result.error_message.c_str());
        return load_result;
    }

    // 获取服务参数并更新配置
    auto service_result = config_manager.GetServiceParam();
    if (service_result.IsSuccess()) {
        const auto& service_param = service_result.data;

        // 更新 Redis 配置
        if (config_.enable_redis) {
            config_.redis_config.connection_param.host = service_param.redis_ip;
            config_.redis_config.connection_param.port = service_param.redis_port;
            config_.redis_config.connection_param.password = service_param.redis_password;
            config_.redis_config.connection_param.database = service_param.redis_database;
            config_.redis_config.connection_param.timeout_ms = 3000;
        }

        // 更新日志配置
        config_.log_file = service_param.log_file;

        // 更新其他服务参数
        config_.service_name = service_param.service_name;

        WRITE_INFO_LOG("Configuration loaded successfully from: %s", config_file.c_str());
    }

    return Result<bool>(true);
}

Result<bool> ModbusService::SaveConfigToFile(const std::string& config_file) const {
    // 简化实现，实际应该序列化配置到 JSON 文件
    WRITE_INFO_LOG("Saving configuration to file: %s", config_file.c_str());
    
    // 这里应该实现配置文件保存
    // 暂时返回成功
    return Result<bool>(true);
}

ServiceStatistics ModbusService::GetStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    ServiceStatistics stats = stats_;
    stats.status = status_;
    stats.last_update_time = utils::TimeUtils::GetCurrentTimestamp();
    
    if (stats.start_time > 0) {
        stats.uptime_seconds = (stats.last_update_time - stats.start_time) / 1000;
    }
    
    // 获取设备管理器统计
    auto device_stats = DEVICE_MANAGER().GetStatistics();
    stats.total_devices = device_stats.total_devices;
    stats.running_devices = device_stats.running_devices;
    stats.connected_devices = device_stats.connected_devices;
    stats.error_devices = device_stats.error_devices;
    stats.total_data_points = device_stats.total_data_points;
    stats.total_scan_count = device_stats.total_scan_count;
    stats.total_success_count = device_stats.total_success_count;
    stats.total_error_count = device_stats.total_error_count;
    stats.overall_success_rate = device_stats.overall_success_rate;
    
    // 获取 Redis 统计
    if (config_.enable_redis) {
        auto redis_stats = REDIS_MANAGER().GetStatistics();
        stats.redis_connected = redis_stats.publisher_connected && redis_stats.subscriber_connected;
        stats.redis_publish_count = redis_stats.publish_success_count;
        stats.redis_subscribe_count = redis_stats.message_count;
        stats.redis_error_count = redis_stats.publish_error_count + redis_stats.subscribe_error_count;
    }
    
    return stats;
}

std::string ModbusService::GetStatusString() const {
    switch (status_) {
        case ServiceStatus::STOPPED: return "STOPPED";
        case ServiceStatus::STARTING: return "STARTING";
        case ServiceStatus::RUNNING: return "RUNNING";
        case ServiceStatus::STOPPING: return "STOPPING";
        case ServiceStatus::ERROR: return "ERROR";
        default: return "UNKNOWN";
    }
}

Result<bool> ModbusService::AddDevice(const DeviceConfig& device_config) {
    return DEVICE_MANAGER().AddDevice(device_config);
}

Result<bool> ModbusService::RemoveDevice(int device_id) {
    return DEVICE_MANAGER().RemoveDevice(device_id);
}

Result<bool> ModbusService::StartDevice(int device_id) {
    auto device_result = DEVICE_MANAGER().GetDevice(device_id);
    if (!device_result.IsSuccess()) {
        return Result<bool>(device_result.error_code, device_result.error_message);
    }
    
    return device_result.data->Start();
}

Result<bool> ModbusService::StopDevice(int device_id) {
    auto device_result = DEVICE_MANAGER().GetDevice(device_id);
    if (!device_result.IsSuccess()) {
        return Result<bool>(device_result.error_code, device_result.error_message);
    }
    
    device_result.data->Stop();
    return Result<bool>(true);
}

std::vector<DeviceStatusInfo> ModbusService::GetAllDeviceStatus() const {
    return DEVICE_MANAGER().GetAllDeviceStatus();
}

Result<DataPointValue> ModbusService::ReadDataPoint(int device_id, const TypeIndex& type_idx) {
    auto device_result = DEVICE_MANAGER().GetDevice(device_id);
    if (!device_result.IsSuccess()) {
        return Result<DataPointValue>(device_result.error_code, device_result.error_message);
    }
    
    return device_result.data->ReadDataPoint(type_idx);
}

Result<bool> ModbusService::WriteDataPoint(int device_id, const TypeIndex& type_idx, double value) {
    auto device_result = DEVICE_MANAGER().GetDevice(device_id);
    if (!device_result.IsSuccess()) {
        return Result<bool>(device_result.error_code, device_result.error_message);
    }
    
    return device_result.data->WriteDataPoint(type_idx, value);
}

Result<std::vector<DataPointValue>> ModbusService::ReadAllDataPoints(int device_id) {
    auto device_result = DEVICE_MANAGER().GetDevice(device_id);
    if (!device_result.IsSuccess()) {
        return Result<std::vector<DataPointValue>>(device_result.error_code, device_result.error_message);
    }
    
    return device_result.data->ReadAllDataPoints();
}

Result<bool> ModbusService::PublishData(int device_id, const TypeIndex& type_idx, double value) {
    if (!config_.enable_redis) {
        return Result<bool>(ErrorCode::NOT_SUPPORTED, "Redis not enabled");
    }
    
    auto result = REDIS_MANAGER().PublishData(device_id, type_idx, value);
    return Result<bool>(result.IsSuccess());
}

Result<bool> ModbusService::PublishDeviceStatus(int device_id, DeviceStatus status, const std::string& message) {
    if (!config_.enable_redis) {
        return Result<bool>(ErrorCode::NOT_SUPPORTED, "Redis not enabled");
    }
    
    auto result = REDIS_MANAGER().PublishDeviceStatus(device_id, status, message);
    return Result<bool>(result.IsSuccess());
}

void ModbusService::EnableMonitoring(bool enable) {
    if (monitoring_enabled_ == enable) {
        return;
    }
    
    monitoring_enabled_ = enable;
    
    if (enable && IsRunning()) {
        StartMonitoring();
    } else if (!enable) {
        StopMonitoring();
    }
}

void ModbusService::EnableHealthCheck(bool enable) {
    if (health_check_enabled_ == enable) {
        return;
    }
    
    health_check_enabled_ = enable;
    
    if (enable && IsRunning()) {
        StartHealthCheck();
    } else if (!enable) {
        StopHealthCheck();
    }
}

void ModbusService::SetStatusChangeCallback(StatusChangeCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    status_callback_ = callback;
}

void ModbusService::SetDeviceEventCallback(DeviceEventCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    device_callback_ = callback;
}

void ModbusService::SetDataEventCallback(DataEventCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    data_callback_ = callback;
}

void ModbusService::SetErrorCallback(ErrorCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    error_callback_ = callback;
}

uint64_t ModbusService::GetUptime() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    if (stats_.start_time > 0) {
        return (utils::TimeUtils::GetCurrentTimestamp() - stats_.start_time) / 1000;
    }
    
    return 0;
}

void ModbusService::RequestShutdown() {
    shutdown_requested_ = true;
    stop_event_.Set();
}

// 内部方法实现
Result<bool> ModbusService::InitializeLogging() {
    auto& logger = Logger::GetInstance();

    logger.Initialize(config_.log_level, config_.log_target, config_.log_file);

    return Result<bool>(true);
}

Result<bool> ModbusService::InitializeRedis() {
    auto& redis_manager = REDIS_MANAGER();
    redis_manager.SetConfig(config_.redis_config);

    return redis_manager.Initialize();
}

Result<bool> ModbusService::InitializeDevices() {
    auto& device_manager = DEVICE_MANAGER();

    auto init_result = device_manager.Initialize();
    if (!init_result.IsSuccess()) {
        return init_result;
    }

    // 从配置管理器加载设备
    auto& config_manager = CONFIG_MANAGER();
    auto devices_result = config_manager.GetAllDeviceParams();

    if (devices_result.IsSuccess()) {
        const auto& devices = devices_result.data;
        for (const auto& device_pair : devices) {
            const auto& device_param = device_pair.second;

            // 创建设备配置
            DeviceConfig device_config(device_param.device_id, device_param.device_name);
            device_config.device_type = "Generic";  // DeviceParam 中没有 device_type 字段
            device_config.comm_type = device_param.comm_type;
            device_config.slave_id = device_param.slave_id;
            device_config.enable_auto_scan = device_param.enable_auto_scan;
            device_config.scan_interval_ms = device_param.scan_interval_ms;
            device_config.enable_change_detection = device_param.enable_change_detection;
            device_config.change_threshold = device_param.change_threshold;
            device_config.embedded_point_config = device_param.point_config;

            // 设置通信参数 (简化为字符串格式)
            if (device_param.comm_type == CommType::RTU) {
                device_config.comm_config = device_param.rtu_param.device + "," +
                                           std::to_string(device_param.rtu_param.baud) + "," +
                                           std::string(1, device_param.rtu_param.parity);
            } else {
                device_config.comm_config = device_param.tcp_param.ip + ":" +
                                           std::to_string(device_param.tcp_param.port);
            }

            // 添加设备到设备管理器
            auto add_result = device_manager.AddDevice(device_config);
            if (!add_result.IsSuccess()) {
                WRITE_WARN_LOG("从配置文件加载设备失败: %s: %s",
                    device_param.device_name.c_str(), add_result.error_message.c_str());
            } else {
                WRITE_INFO_LOG("设备加载成功: %s (ID: %d)",
                    device_param.device_name.c_str(), device_param.device_id);
            }
        }
    } else {
        WRITE_WARN_LOG("获取设备配置失败: %s", devices_result.error_message.c_str());
    }

    return Result<bool>(true);
}

void ModbusService::SetupCallbacks() {
    // 设置设备管理器回调
    DEVICE_MANAGER().SetGlobalStatusChangeCallback([this](int device_id, DeviceStatus old_status, DeviceStatus new_status) {
        HandleDeviceStatusChange(device_id, old_status, new_status);
    });

    DEVICE_MANAGER().SetGlobalDataChangeCallback([this](int device_id, const DataPointValue& value) {
        HandleDeviceDataChange(device_id, value);
    });

    DEVICE_MANAGER().SetGlobalAlarmCallback([this](int device_id, const std::string& alarm_message) {
        HandleDeviceAlarm(device_id, alarm_message);
    });

    // 设置 Redis 管理器回调
    if (config_.enable_redis) {
        REDIS_MANAGER().SetStatusCallback([this](const std::string& component, bool connected, const std::string& error) {
            if (!connected && !error.empty()) {
                HandleError("Redis " + component + " error: " + error);
            }
        });
    }
}

// 监控线程
void ModbusService::StartMonitoring() {
    if (monitoring_running_) {
        return;
    }

    monitoring_running_ = true;
    monitoring_thread_ = std::make_unique<std::thread>(&ModbusService::MonitoringThread, this);
}

void ModbusService::StopMonitoring() {
    if (monitoring_running_) {
        monitoring_running_ = false;
        stop_event_.Set();

        if (monitoring_thread_ && monitoring_thread_->joinable()) {
            monitoring_thread_->join();
        }
        monitoring_thread_.reset();
    }
}

void ModbusService::MonitoringThread() {
    while (monitoring_running_) {
        if (stop_event_.WaitFor(config_.monitoring_interval_ms)) {
            break;  // 收到停止信号
        }

        UpdateStatistics();
    }
}

void ModbusService::UpdateStatistics() {
    // 统计信息在 GetStatistics() 中实时计算
    // 这里可以添加额外的监控逻辑

    auto stats = GetStatistics();

    // 检查错误率
    if (stats.total_scan_count > 100 && stats.overall_success_rate < 50.0) {
        HandleError("检测到低成功率: " + std::to_string(stats.overall_success_rate) + "%");
    }

    // 检查设备状态
    if (stats.error_devices > stats.total_devices / 2) {
        HandleError("过多设备处于错误状态: " + std::to_string(stats.error_devices) + "/" + std::to_string(stats.total_devices));
    }
}

// 健康检查
void ModbusService::StartHealthCheck() {
    if (health_check_running_) {
        return;
    }

    health_check_running_ = true;
    health_check_thread_ = std::make_unique<std::thread>(&ModbusService::HealthCheckThread, this);
}

void ModbusService::StopHealthCheck() {
    if (health_check_running_) {
        health_check_running_ = false;
        stop_event_.Set();

        if (health_check_thread_ && health_check_thread_->joinable()) {
            health_check_thread_->join();
        }
        health_check_thread_.reset();
    }
}

void ModbusService::HealthCheckThread() {
    while (health_check_running_) {
        if (stop_event_.WaitFor(config_.health_check_interval_ms)) {
            break;  // 收到停止信号
        }

        if (!PerformHealthCheck()) {
            HandleError("健康检查失败");
        }
    }
}

bool ModbusService::PerformHealthCheck() {
    // 检查服务状态
    if (status_ != ServiceStatus::RUNNING) {
        return false;
    }

    // 检查设备管理器
    if (!DEVICE_MANAGER().IsRunning()) {
        return false;
    }

    // 检查 Redis 连接
    if (config_.enable_redis) {
        auto redis_stats = REDIS_MANAGER().GetStatistics();
        if (!redis_stats.publisher_connected && !redis_stats.subscriber_connected) {
            return false;
        }
    }

    // 检查是否有设备在运行
    auto device_stats = DEVICE_MANAGER().GetStatistics();
    if (device_stats.total_devices > 0 && device_stats.running_devices == 0) {
        return false;
    }

    return true;
}

// 状态管理
void ModbusService::SetStatus(ServiceStatus new_status) {
    ServiceStatus old_status = status_;
    status_ = new_status;

    if (old_status != new_status && status_callback_) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        try {
            status_callback_(old_status, new_status);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("Status callback exception: %s", e.what());
        }
    }
}

void ModbusService::HandleError(const std::string& error_message) {
    WRITE_ERROR_LOG("服务错误: %s", error_message.c_str());

    if (status_ != ServiceStatus::ERROR) {
        SetStatus(ServiceStatus::ERROR);
    }

    if (error_callback_) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        try {
            error_callback_(error_message);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("错误回调函数异常: %s", e.what());
        }
    }
}

// 回调处理
void ModbusService::HandleDeviceStatusChange(int device_id, DeviceStatus old_status, DeviceStatus new_status) {
    std::string event = "STATUS_CHANGE";
    std::string message = utils::StringUtils::Format("Device %d status changed from %d to %d",
                                                    device_id, static_cast<int>(old_status), static_cast<int>(new_status));

    if (device_callback_) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        try {
            device_callback_(device_id, event, message);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("设备回调函数异常: %s", e.what());
        }
    }
}

void ModbusService::HandleDeviceDataChange(int device_id, const DataPointValue& value) {
    if (data_callback_) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        try {
            data_callback_(device_id, value);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("Data callback exception: %s", e.what());
        }
    }
}

void ModbusService::HandleDeviceAlarm(int device_id, const std::string& alarm_message) {
    std::string event = "ALARM";

    if (device_callback_) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        try {
            device_callback_(device_id, event, alarm_message);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("Device alarm callback exception: %s", e.what());
        }
    }
}

// ModbusServiceFactory 实现
std::unique_ptr<ModbusService> ModbusServiceFactory::CreateService(const ServiceConfig& config) {
    return std::make_unique<ModbusService>(config);
}

std::unique_ptr<ModbusService> ModbusServiceFactory::CreateServiceFromConfig(const std::string& config_file) {
    ServiceConfig config;
    config.config_file = config_file;

    auto service = std::make_unique<ModbusService>(config);

    // 加载配置文件
    auto load_result = service->LoadConfigFromFile(config_file);
    if (!load_result.IsSuccess()) {
        WRITE_WARN_LOG("Failed to load config from file: %s", load_result.error_message.c_str());
    }

    return service;
}

ServiceConfig ModbusServiceFactory::GetDefaultConfig() {
    ServiceConfig config;
    config.service_name = "ModbusProtocolService";
    config.version = "1.0.0";
    config.log_level = LogLevel::INFO;
    config.log_target = LogTarget::CONSOLE;
    config.enable_redis = false;
    config.auto_start_devices = true;
    config.enable_monitoring = true;
    config.enable_health_check = true;

    return config;
}

ServiceConfig ModbusServiceFactory::GetProductionConfig() {
    ServiceConfig config = GetDefaultConfig();
    config.log_level = LogLevel::WARN;
    config.log_target = LogTarget::FILE;
    config.log_file = "/var/log/modbus_service.log";
    config.enable_redis = true;
    config.redis_config.connection_param.host = "127.0.0.1";
    config.redis_config.connection_param.port = 6379;

    return config;
}

ServiceConfig ModbusServiceFactory::GetDevelopmentConfig() {
    ServiceConfig config = GetDefaultConfig();
    config.log_level = LogLevel::DEBUG;
    config.log_target = LogTarget::CONSOLE;
    config.enable_redis = true;
    config.redis_config.connection_param.host = "127.0.0.1";
    config.redis_config.connection_param.port = 6379;

    return config;
}

ServiceConfig ModbusServiceFactory::GetTestConfig() {
    ServiceConfig config = GetDefaultConfig();
    config.log_level = LogLevel::DEBUG;
    config.log_target = LogTarget::CONSOLE;
    config.enable_redis = false;
    config.auto_start_devices = false;
    config.enable_monitoring = false;
    config.enable_health_check = false;

    return config;
}

// GlobalModbusService 实现
ModbusService& GlobalModbusService::GetInstance() {
    std::lock_guard<std::mutex> lock(instance_mutex_);

    if (!instance_) {
        instance_ = std::make_unique<ModbusService>();
    }

    return *instance_;
}

bool GlobalModbusService::Initialize(const ServiceConfig& config) {
    std::lock_guard<std::mutex> lock(instance_mutex_);

    if (!instance_) {
        instance_ = std::make_unique<ModbusService>(config);
    } else {
        instance_->SetConfig(config);
    }

    return instance_->Initialize().IsSuccess();
}

void GlobalModbusService::Shutdown() {
    std::lock_guard<std::mutex> lock(instance_mutex_);

    if (instance_) {
        instance_->Shutdown();
        instance_.reset();
    }
}

// ServiceLauncher 实现
int ServiceLauncher::Run(int argc, char* argv[]) {
    // 解析命令行参数
    std::string config_file = "modbus_config.xml";
    bool daemon_mode = false;

    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];

        if (arg == "--help" || arg == "-h") {
            PrintUsage();
            return 0;
        } else if (arg == "--version" || arg == "-v") {
            PrintVersion();
            return 0;
        } else if (arg == "--config" || arg == "-c") {
            if (i + 1 < argc) {
                config_file = argv[++i];
            } else {
                std::cerr << "Error: --config requires a filename" << std::endl;
                return 1;
            }
        } else if (arg == "--daemon" || arg == "-d") {
            daemon_mode = true;
        }
    }

    // 安装信号处理器
    InstallSignalHandlers();

    // 创建服务
    auto service = ModbusServiceFactory::CreateServiceFromConfig(config_file);

    // 初始化服务
    auto init_result = service->Initialize();
    if (!init_result.IsSuccess()) {
        std::cerr << "Failed to initialize service: " << init_result.error_message << std::endl;
        return 1;
    }

    // 启动服务
    auto start_result = service->Start();
    if (!start_result.IsSuccess()) {
        std::cerr << "Failed to start service: " << start_result.error_message << std::endl;
        return 1;
    }

    std::cout << "Modbus Protocol Service started successfully" << std::endl;
    std::cout << "Press Ctrl+C to stop the service" << std::endl;

    // 主循环
    while (!shutdown_signal_received_ && !service->IsShutdownRequested()) {
        utils::TimeUtils::SleepMs(1000);

        // 检查服务状态
        if (service->GetStatus() == ServiceStatus::ERROR) {
            std::cerr << "Service entered error state" << std::endl;
            break;
        }
    }

    std::cout << "Shutting down service..." << std::endl;
    service->Shutdown();

    std::cout << "Service shutdown completed" << std::endl;
    return 0;
}

void ServiceLauncher::PrintUsage() {
    std::cout << "Usage: modbus_service [OPTIONS]" << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  -h, --help           Show this help message" << std::endl;
    std::cout << "  -v, --version        Show version information" << std::endl;
    std::cout << "  -c, --config FILE    Configuration file path" << std::endl;
    std::cout << "  -d, --daemon         Run as daemon" << std::endl;
}

void ServiceLauncher::PrintVersion() {
    std::cout << "Modbus Protocol Service v1.0.0" << std::endl;
    std::cout << "Built on " << __DATE__ << " " << __TIME__ << std::endl;
}

void ServiceLauncher::InstallSignalHandlers() {
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
#ifndef _WIN32
    signal(SIGHUP, SignalHandler);
#endif
}

void ServiceLauncher::SignalHandler(int signal) {
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            shutdown_signal_received_ = true;
            break;
#ifndef _WIN32
        case SIGHUP:
            // 重新加载配置
            break;
#endif
        default:
            break;
    }
}

} // namespace modbus
