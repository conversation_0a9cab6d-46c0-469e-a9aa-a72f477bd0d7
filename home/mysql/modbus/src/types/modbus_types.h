#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>

namespace modbus {

// 错误码定义
enum class ErrorCode {
    SUCCESS = 0,
    INVALID_PARAM = 1,
    COMM_ERROR = 2,
    TIMEOUT = 3,
    DEVICE_NOT_FOUND = 4,
    CONFIG_ERROR = 5,
    NOT_INITIALIZED = 6,
    ALREADY_RUNNING = 7,
    QUEUE_FULL = 8,
    PARSE_ERROR = 9,
    UNKNOWN_ERROR = 99
};

// 结果模板类
template<typename T>
class Result {
public:
    T data;
    ErrorCode error_code;
    std::string error_message;
    
    Result() : error_code(ErrorCode::SUCCESS) {}
    Result(const T& value) : data(value), error_code(ErrorCode::SUCCESS) {}
    Result(ErrorCode code, const std::string& message) 
        : error_code(code), error_message(message) {}
    
    bool IsSuccess() const { return error_code == ErrorCode::SUCCESS; }
    bool HasError() const { return error_code != ErrorCode::SUCCESS; }
};

// 数据类型枚举
enum class DataType {
    YC = 0,  // 遥测 (模拟量)
    YX = 1,  // 遥信 (开关量)
    YK = 2,  // 遥控
    YT = 3   // 遥调
};

// 字节序枚举
enum class ByteOrder {
    BIG_ENDIAN = 0,     // 大端序
    LITTLE_ENDIAN = 1,  // 小端序
    BIG_ENDIAN_SWAP = 2,    // 大端序字节交换
    LITTLE_ENDIAN_SWAP = 3  // 小端序字节交换
};

// 通信类型
enum class CommType {
    RTU = 0,
    TCP = 1
};

// 设备状态
enum class DeviceStatus {
    DISCONNECTED = 0,
    CONNECTING = 1,
    CONNECTED = 2,
    INITIALIZED = 3,
    RUNNING = 4,
    ERROR = 5,
    STOPPING = 6
};

// 数据质量
enum class DataQuality {
    GOOD = 0,
    BAD = 1,
    UNCERTAIN = 2
};

// 类型索引
struct TypeIndex {
    DataType data_type;
    uint16_t point_id;
    
    TypeIndex() : data_type(DataType::YC), point_id(0) {}
    TypeIndex(DataType type, uint16_t id) : data_type(type), point_id(id) {}
    
    bool operator<(const TypeIndex& other) const {
        if (data_type != other.data_type) {
            return static_cast<int>(data_type) < static_cast<int>(other.data_type);
        }
        return point_id < other.point_id;
    }
    
    bool operator==(const TypeIndex& other) const {
        return data_type == other.data_type && point_id == other.point_id;
    }
    
    bool operator!=(const TypeIndex& other) const {
        return !(*this == other);
    }
};

// 数据点定义
struct DataPoint {
    TypeIndex type_idx;
    uint16_t address;           // Modbus地址
    uint8_t function_code;      // 功能码
    uint8_t data_type;          // 数据类型
    double scale_factor;        // 缩放因子
    double offset;              // 偏移量
    std::string description;    // 描述
    
    DataPoint() : address(0), function_code(3), data_type(0), 
                  scale_factor(1.0), offset(0.0) {}
};

// 数据点值
struct DataPointValue {
    TypeIndex type_idx;
    uint16_t raw_value;         // 原始值
    double scaled_value;        // 缩放后的值
    bool is_valid;              // 数据有效性
    DataQuality quality;        // 数据质量
    uint64_t timestamp;         // 时间戳
    
    DataPointValue() : raw_value(0), scaled_value(0.0), 
                       is_valid(false), quality(DataQuality::BAD), 
                       timestamp(0) {}
};

// RTU通信参数
struct RtuCommParam {
    std::string device;         // 设备路径
    int baud_rate;              // 波特率
    char parity;                // 校验位
    int data_bits;              // 数据位
    int stop_bits;              // 停止位
    int timeout_ms;             // 超时时间
    
    RtuCommParam() : device("/dev/ttyS0"), baud_rate(9600), 
                     parity('N'), data_bits(8), stop_bits(1), 
                     timeout_ms(1000) {}
};

// TCP通信参数
struct TcpCommParam {
    std::string ip;             // IP地址
    int port;                   // 端口
    int timeout_ms;             // 超时时间
    
    TcpCommParam() : ip("127.0.0.1"), port(502), timeout_ms(1000) {}
};

// Redis连接参数
struct RedisConnectionParam {
    std::string host;
    int port;
    std::string password;
    int database;
    int timeout_ms;
    
    RedisConnectionParam() : host("127.0.0.1"), port(6379), 
                            database(0), timeout_ms(3000) {}
};

// Redis管理器配置
struct RedisManagerConfig {
    RedisConnectionParam connection_param;
    bool enable_publisher;
    bool enable_subscriber;
    bool enable_auto_reconnect;
    int max_publish_queue_size;
    int reconnect_interval_ms;
    
    RedisManagerConfig() : enable_publisher(true), enable_subscriber(false),
                          enable_auto_reconnect(true), max_publish_queue_size(1000),
                          reconnect_interval_ms(5000) {}
};

// 设备配置信息
struct DeviceConfig {
    int device_id;
    std::string device_name;
    std::string device_type;

    // 通信配置
    CommType comm_type = CommType::RTU;
    std::string comm_config;  // JSON 格式的通信配置
    int slave_id = 1;         // Modbus 从站地址

    // 数据点配置
    std::string point_table_file;  // 点表文件路径
    int scan_interval_ms = 1000;
    bool enable_auto_scan = true;

    // 变化检测配置
    bool enable_change_detection = false;  // 默认不启用变化检测
    double change_threshold = 0.01;        // 变化阈值

    // Redis 配置
    bool enable_redis = true;
    int redis_report_interval_ms = 200;

    // 其他配置
    bool enable_logging = true;
    std::string log_level = "INFO";
    
    DeviceConfig() : device_id(0) {}
    DeviceConfig(int id, const std::string& name) 
        : device_id(id), device_name(name) {}
};

// 设备点表配置
struct DevicePointConfig {
    std::vector<DataPoint> yc_points;  // 遥测点
    std::vector<DataPoint> yx_points;  // 遥信点
    std::vector<DataPoint> yk_points;  // 遥控点
    std::vector<DataPoint> yt_points;  // 遥调点
};

// 服务参数
struct ServiceParam {
    std::string service_name;
    std::string redis_ip;
    int redis_port;
    int report_interval_ms;
    std::map<int, DeviceConfig> devices;
    
    ServiceParam() : service_name("ModbusService"), redis_ip("127.0.0.1"),
                    redis_port(6379), report_interval_ms(200) {}
};

// 设备参数
struct DeviceParam {
    int device_id;
    std::string device_name;
    std::string device_type;
    CommType comm_type;
    RtuCommParam rtu_param;
    TcpCommParam tcp_param;
    int scan_interval_ms;
    bool enable_auto_scan;
    DevicePointConfig point_config;
    
    DeviceParam() : device_id(0), comm_type(CommType::RTU),
                   scan_interval_ms(1000), enable_auto_scan(true) {}
};

// 回调函数类型定义
using DataChangeCallback = std::function<void(const DataPointValue&)>;
using AlarmCallback = std::function<void(const DataPointValue&, const std::string&)>;
using StatusChangeCallback = std::function<void(DeviceStatus, const std::string&)>;

} // namespace modbus
