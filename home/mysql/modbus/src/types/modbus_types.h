#ifndef MODBUS_TYPES_H
#define MODBUS_TYPES_H

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <cstdint>

namespace modbus {

// Modbus 功能码定义
enum class FunctionCode : uint8_t {
    READ_COILS = 0x01,
    READ_DISCRETE_INPUTS = 0x02,
    READ_HOLDING_REGISTERS = 0x03,
    READ_INPUT_REGISTERS = 0x04,
    WRITE_SINGLE_COIL = 0x05,
    WRITE_SINGLE_REGISTER = 0x06,
    WRITE_MULTIPLE_COILS = 0x0F,
    WRITE_MULTIPLE_REGISTERS = 0x10
};

// 数据类型定义（兼容现有系统）
enum class DataType : int {
    YC = 1,  // 遥测 - 模拟量
    YX = 2,  // 遥信 - 开关量
    YT = 3,  // 遥调 - 模拟量设定
    YK = 4   // 遥控 - 开关量控制
};

// 通信类型
enum class CommType : int {
    RTU = 0,
    TCP = 1
};

// 设备状态
enum class DeviceStatus {
    DISCONNECTED = 0,
    CONNECTING = 1,
    CONNECTED = 2,
    ERROR = 3,
    INITIALIZED = 4
};

// 数据质量标识
enum class DataQuality : uint8_t {
    GOOD = 0x00,
    BAD = 0x01,
    UNCERTAIN = 0x02,
    TIMEOUT = 0x03,
    COMM_ERROR = 0x04
};

// RTU 通信参数
struct RtuCommParam {
    std::string device;      // 串口设备路径
    int baud;               // 波特率
    char parity;            // 校验位 ('N', 'E', 'O')
    int data_bit;           // 数据位
    int stop_bit;           // 停止位
    int mode;               // 模式
    int timeout_ms;         // 超时时间(毫秒)
    
    RtuCommParam() 
        : device("/dev/ttyS1")
        , baud(9600)
        , parity('N')
        , data_bit(8)
        , stop_bit(1)
        , mode(1)
        , timeout_ms(1000) {}
};

// TCP 通信参数
struct TcpCommParam {
    std::string ip;         // IP地址
    int port;               // 端口号
    int timeout_ms;         // 超时时间(毫秒)
    
    TcpCommParam()
        : ip("127.0.0.1")
        , port(502)
        , timeout_ms(1000) {}
};

// 数据点类型索引
struct TypeIndex {
    DataType data_type;     // 数据类型
    int point_id;           // 点号

    TypeIndex() : data_type(DataType::YC), point_id(0) {}
    TypeIndex(DataType type, int id) : data_type(type), point_id(id) {}

    // 比较操作符
    bool operator<(const TypeIndex& other) const {
        if (data_type != other.data_type) {
            return static_cast<int>(data_type) < static_cast<int>(other.data_type);
        }
        return point_id < other.point_id;
    }

    bool operator==(const TypeIndex& other) const {
        return data_type == other.data_type && point_id == other.point_id;
    }

    bool operator!=(const TypeIndex& other) const {
        return !(*this == other);
    }
};

// 数据点参数
struct DataPoint {
    TypeIndex type_idx;     // 类型索引
    FunctionCode func_code; // 功能码
    int slave_id;           // 从站地址
    int start_addr;         // 起始地址
    int count;              // 数量
    int data_type;          // 数据类型 (0-11)
    int param;              // 参数 (字节序或BIT位配置)
    double scale;           // 比例因子
    double offset;          // 偏移量
    std::string description; // 描述

    DataPoint()
        : func_code(FunctionCode::READ_HOLDING_REGISTERS)
        , slave_id(1)
        , start_addr(0)
        , count(1)
        , data_type(0)
        , param(0)
        , scale(1.0)
        , offset(0.0) {}
};

// 新的数据点配置格式 (功能码,起始地址,数量,装置内序号起始,类型,参数)
struct ModbusPointConfig {
    int function_code;          // Modbus 功能码 (01,02,03,04,05,06,15,16)
    int start_addr;             // 起始地址 (16进制)
    int count;                  // 数量
    int redis_start_index;      // Redis中的起始序号
    int data_type;              // 数据类型 (0=uint16, 1=int16, 2=uint32, 3=int32, 4=float, 5=double)
    int param;                  // 参数 (保留)

    ModbusPointConfig() : function_code(3), start_addr(0), count(1),
                         redis_start_index(1), data_type(0), param(0) {}

    ModbusPointConfig(int fc, int addr, int cnt, int redis_idx, int type, int p = 0)
        : function_code(fc), start_addr(addr), count(cnt),
          redis_start_index(redis_idx), data_type(type), param(p) {}
};

// 按数据类型分组的点配置
struct DevicePointConfig {
    std::vector<ModbusPointConfig> yc_configs;  // 遥测配置
    std::vector<ModbusPointConfig> yx_configs;  // 遥信配置
    std::vector<ModbusPointConfig> yt_configs;  // 遥脉配置
    std::vector<ModbusPointConfig> yk_configs;  // 遥控配置
    std::vector<ModbusPointConfig> ys_configs;  // 遥设配置
};

// 设备参数
struct DeviceParam {
    int device_id;          // 设备ID
    std::string device_name; // 设备名称
    std::string point_file; // 点表文件路径 (可选)
    int slave_id;           // Modbus 从站地址
    CommType comm_type;     // 通信类型
    RtuCommParam rtu_param; // RTU参数
    TcpCommParam tcp_param; // TCP参数
    DeviceStatus status;    // 设备状态

    // 数据采集配置
    int scan_interval_ms;   // 扫描间隔 (毫秒)
    bool enable_auto_scan;  // 启用自动扫描

    // 变化检测配置
    bool enable_change_detection; // 启用变化检测
    double change_threshold;      // 变化阈值

    // 内嵌数据点配置
    DevicePointConfig point_config; // 数据点配置

    DeviceParam()
        : device_id(0)
        , device_name("Modbus-Device")
        , point_file("")
        , slave_id(1)
        , comm_type(CommType::RTU)
        , status(DeviceStatus::DISCONNECTED)
        , scan_interval_ms(1000)
        , enable_auto_scan(true)
        , enable_change_detection(false)
        , change_threshold(0.01) {}

    std::string GetKey() const {
        return device_name + "-" + std::to_string(device_id);
    }
};

// 设备配置信息（用于设备管理器）
struct DeviceConfig {
    int device_id;
    std::string device_name;
    std::string device_type;

    // 通信配置
    CommType comm_type = CommType::RTU;
    std::string comm_config;  // JSON 格式的通信配置
    int slave_id = 1;         // Modbus 从站地址

    // 数据点配置
    std::string point_table_file;  // 点表文件路径
    DevicePointConfig embedded_point_config;  // 内嵌数据点配置
    int scan_interval_ms = 1000;
    bool enable_auto_scan = true;

    // 变化检测配置
    bool enable_change_detection = false;  // 默认不启用变化检测
    double change_threshold = 0.01;        // 变化阈值

    // Redis 配置
    bool enable_redis = true;
    int redis_report_interval_ms = 200;

    // 其他配置
    bool enable_logging = true;
    std::string log_level = "INFO";

    DeviceConfig() : device_id(0) {}
    DeviceConfig(int id, const std::string& name) : device_id(id), device_name(name) {}
};

// 服务参数
struct ServiceParam {
    // 服务基本信息
    std::string service_name;       // 服务名称
    std::string log_level;          // 日志级别
    std::string log_file;           // 日志文件
    int max_threads;                // 最大线程数

    // Redis 配置
    std::string redis_ip;           // Redis IP地址
    int redis_port;                 // Redis 端口
    std::string redis_password;     // Redis 密码
    int redis_database;             // Redis 数据库
    int report_interval_ms;         // 上报间隔(毫秒)

    // 设备列表
    std::map<int, DeviceParam> devices; // 设备列表

    ServiceParam()
        : service_name("Modbus Service")
        , log_level("INFO")
        , log_file("modbus_service.log")
        , max_threads(4)
        , redis_ip("127.0.0.1")
        , redis_port(6379)
        , redis_password("")
        , redis_database(0)
        , report_interval_ms(200) {}
};

// Redis 遥测数据结构（兼容现有格式）
struct RedisYC {
    int device_id;          // 设备ID
    int data_id;            // 数据点ID
    double value;           // 数值
    DataQuality quality;    // 数据质量
    uint64_t timestamp;     // 时间戳
    
    RedisYC() : device_id(0), data_id(0), value(0.0), 
                quality(DataQuality::BAD), timestamp(0) {}
};

// Redis 遥信数据结构（兼容现有格式）
struct RedisYX {
    int device_id;          // 设备ID
    int data_id;            // 数据点ID
    bool value;             // 状态值
    DataQuality quality;    // 数据质量
    uint64_t timestamp;     // 时间戳
    
    RedisYX() : device_id(0), data_id(0), value(false), 
                quality(DataQuality::BAD), timestamp(0) {}
};

// Redis 遥调命令结构（兼容现有格式）
struct RedisYT {
    int device_id;          // 设备ID
    int data_id;            // 数据点ID
    double value;           // 设定值
    uint8_t status;         // 状态
    
    RedisYT() : device_id(0), data_id(0), value(0.0), status(0) {}
};

// Redis 遥控命令结构（兼容现有格式）
struct RedisYK {
    int device_id;          // 设备ID
    int data_id;            // 数据点ID
    uint16_t value;         // 控制值
    uint8_t status;         // 状态
    
    RedisYK() : device_id(0), data_id(0), value(0), status(0) {}
};

// 数据值联合体
union DataValue {
    bool bool_val;
    uint16_t uint16_val;
    int16_t int16_val;
    uint32_t uint32_val;
    int32_t int32_val;
    float float_val;
    double double_val;
    
    DataValue() : double_val(0.0) {}
};

// 通用数据项
struct DataItem {
    TypeIndex type_idx;     // 类型索引
    DataValue value;        // 数据值
    DataQuality quality;    // 数据质量
    uint64_t timestamp;     // 时间戳
    
    DataItem() : quality(DataQuality::BAD), timestamp(0) {}
};

// 错误码定义
enum class ErrorCode {
    SUCCESS = 0,
    INVALID_PARAM = -1,
    COMM_ERROR = -2,
    TIMEOUT = -3,
    DEVICE_NOT_FOUND = -4,
    CONFIG_ERROR = -5,
    REDIS_ERROR = -6,
    NOT_INITIALIZED = -7,
    NOT_SUPPORTED = -8,
    AUTH_ERROR = -9,
    QUEUE_FULL = -10,
    UNKNOWN_ERROR = -99
};

// 结果类型
template<typename T>
struct Result {
    ErrorCode error_code;
    T data;
    std::string error_message;
    
    Result() : error_code(ErrorCode::SUCCESS) {}
    Result(ErrorCode code, const std::string& msg = "") 
        : error_code(code), error_message(msg) {}
    Result(const T& value) : error_code(ErrorCode::SUCCESS), data(value) {}
    
    bool IsSuccess() const { return error_code == ErrorCode::SUCCESS; }
    bool IsError() const { return error_code != ErrorCode::SUCCESS; }
};

// 类型别名
using DataPointList = std::vector<DataPoint>;
using DataItemList = std::vector<DataItem>;
using DeviceParamMap = std::map<int, DeviceParam>;

// 常量定义
namespace Constants {
    // Redis 频道名称（兼容现有系统）
    const std::string REDIS_CHANNEL_YK = "EVENT_RC_SCADA_YK";
    const std::string REDIS_CHANNEL_YT = "EVENT_RC_SCADA_YT";
    const std::string REDIS_CHANNEL_YC = "EVENT_RC_SCADA_YC";
    const std::string REDIS_CHANNEL_YX = "EVENT_RC_SCADA_YX";
    
    // 默认配置
    const int DEFAULT_SLAVE_ID = 1;
    const int DEFAULT_TIMEOUT_MS = 1000;
    const int DEFAULT_REPORT_INTERVAL_MS = 200;
    const int MAX_REGISTER_COUNT = 125;
    const int MAX_COIL_COUNT = 2000;
}

} // namespace modbus

#endif // MODBUS_TYPES_H
