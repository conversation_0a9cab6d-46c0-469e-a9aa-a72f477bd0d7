#pragma once

#include <chrono>
#include <thread>
#include <atomic>
#include <memory>
#include <vector>
#include <array>
#include <cstring>
#include "../types/modbus_types.h"
#include "../utils/utils.h"

namespace modbus {
namespace optimized {

// 预分配缓冲区管理
class PreallocatedBuffers {
public:
    static constexpr size_t RECV_BUFFER_SIZE = 4096;
    static constexpr size_t SEND_BUFFER_SIZE = 1550;
    static constexpr size_t PROCESS_BUFFER_SIZE = 2048;
    
    // 设备控制块
    struct DeviceControlBlock {
        uint16_t query_index;           // 轮询索引
        uint16_t query_type;            // 查询类型索引
        uint32_t last_poll_time;        // 最后轮询时间
        uint16_t recv_state;            // 接收状态机
        uint16_t recv_length;           // 接收长度
        uint8_t  device_status;         // 设备状态
        uint8_t  reserved[3];           // 对齐填充
    } __attribute__((packed));
    
private:
    // 内存对齐的缓冲区
    alignas(64) uint8_t recv_buffer_[RECV_BUFFER_SIZE];
    alignas(64) uint8_t send_buffer_[SEND_BUFFER_SIZE];
    alignas(64) uint8_t process_buffer_[PROCESS_BUFFER_SIZE];
    alignas(64) DeviceControlBlock control_block_;
    
public:
    explicit PreallocatedBuffers(int device_id);
    ~PreallocatedBuffers() = default;
    
    // 禁止拷贝和移动
    PreallocatedBuffers(const PreallocatedBuffers&) = delete;
    PreallocatedBuffers& operator=(const PreallocatedBuffers&) = delete;
    PreallocatedBuffers(PreallocatedBuffers&&) = delete;
    PreallocatedBuffers& operator=(PreallocatedBuffers&&) = delete;
    
    uint8_t* GetReceiveBuffer() { return recv_buffer_; }
    uint8_t* GetSendBuffer() { return send_buffer_; }
    uint8_t* GetProcessBuffer() { return process_buffer_; }
    DeviceControlBlock* GetControlBlock() { return &control_block_; }
    
    const uint8_t* GetReceiveBuffer() const { return recv_buffer_; }
    const uint8_t* GetSendBuffer() const { return send_buffer_; }
    const uint8_t* GetProcessBuffer() const { return process_buffer_; }
    const DeviceControlBlock* GetControlBlock() const { return &control_block_; }
    
    void ClearBuffers();
    void ResetControlBlock();
};

// Modbus寄存器分组
struct ModbusRegisterGroup {
    uint16_t start_address;
    uint16_t register_count;
    DataType data_type;
    ByteOrder byte_order;
    std::vector<uint16_t> point_mappings;  // 点号映射
    
    ModbusRegisterGroup() = default;
    ModbusRegisterGroup(uint16_t start, uint16_t count, DataType type, ByteOrder order)
        : start_address(start), register_count(count), data_type(type), byte_order(order) {}
    
    // 检查是否可以与另一个分组合并
    bool CanMergeWith(const ModbusRegisterGroup& other) const;
    
    // 获取结束地址
    uint16_t GetEndAddress() const { return start_address + register_count - 1; }
    
    // 检查地址是否在范围内
    bool ContainsAddress(uint16_t address) const {
        return address >= start_address && address <= GetEndAddress();
    }
};

// 设备轮询调度器
class DeviceRoundRobin {
public:
    struct PollConfiguration {
        uint16_t yc_count;              // 遥测点数量
        uint16_t yx_count;              // 遥信点数量
        uint16_t poll_interval_ms;      // 轮询间隔
        std::vector<ModbusRegisterGroup> yc_groups;  // 遥测分组
        std::vector<ModbusRegisterGroup> yx_groups;  // 遥信分组
        
        PollConfiguration() : yc_count(0), yx_count(0), poll_interval_ms(1000) {}
    };
    
private:
    PollConfiguration config_;
    uint16_t current_query_index_;
    uint16_t current_query_type_;
    std::chrono::steady_clock::time_point last_poll_time_;
    
public:
    explicit DeviceRoundRobin(int device_id);
    ~DeviceRoundRobin() = default;
    
    // 初始化轮询配置
    bool Initialize(const std::vector<DataPoint>& data_points);
    
    // 获取下一个轮询分组 (O(1)复杂度)
    ModbusRegisterGroup* GetNextPollGroup();
    
    // 检查是否应该轮询
    bool ShouldPoll() const;
    
    // 更新轮询时间
    void UpdatePollTime();
    
    // 获取配置
    const PollConfiguration& GetConfiguration() const { return config_; }
    
    // 重置轮询状态
    void Reset();
    
private:
    // 优化寄存器分组
    std::vector<ModbusRegisterGroup> OptimizeRegisterGroups(
        const std::vector<DataPoint>& points, DataType filter_type);
};

// 原子操作设备状态
class AtomicDeviceState {
public:
    // 状态位定义
    static constexpr uint32_t CONNECTED_BIT = 0x01;
    static constexpr uint32_t SCANNING_BIT = 0x02;
    static constexpr uint32_t ERROR_BIT = 0x04;
    static constexpr uint32_t PROCESSING_BIT = 0x08;
    static constexpr uint32_t QUERY_INDEX_MASK = 0xFFFF0000;
    static constexpr uint32_t QUERY_INDEX_SHIFT = 16;
    
private:
    std::atomic<uint32_t> state_word_{0};
    
public:
    AtomicDeviceState() = default;
    ~AtomicDeviceState() = default;
    
    // 连接状态
    void SetConnected(bool connected);
    bool IsConnected() const;
    
    // 扫描状态
    void SetScanning(bool scanning);
    bool IsScanning() const;
    
    // 错误状态
    void SetError(bool error);
    bool HasError() const;
    
    // 处理状态
    void SetProcessing(bool processing);
    bool IsProcessing() const;
    
    // 查询索引
    void SetQueryIndex(uint16_t index);
    uint16_t GetQueryIndex() const;
    
    // 获取完整状态
    uint32_t GetFullState() const;
    void SetFullState(uint32_t state);
    
    // 重置所有状态
    void Reset();
};

// 层次化定时器系统
class HierarchicalTimerSystem {
public:
    struct TimerLevel {
        std::chrono::milliseconds interval{0};
        std::chrono::steady_clock::time_point last_trigger;
        bool enabled{false};
        
        TimerLevel() = default;
        TimerLevel(std::chrono::milliseconds intv) : interval(intv), enabled(false) {}
    };
    
private:
    TimerLevel port_timer_;     // 端口级定时器
    TimerLevel device_timer_;   // 设备级定时器
    TimerLevel command_timer_;  // 命令级定时器
    TimerLevel poll_timer_;     // 轮询定时器
    
public:
    HierarchicalTimerSystem() = default;
    ~HierarchicalTimerSystem() = default;
    
    // 端口定时器
    bool CheckPortTimer();
    void StartPortTimer(std::chrono::milliseconds interval);
    void StopPortTimer();
    
    // 设备定时器
    bool CheckDeviceTimer();
    void StartDeviceTimer(std::chrono::milliseconds interval);
    void StopDeviceTimer();
    
    // 命令定时器
    bool CheckCommandTimer();
    void StartCommandTimer(std::chrono::milliseconds timeout);
    void StopCommandTimer();
    
    // 轮询定时器
    bool CheckPollTimer();
    void StartPollTimer(std::chrono::milliseconds interval);
    void StopPollTimer();
    
    // 重置所有定时器
    void ResetAllTimers();
    
private:
    bool CheckTimer(TimerLevel& timer);
    void StartTimer(TimerLevel& timer, std::chrono::milliseconds interval);
    void StopTimer(TimerLevel& timer);
};

// 优化的设备工作器
class OptimizedDeviceWorker {
public:
    static constexpr int DEFAULT_CYCLE_TIME_MS = 3;  // 固定3ms循环周期
    
    enum class WorkerState {
        STOPPED,
        STARTING,
        RUNNING,
        STOPPING,
        ERROR
    };
    
private:
    int device_id_;
    std::atomic<WorkerState> state_{WorkerState::STOPPED};
    std::atomic<bool> shutdown_requested_{false};
    
    std::unique_ptr<std::thread> worker_thread_;
    std::unique_ptr<PreallocatedBuffers> buffers_;
    std::unique_ptr<DeviceRoundRobin> scheduler_;
    std::unique_ptr<AtomicDeviceState> device_state_;
    std::unique_ptr<HierarchicalTimerSystem> timer_system_;
    
    int cycle_time_ms_;
    
    // 统计信息
    struct Statistics {
        std::atomic<uint64_t> total_cycles{0};
        std::atomic<uint64_t> successful_cycles{0};
        std::atomic<uint64_t> error_cycles{0};
        std::atomic<uint64_t> total_polls{0};
        std::atomic<uint64_t> successful_polls{0};
        std::atomic<uint32_t> max_cycle_time_us{0};
        std::atomic<uint32_t> avg_cycle_time_us{0};
    } stats_;
    
public:
    explicit OptimizedDeviceWorker(int device_id, int cycle_time_ms = DEFAULT_CYCLE_TIME_MS);
    ~OptimizedDeviceWorker();
    
    // 禁止拷贝和移动
    OptimizedDeviceWorker(const OptimizedDeviceWorker&) = delete;
    OptimizedDeviceWorker& operator=(const OptimizedDeviceWorker&) = delete;
    OptimizedDeviceWorker(OptimizedDeviceWorker&&) = delete;
    OptimizedDeviceWorker& operator=(OptimizedDeviceWorker&&) = delete;
    
    // 生命周期管理
    bool Initialize(const std::vector<DataPoint>& data_points);
    bool Start();
    void Stop();
    bool IsRunning() const;
    
    // 状态查询
    WorkerState GetState() const { return state_.load(); }
    int GetDeviceId() const { return device_id_; }
    
    // 统计信息
    Statistics GetStatistics() const;
    void ResetStatistics();
    
private:
    // 主工作循环
    void WorkerMainLoop();
    
    // 三阶段处理
    bool ProcessReceive();
    bool ProcessData();
    bool ProcessSend();
    
    // 周期控制
    void SleepUntilNextCycle(std::chrono::steady_clock::time_point start_time);
    
    // 统计更新
    void UpdateCycleStatistics(std::chrono::microseconds cycle_time, bool success);
};

} // namespace optimized
} // namespace modbus
