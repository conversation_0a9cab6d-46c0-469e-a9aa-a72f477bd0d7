#include "optimized_device_worker.h"
#include "../utils/logger.h"
#include <algorithm>
#include <cassert>

namespace modbus {
namespace optimized {

// PreallocatedBuffers 实现
PreallocatedBuffers::PreallocatedBuffers(int device_id) {
    // 初始化缓冲区，避免运行时分配
    std::memset(recv_buffer_, 0, RECV_BUFFER_SIZE);
    std::memset(send_buffer_, 0, SEND_BUFFER_SIZE);
    std::memset(process_buffer_, 0, PROCESS_BUFFER_SIZE);
    std::memset(&control_block_, 0, sizeof(control_block_));
    
    WRITE_DEBUG_LOG("设备 %d 预分配缓冲区初始化完成", device_id);
}

void PreallocatedBuffers::ClearBuffers() {
    std::memset(recv_buffer_, 0, RECV_BUFFER_SIZE);
    std::memset(send_buffer_, 0, SEND_BUFFER_SIZE);
    std::memset(process_buffer_, 0, PROCESS_BUFFER_SIZE);
}

void PreallocatedBuffers::ResetControlBlock() {
    std::memset(&control_block_, 0, sizeof(control_block_));
}

// ModbusRegisterGroup 实现
bool ModbusRegisterGroup::CanMergeWith(const ModbusRegisterGroup& other) const {
    // 检查是否可以合并：连续地址、相同类型、相同字节序
    return (start_address + register_count == other.start_address) &&
           (data_type == other.data_type) &&
           (byte_order == other.byte_order) &&
           (register_count + other.register_count <= 125);  // Modbus限制
}

// DeviceRoundRobin 实现
DeviceRoundRobin::DeviceRoundRobin(int device_id) 
    : current_query_index_(0), current_query_type_(0) {
    last_poll_time_ = std::chrono::steady_clock::now();
    WRITE_DEBUG_LOG("设备 %d 轮询调度器初始化", device_id);
}

bool DeviceRoundRobin::Initialize(const std::vector<DataPoint>& data_points) {
    if (data_points.empty()) {
        WRITE_WARN_LOG("数据点列表为空，无法初始化轮询调度器");
        return false;
    }
    
    // 分离遥测和遥信数据点
    std::vector<DataPoint> yc_points, yx_points;
    for (const auto& point : data_points) {
        if (point.type_idx.data_type == DataType::YC) {
            yc_points.push_back(point);
        } else if (point.type_idx.data_type == DataType::YX) {
            yx_points.push_back(point);
        }
    }
    
    // 优化寄存器分组
    config_.yc_groups = OptimizeRegisterGroups(yc_points, DataType::YC);
    config_.yx_groups = OptimizeRegisterGroups(yx_points, DataType::YX);
    config_.yc_count = static_cast<uint16_t>(config_.yc_groups.size());
    config_.yx_count = static_cast<uint16_t>(config_.yx_groups.size());
    
    WRITE_INFO_LOG("轮询调度器初始化完成: 遥测分组 %d, 遥信分组 %d", 
                   config_.yc_count, config_.yx_count);
    
    return true;
}

ModbusRegisterGroup* DeviceRoundRobin::GetNextPollGroup() {
    // 轮询类型切换 (遥测/遥信分离)
    uint16_t total_groups = config_.yc_count + config_.yx_count;
    if (total_groups == 0) {
        return nullptr;
    }
    
    if (current_query_type_ >= total_groups) {
        current_query_type_ = 0;
    }
    
    ModbusRegisterGroup* group = nullptr;
    if (current_query_type_ < config_.yc_count) {
        // 遥测处理
        group = &config_.yc_groups[current_query_type_];
    } else {
        // 遥信处理
        group = &config_.yx_groups[current_query_type_ - config_.yc_count];
    }
    
    current_query_type_++;
    return group;
}

bool DeviceRoundRobin::ShouldPoll() const {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
        now - last_poll_time_);
    return elapsed >= std::chrono::milliseconds(config_.poll_interval_ms);
}

void DeviceRoundRobin::UpdatePollTime() {
    last_poll_time_ = std::chrono::steady_clock::now();
}

void DeviceRoundRobin::Reset() {
    current_query_index_ = 0;
    current_query_type_ = 0;
    last_poll_time_ = std::chrono::steady_clock::now();
}

std::vector<ModbusRegisterGroup> DeviceRoundRobin::OptimizeRegisterGroups(
    const std::vector<DataPoint>& points, DataType filter_type) {
    
    std::vector<ModbusRegisterGroup> groups;
    if (points.empty()) {
        return groups;
    }
    
    // 按地址排序
    auto sorted_points = points;
    std::sort(sorted_points.begin(), sorted_points.end(),
             [](const DataPoint& a, const DataPoint& b) {
                 return a.address < b.address;
             });
    
    // 合并连续地址
    ModbusRegisterGroup current_group;
    bool group_started = false;
    
    for (const auto& point : sorted_points) {
        if (point.type_idx.data_type != filter_type) {
            continue;
        }
        
        if (!group_started) {
            // 开始新分组
            current_group = ModbusRegisterGroup(point.address, 1, 
                                              point.type_idx.data_type, 
                                              ByteOrder::BIG_ENDIAN);
            current_group.point_mappings.push_back(point.type_idx.point_id);
            group_started = true;
        } else if (point.address == current_group.GetEndAddress() + 1 &&
                   current_group.register_count < 125) {
            // 扩展当前分组
            current_group.register_count++;
            current_group.point_mappings.push_back(point.type_idx.point_id);
        } else {
            // 保存当前分组，开始新分组
            groups.push_back(current_group);
            current_group = ModbusRegisterGroup(point.address, 1,
                                              point.type_idx.data_type,
                                              ByteOrder::BIG_ENDIAN);
            current_group.point_mappings.push_back(point.type_idx.point_id);
        }
    }
    
    // 保存最后一个分组
    if (group_started) {
        groups.push_back(current_group);
    }
    
    WRITE_DEBUG_LOG("数据类型 %d 优化完成: %zu 个数据点合并为 %zu 个分组",
                   static_cast<int>(filter_type), sorted_points.size(), groups.size());
    
    return groups;
}

// AtomicDeviceState 实现
void AtomicDeviceState::SetConnected(bool connected) {
    if (connected) {
        state_word_.fetch_or(CONNECTED_BIT, std::memory_order_relaxed);
    } else {
        state_word_.fetch_and(~CONNECTED_BIT, std::memory_order_relaxed);
    }
}

bool AtomicDeviceState::IsConnected() const {
    return (state_word_.load(std::memory_order_relaxed) & CONNECTED_BIT) != 0;
}

void AtomicDeviceState::SetScanning(bool scanning) {
    if (scanning) {
        state_word_.fetch_or(SCANNING_BIT, std::memory_order_relaxed);
    } else {
        state_word_.fetch_and(~SCANNING_BIT, std::memory_order_relaxed);
    }
}

bool AtomicDeviceState::IsScanning() const {
    return (state_word_.load(std::memory_order_relaxed) & SCANNING_BIT) != 0;
}

void AtomicDeviceState::SetError(bool error) {
    if (error) {
        state_word_.fetch_or(ERROR_BIT, std::memory_order_relaxed);
    } else {
        state_word_.fetch_and(~ERROR_BIT, std::memory_order_relaxed);
    }
}

bool AtomicDeviceState::HasError() const {
    return (state_word_.load(std::memory_order_relaxed) & ERROR_BIT) != 0;
}

void AtomicDeviceState::SetProcessing(bool processing) {
    if (processing) {
        state_word_.fetch_or(PROCESSING_BIT, std::memory_order_relaxed);
    } else {
        state_word_.fetch_and(~PROCESSING_BIT, std::memory_order_relaxed);
    }
}

bool AtomicDeviceState::IsProcessing() const {
    return (state_word_.load(std::memory_order_relaxed) & PROCESSING_BIT) != 0;
}

void AtomicDeviceState::SetQueryIndex(uint16_t index) {
    uint32_t new_value = static_cast<uint32_t>(index) << QUERY_INDEX_SHIFT;
    uint32_t old_value = state_word_.load(std::memory_order_relaxed);
    uint32_t updated_value;
    
    do {
        updated_value = (old_value & ~QUERY_INDEX_MASK) | new_value;
    } while (!state_word_.compare_exchange_weak(old_value, updated_value,
                                               std::memory_order_relaxed));
}

uint16_t AtomicDeviceState::GetQueryIndex() const {
    return static_cast<uint16_t>(
        (state_word_.load(std::memory_order_relaxed) & QUERY_INDEX_MASK) 
        >> QUERY_INDEX_SHIFT);
}

uint32_t AtomicDeviceState::GetFullState() const {
    return state_word_.load(std::memory_order_relaxed);
}

void AtomicDeviceState::SetFullState(uint32_t state) {
    state_word_.store(state, std::memory_order_relaxed);
}

void AtomicDeviceState::Reset() {
    state_word_.store(0, std::memory_order_relaxed);
}

// HierarchicalTimerSystem 实现
bool HierarchicalTimerSystem::CheckPortTimer() {
    return CheckTimer(port_timer_);
}

void HierarchicalTimerSystem::StartPortTimer(std::chrono::milliseconds interval) {
    StartTimer(port_timer_, interval);
}

void HierarchicalTimerSystem::StopPortTimer() {
    StopTimer(port_timer_);
}

bool HierarchicalTimerSystem::CheckDeviceTimer() {
    return CheckTimer(device_timer_);
}

void HierarchicalTimerSystem::StartDeviceTimer(std::chrono::milliseconds interval) {
    StartTimer(device_timer_, interval);
}

void HierarchicalTimerSystem::StopDeviceTimer() {
    StopTimer(device_timer_);
}

bool HierarchicalTimerSystem::CheckCommandTimer() {
    return CheckTimer(command_timer_);
}

void HierarchicalTimerSystem::StartCommandTimer(std::chrono::milliseconds timeout) {
    StartTimer(command_timer_, timeout);
}

void HierarchicalTimerSystem::StopCommandTimer() {
    StopTimer(command_timer_);
}

bool HierarchicalTimerSystem::CheckPollTimer() {
    return CheckTimer(poll_timer_);
}

void HierarchicalTimerSystem::StartPollTimer(std::chrono::milliseconds interval) {
    StartTimer(poll_timer_, interval);
}

void HierarchicalTimerSystem::StopPollTimer() {
    StopTimer(poll_timer_);
}

void HierarchicalTimerSystem::ResetAllTimers() {
    StopTimer(port_timer_);
    StopTimer(device_timer_);
    StopTimer(command_timer_);
    StopTimer(poll_timer_);
}

bool HierarchicalTimerSystem::CheckTimer(TimerLevel& timer) {
    if (!timer.enabled) return false;
    
    auto now = std::chrono::steady_clock::now();
    if (now - timer.last_trigger >= timer.interval) {
        timer.last_trigger = now;
        return true;
    }
    return false;
}

void HierarchicalTimerSystem::StartTimer(TimerLevel& timer, std::chrono::milliseconds interval) {
    timer.interval = interval;
    timer.last_trigger = std::chrono::steady_clock::now();
    timer.enabled = true;
}

void HierarchicalTimerSystem::StopTimer(TimerLevel& timer) {
    timer.enabled = false;
}

// OptimizedDeviceWorker 实现
OptimizedDeviceWorker::OptimizedDeviceWorker(int device_id, int cycle_time_ms)
    : device_id_(device_id), cycle_time_ms_(cycle_time_ms) {

    // 创建组件
    buffers_ = std::make_unique<PreallocatedBuffers>(device_id);
    scheduler_ = std::make_unique<DeviceRoundRobin>(device_id);
    device_state_ = std::make_unique<AtomicDeviceState>();
    timer_system_ = std::make_unique<HierarchicalTimerSystem>();

    WRITE_INFO_LOG("设备 %d 优化工作器创建完成，循环周期 %dms", device_id, cycle_time_ms);
}

OptimizedDeviceWorker::~OptimizedDeviceWorker() {
    Stop();
    WRITE_INFO_LOG("设备 %d 优化工作器已销毁", device_id_);
}

bool OptimizedDeviceWorker::Initialize(const std::vector<DataPoint>& data_points) {
    if (state_.load() != WorkerState::STOPPED) {
        WRITE_ERROR_LOG("设备 %d 工作器状态错误，无法初始化", device_id_);
        return false;
    }

    // 初始化调度器
    if (!scheduler_->Initialize(data_points)) {
        WRITE_ERROR_LOG("设备 %d 调度器初始化失败", device_id_);
        return false;
    }

    // 重置状态
    device_state_->Reset();
    timer_system_->ResetAllTimers();
    buffers_->ResetControlBlock();

    WRITE_INFO_LOG("设备 %d 优化工作器初始化完成", device_id_);
    return true;
}

bool OptimizedDeviceWorker::Start() {
    WorkerState expected = WorkerState::STOPPED;
    if (!state_.compare_exchange_strong(expected, WorkerState::STARTING)) {
        WRITE_WARN_LOG("设备 %d 工作器已在运行或正在启动", device_id_);
        return false;
    }

    shutdown_requested_.store(false);

    try {
        // 启动工作线程
        worker_thread_ = std::make_unique<std::thread>(&OptimizedDeviceWorker::WorkerMainLoop, this);

        // 等待启动完成
        auto start_time = std::chrono::steady_clock::now();
        while (state_.load() == WorkerState::STARTING) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));

            // 超时检查
            auto elapsed = std::chrono::steady_clock::now() - start_time;
            if (elapsed > std::chrono::seconds(5)) {
                WRITE_ERROR_LOG("设备 %d 工作器启动超时", device_id_);
                state_.store(WorkerState::ERROR);
                return false;
            }
        }

        bool success = (state_.load() == WorkerState::RUNNING);
        if (success) {
            WRITE_INFO_LOG("设备 %d 优化工作器启动成功", device_id_);
        } else {
            WRITE_ERROR_LOG("设备 %d 优化工作器启动失败", device_id_);
        }

        return success;

    } catch (const std::exception& e) {
        WRITE_ERROR_LOG("设备 %d 工作器启动异常: %s", device_id_, e.what());
        state_.store(WorkerState::ERROR);
        return false;
    }
}

void OptimizedDeviceWorker::Stop() {
    WorkerState current_state = state_.load();
    if (current_state == WorkerState::STOPPED || current_state == WorkerState::STOPPING) {
        return;
    }

    WRITE_INFO_LOG("正在停止设备 %d 优化工作器", device_id_);

    state_.store(WorkerState::STOPPING);
    shutdown_requested_.store(true);

    // 等待工作线程结束
    if (worker_thread_ && worker_thread_->joinable()) {
        worker_thread_->join();
    }
    worker_thread_.reset();

    // 重置状态
    device_state_->Reset();
    timer_system_->ResetAllTimers();
    state_.store(WorkerState::STOPPED);

    WRITE_INFO_LOG("设备 %d 优化工作器已停止", device_id_);
}

bool OptimizedDeviceWorker::IsRunning() const {
    return state_.load() == WorkerState::RUNNING;
}

OptimizedDeviceWorker::Statistics OptimizedDeviceWorker::GetStatistics() const {
    return stats_;
}

void OptimizedDeviceWorker::ResetStatistics() {
    stats_.total_cycles.store(0);
    stats_.successful_cycles.store(0);
    stats_.error_cycles.store(0);
    stats_.total_polls.store(0);
    stats_.successful_polls.store(0);
    stats_.max_cycle_time_us.store(0);
    stats_.avg_cycle_time_us.store(0);
}

void OptimizedDeviceWorker::WorkerMainLoop() {
    WRITE_INFO_LOG("设备 %d 工作线程启动", device_id_);

    try {
        // 标记为运行状态
        state_.store(WorkerState::RUNNING);
        device_state_->SetConnected(true);

        // 启动轮询定时器
        timer_system_->StartPollTimer(std::chrono::milliseconds(
            scheduler_->GetConfiguration().poll_interval_ms));

        uint64_t cycle_count = 0;
        uint64_t total_cycle_time_us = 0;

        while (!shutdown_requested_.load()) {
            auto cycle_start = std::chrono::high_resolution_clock::now();

            bool cycle_success = true;

            // 三阶段处理：接收→处理→发送
            device_state_->SetProcessing(true);

            if (!ProcessReceive()) {
                cycle_success = false;
            }

            if (!ProcessData()) {
                cycle_success = false;
            }

            if (!ProcessSend()) {
                cycle_success = false;
            }

            device_state_->SetProcessing(false);

            // 计算周期时间
            auto cycle_end = std::chrono::high_resolution_clock::now();
            auto cycle_time = std::chrono::duration_cast<std::chrono::microseconds>(
                cycle_end - cycle_start);

            // 更新统计信息
            UpdateCycleStatistics(cycle_time, cycle_success);

            // 固定周期控制
            SleepUntilNextCycle(cycle_start);

            cycle_count++;
        }

    } catch (const std::exception& e) {
        WRITE_ERROR_LOG("设备 %d 工作线程异常: %s", device_id_, e.what());
        state_.store(WorkerState::ERROR);
        device_state_->SetError(true);
    }

    device_state_->SetConnected(false);
    device_state_->SetScanning(false);
    device_state_->SetProcessing(false);

    WRITE_INFO_LOG("设备 %d 工作线程结束", device_id_);
}

} // namespace optimized
} // namespace modbus
