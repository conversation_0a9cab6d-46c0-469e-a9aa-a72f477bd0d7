#include "service/modbus_service.h"
#include "utils/logger.h"
#include <iostream>
#include <exception>

using namespace modbus;

int main(int argc, char* argv[]) {
    try {
        // 使用服务启动器运行服务
        return ServiceLauncher::Run(argc, argv);

    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown fatal error occurred" << std::endl;
        return 1;
    }
}
