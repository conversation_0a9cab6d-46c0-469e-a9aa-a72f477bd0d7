#include "redis_subscriber.h"
#include "redis_message_handler.h"
#include "../utils/utils.h"
#include <sstream>
#include <algorithm>
#include <cstring>

namespace modbus {

#ifdef HAVE_HIREDIS

// RedisSubscriber 实现
RedisSubscriber::RedisSubscriber(const RedisConnectionParam& param)
    : param_(param)
    , redis_context_(nullptr)
    , stop_event_(true) {  // manual reset event
}

RedisSubscriber::~RedisSubscriber() {
    Stop();
    StopAutoReconnect();
    Disconnect();
}

Result<bool> RedisSubscriber::Connect() {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (is_connected_) {
        return Result<bool>(true);
    }
    
    auto result = CreateConnection();
    if (!result.IsSuccess()) {
        UpdateConnectionStatus(false, result.error_message);
        return result;
    }
    
    // 认证
    if (param_.enable_auth && !param_.password.empty()) {
        auto auth_result = Authenticate();
        if (!auth_result.IsSuccess()) {
            DestroyConnection();
            UpdateConnectionStatus(false, auth_result.error_message);
            return auth_result;
        }
    }
    
    // 选择数据库
    if (param_.database != 0) {
        auto db_result = SelectDatabase();
        if (!db_result.IsSuccess()) {
            DestroyConnection();
            UpdateConnectionStatus(false, db_result.error_message);
            return db_result;
        }
    }
    
    // 测试连接
    auto test_result = TestConnection();
    if (!test_result.IsSuccess()) {
        DestroyConnection();
        UpdateConnectionStatus(false, test_result.error_message);
        return test_result;
    }
    
    is_connected_ = true;
    UpdateConnectionStatus(true);
    
    WRITE_INFO_LOG("Redis 订阅器已连接到 %s:%d", param_.host.c_str(), param_.port);
    
    return Result<bool>(true);
}

void RedisSubscriber::Disconnect() {
    Stop();
    
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (is_connected_) {
        is_connected_ = false;
        UpdateConnectionStatus(false);
        WRITE_INFO_LOG("Redis 订阅器已断开连接 %s:%d", param_.host.c_str(), param_.port);
    }
    
    DestroyConnection();
}

bool RedisSubscriber::IsConnected() const {
    return is_connected_ && redis_context_ != nullptr;
}

Result<bool> RedisSubscriber::Reconnect() {
    WRITE_INFO_LOG("正在尝试重连 Redis 订阅器到 %s:%d", param_.host.c_str(), param_.port);
    
    Disconnect();
    utils::TimeUtils::SleepMs(param_.retry_interval_ms);
    
    auto result = Connect();
    if (result.IsSuccess()) {
        // 重新订阅之前的通道
        std::lock_guard<std::mutex> sub_lock(subscription_mutex_);
        
        for (const auto& channel : subscribed_channels_) {
            Subscribe(channel);
        }
        
        for (const auto& pattern : subscribed_patterns_) {
            SubscribePattern(pattern);
        }
    }
    
    return result;
}

Result<bool> RedisSubscriber::Subscribe(const std::string& channel) {
    if (!IsConnected()) {
        return Result<bool>(ErrorCode::COMM_ERROR, "Not connected");
    }
    
    std::lock_guard<std::mutex> lock(subscription_mutex_);
    
    if (subscribed_channels_.find(channel) != subscribed_channels_.end()) {
        return Result<bool>(true);  // 已经订阅
    }
    
    redisReply* reply = static_cast<redisReply*>(redisCommand(redis_context_, "SUBSCRIBE %s", channel.c_str()));
    
    if (!reply) {
        std::string error = GetRedisError();
        RecordError();
        return Result<bool>(ErrorCode::COMM_ERROR, error);
    }
    
    bool success = (reply->type == REDIS_REPLY_ARRAY && reply->elements >= 3);
    freeReplyObject(reply);
    
    if (success) {
        subscribed_channels_.insert(channel);
        WRITE_INFO_LOG("Subscribed to Redis channel: %s", channel.c_str());
    }
    
    return Result<bool>(success);
}

Result<bool> RedisSubscriber::Unsubscribe(const std::string& channel) {
    if (!IsConnected()) {
        return Result<bool>(ErrorCode::COMM_ERROR, "Not connected");
    }
    
    std::lock_guard<std::mutex> lock(subscription_mutex_);
    
    if (subscribed_channels_.find(channel) == subscribed_channels_.end()) {
        return Result<bool>(true);  // 没有订阅
    }
    
    redisReply* reply = static_cast<redisReply*>(redisCommand(redis_context_, "UNSUBSCRIBE %s", channel.c_str()));
    
    if (!reply) {
        std::string error = GetRedisError();
        RecordError();
        return Result<bool>(ErrorCode::COMM_ERROR, error);
    }
    
    bool success = (reply->type == REDIS_REPLY_ARRAY && reply->elements >= 3);
    freeReplyObject(reply);
    
    if (success) {
        subscribed_channels_.erase(channel);
        WRITE_INFO_LOG("Unsubscribed from Redis channel: %s", channel.c_str());
    }
    
    return Result<bool>(success);
}

Result<bool> RedisSubscriber::SubscribePattern(const std::string& pattern) {
    if (!IsConnected()) {
        return Result<bool>(ErrorCode::COMM_ERROR, "Not connected");
    }
    
    std::lock_guard<std::mutex> lock(subscription_mutex_);
    
    if (subscribed_patterns_.find(pattern) != subscribed_patterns_.end()) {
        return Result<bool>(true);  // 已经订阅
    }
    
    redisReply* reply = static_cast<redisReply*>(redisCommand(redis_context_, "PSUBSCRIBE %s", pattern.c_str()));
    
    if (!reply) {
        std::string error = GetRedisError();
        RecordError();
        return Result<bool>(ErrorCode::COMM_ERROR, error);
    }
    
    bool success = (reply->type == REDIS_REPLY_ARRAY && reply->elements >= 3);
    freeReplyObject(reply);
    
    if (success) {
        subscribed_patterns_.insert(pattern);
        WRITE_INFO_LOG("Subscribed to Redis pattern: %s", pattern.c_str());
    }
    
    return Result<bool>(success);
}

Result<bool> RedisSubscriber::UnsubscribePattern(const std::string& pattern) {
    if (!IsConnected()) {
        return Result<bool>(ErrorCode::COMM_ERROR, "Not connected");
    }
    
    std::lock_guard<std::mutex> lock(subscription_mutex_);
    
    if (subscribed_patterns_.find(pattern) == subscribed_patterns_.end()) {
        return Result<bool>(true);  // 没有订阅
    }
    
    redisReply* reply = static_cast<redisReply*>(redisCommand(redis_context_, "PUNSUBSCRIBE %s", pattern.c_str()));
    
    if (!reply) {
        std::string error = GetRedisError();
        RecordError();
        return Result<bool>(ErrorCode::COMM_ERROR, error);
    }
    
    bool success = (reply->type == REDIS_REPLY_ARRAY && reply->elements >= 3);
    freeReplyObject(reply);
    
    if (success) {
        subscribed_patterns_.erase(pattern);
        WRITE_INFO_LOG("Unsubscribed from Redis pattern: %s", pattern.c_str());
    }
    
    return Result<bool>(success);
}

Result<bool> RedisSubscriber::SubscribeBinary(const std::string& channel) {
    // 二进制订阅与普通订阅相同，区别在于消息处理时的解析
    return Subscribe(channel);
}

Result<bool> RedisSubscriber::Start() {
    if (is_running_) {
        return Result<bool>(true);
    }
    
    if (!IsConnected()) {
        auto connect_result = Connect();
        if (!connect_result.IsSuccess()) {
            return connect_result;
        }
    }
    
    is_running_ = true;
    stop_event_.Reset();
    subscribe_thread_ = std::make_unique<std::thread>(&RedisSubscriber::SubscribeThread, this);
    
    WRITE_INFO_LOG("Redis subscriber started");
    
    return Result<bool>(true);
}

void RedisSubscriber::Stop() {
    if (is_running_) {
        is_running_ = false;
        stop_event_.Set();
        
        if (subscribe_thread_ && subscribe_thread_->joinable()) {
            subscribe_thread_->join();
        }
        subscribe_thread_.reset();
        
        WRITE_INFO_LOG("Redis subscriber stopped");
    }
}

void RedisSubscriber::SetConnectionParam(const RedisConnectionParam& param) {
    param_ = param;
    
    // 如果已连接，需要重新连接
    if (is_connected_) {
        Reconnect();
    }
}

RedisConnectionParam RedisSubscriber::GetConnectionParam() const {
    return param_;
}

void RedisSubscriber::GetStats(int& message_count, int& error_count, int& channel_count) const {
    message_count = message_count_.load();
    error_count = error_count_.load();
    
    std::lock_guard<std::mutex> lock(subscription_mutex_);
    channel_count = subscribed_channels_.size() + subscribed_patterns_.size();
}

void RedisSubscriber::ResetStats() {
    message_count_ = 0;
    error_count_ = 0;
}

void RedisSubscriber::SetMessageCallback(MessageCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    message_callback_ = callback;
}

void RedisSubscriber::SetConnectionStatusCallback(ConnectionStatusCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    connection_callback_ = callback;
}

std::set<std::string> RedisSubscriber::GetSubscribedChannels() const {
    std::lock_guard<std::mutex> lock(subscription_mutex_);
    return subscribed_channels_;
}

std::set<std::string> RedisSubscriber::GetSubscribedPatterns() const {
    std::lock_guard<std::mutex> lock(subscription_mutex_);
    return subscribed_patterns_;
}

// 内部方法实现
Result<bool> RedisSubscriber::CreateConnection() {
    struct timeval timeout = { param_.timeout_ms / 1000, (param_.timeout_ms % 1000) * 1000 };

    redis_context_ = redisConnectWithTimeout(param_.host.c_str(), param_.port, timeout);

    if (!redis_context_ || redis_context_->err) {
        std::string error = redis_context_ ? redis_context_->errstr : "Failed to allocate redis context";
        DestroyConnection();
        return Result<bool>(ErrorCode::COMM_ERROR, error);
    }

    return Result<bool>(true);
}

void RedisSubscriber::DestroyConnection() {
    if (redis_context_) {
        redisFree(redis_context_);
        redis_context_ = nullptr;
    }
}

Result<bool> RedisSubscriber::Authenticate() {
    redisReply* reply = static_cast<redisReply*>(redisCommand(redis_context_, "AUTH %s", param_.password.c_str()));

    if (!reply) {
        return Result<bool>(ErrorCode::COMM_ERROR, GetRedisError());
    }

    bool success = (reply->type == REDIS_REPLY_STATUS && strcmp(reply->str, "OK") == 0);
    std::string error = success ? "" : (reply->str ? reply->str : "Authentication failed");

    freeReplyObject(reply);

    return success ? Result<bool>(true) : Result<bool>(ErrorCode::AUTH_ERROR, error);
}

Result<bool> RedisSubscriber::SelectDatabase() {
    redisReply* reply = static_cast<redisReply*>(redisCommand(redis_context_, "SELECT %d", param_.database));

    if (!reply) {
        return Result<bool>(ErrorCode::COMM_ERROR, GetRedisError());
    }

    bool success = (reply->type == REDIS_REPLY_STATUS && strcmp(reply->str, "OK") == 0);
    std::string error = success ? "" : (reply->str ? reply->str : "Database selection failed");

    freeReplyObject(reply);

    return success ? Result<bool>(true) : Result<bool>(ErrorCode::CONFIG_ERROR, error);
}

Result<bool> RedisSubscriber::TestConnection() {
    redisReply* reply = static_cast<redisReply*>(redisCommand(redis_context_, "PING"));

    if (!reply) {
        return Result<bool>(ErrorCode::COMM_ERROR, GetRedisError());
    }

    bool success = (reply->type == REDIS_REPLY_STATUS && strcmp(reply->str, "PONG") == 0);
    std::string error = success ? "" : "PING test failed";

    freeReplyObject(reply);

    return success ? Result<bool>(true) : Result<bool>(ErrorCode::COMM_ERROR, error);
}

// 订阅处理
void RedisSubscriber::SubscribeThread() {
    while (is_running_) {
        if (!IsConnected()) {
            if (auto_reconnect_enabled_) {
                StartAutoReconnect();
            }
            utils::TimeUtils::SleepMs(1000);
            continue;
        }

        redisReply* reply = nullptr;

        {
            std::lock_guard<std::mutex> lock(connection_mutex_);
            if (redis_context_) {
                if (redisGetReply(redis_context_, reinterpret_cast<void**>(&reply)) != REDIS_OK) {
                    RecordError();
                    is_connected_ = false;
                    UpdateConnectionStatus(false, GetRedisError());

                    if (auto_reconnect_enabled_) {
                        StartAutoReconnect();
                    }
                    continue;
                }
            }
        }

        if (reply) {
            ProcessMessage(reply);
            freeReplyObject(reply);
        }
    }
}

void RedisSubscriber::ProcessMessage(redisReply* reply) {
    if (!reply || reply->type != REDIS_REPLY_ARRAY || reply->elements < 3) {
        return;
    }

    std::string message_type = reply->element[0]->str;

    if (message_type == "subscribe" || message_type == "psubscribe" ||
        message_type == "unsubscribe" || message_type == "punsubscribe") {
        HandleSubscribeReply(reply);
    } else if (message_type == "message" || message_type == "pmessage") {
        HandleMessageReply(reply);
    }
}

void RedisSubscriber::HandleSubscribeReply(redisReply* reply) {
    if (reply->elements >= 3) {
        std::string channel = reply->element[1]->str;
        int subscriber_count = reply->element[2]->integer;

        WRITE_DEBUG_LOG("订阅更新: 通道=%s, 订阅者数=%d",
                       channel.c_str(), subscriber_count);
    }
}

void RedisSubscriber::HandleMessageReply(redisReply* reply) {
    SubscribeMessage msg;
    msg.timestamp = utils::TimeUtils::GetCurrentTimestamp();

    if (reply->element[0]->str == std::string("message") && reply->elements >= 3) {
        // 普通消息: ["message", "channel", "data"]
        msg.channel = reply->element[1]->str;

        // 检查是否为二进制数据
        if (reply->element[2]->type == REDIS_REPLY_STRING) {
            msg.message = std::string(reply->element[2]->str, reply->element[2]->len);
            msg.is_binary = true;
            msg.binary_size = reply->element[2]->len;
        } else {
            msg.message = reply->element[2]->str;
            msg.is_binary = false;
        }
    } else if (reply->element[0]->str == std::string("pmessage") && reply->elements >= 4) {
        // 模式消息: ["pmessage", "pattern", "channel", "data"]
        msg.channel = reply->element[2]->str;

        // 检查是否为二进制数据
        if (reply->element[3]->type == REDIS_REPLY_STRING) {
            msg.message = std::string(reply->element[3]->str, reply->element[3]->len);
            msg.is_binary = true;
            msg.binary_size = reply->element[3]->len;
        } else {
            msg.message = reply->element[3]->str;
            msg.is_binary = false;
        }
    } else {
        return;
    }

    RecordMessage();

    // 触发消息回调
    if (message_callback_) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        try {
            message_callback_(msg);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("消息回调函数异常: %s", e.what());
        }
    }
}

// 自动重连
void RedisSubscriber::StartAutoReconnect() {
    if (reconnect_running_) {
        return;
    }

    reconnect_running_ = true;
    reconnect_thread_ = std::make_unique<std::thread>(&RedisSubscriber::AutoReconnectThread, this);
}

void RedisSubscriber::StopAutoReconnect() {
    if (reconnect_running_) {
        reconnect_running_ = false;

        if (reconnect_thread_ && reconnect_thread_->joinable()) {
            reconnect_thread_->join();
        }
        reconnect_thread_.reset();
    }
}

void RedisSubscriber::AutoReconnectThread() {
    int attempt_count = 0;

    while (reconnect_running_ && attempt_count < param_.max_retry_count) {
        if (!IsConnected()) {
            attempt_count++;

            WRITE_INFO_LOG("Auto reconnect attempt %d/%d for Redis subscriber %s:%d",
                          attempt_count, param_.max_retry_count,
                          param_.host.c_str(), param_.port);

            auto result = Reconnect();
            if (result.IsSuccess()) {
                reconnect_running_ = false;
                return;
            }

            // 等待重连间隔
            utils::TimeUtils::SleepMs(param_.retry_interval_ms);
        } else {
            reconnect_running_ = false;
            return;
        }
    }

    if (attempt_count >= param_.max_retry_count) {
        WRITE_ERROR_LOG("Auto reconnect failed after %d attempts for Redis subscriber %s:%d",
                       param_.max_retry_count, param_.host.c_str(), param_.port);
    }

    reconnect_running_ = false;
}

// 错误处理
std::string RedisSubscriber::GetRedisError() const {
    if (redis_context_ && redis_context_->err) {
        return std::string(redis_context_->errstr);
    }
    return "Unknown Redis error";
}

void RedisSubscriber::UpdateConnectionStatus(bool connected, const std::string& error) {
    if (connection_callback_) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        try {
            connection_callback_(connected, error);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("连接回调函数异常: %s", e.what());
        }
    }
}

void RedisSubscriber::RecordMessage() {
    message_count_++;
}

void RedisSubscriber::RecordError() {
    error_count_++;
}

#endif // HAVE_HIREDIS

// RedisSubscriberFactory 实现
std::unique_ptr<RedisSubscriberInterface> RedisSubscriberFactory::Create(const RedisConnectionParam& param) {
    return std::make_unique<RedisSubscriber>(param);
}

// ControlCommandParser 实现
Result<ControlCommand> ControlCommandParser::ParseCommand(const std::string& message) {
    // 尝试解析 JSON 格式
    auto json_result = ParseJsonCommand(message);
    if (json_result.IsSuccess()) {
        return json_result;
    }

    // 尝试解析兼容格式
    auto legacy_result = ParseLegacyCommand(message);
    if (legacy_result.IsSuccess()) {
        return legacy_result;
    }

    return Result<ControlCommand>(ErrorCode::INVALID_PARAM, "Invalid command format");
}

Result<ControlCommand> ControlCommandParser::ParseJsonCommand(const std::string& json) {
    // 简单的 JSON 解析 (生产环境建议使用专业的 JSON 库)
    ControlCommand command;

    try {
        // 查找各个字段
        size_t type_pos = json.find("\"type\":");
        size_t point_pos = json.find("\"point\":");
        size_t value_pos = json.find("\"value\":");
        size_t cmd_id_pos = json.find("\"command_id\":");

        if (type_pos == std::string::npos || point_pos == std::string::npos || value_pos == std::string::npos) {
            return Result<ControlCommand>(ErrorCode::INVALID_PARAM, "Missing required fields in JSON");
        }

        // 解析 type
        size_t type_start = json.find(':', type_pos) + 1;
        size_t type_end = json.find(',', type_start);
        if (type_end == std::string::npos) type_end = json.find('}', type_start);
        std::string type_str = utils::StringUtils::Trim(json.substr(type_start, type_end - type_start));
        type_str.erase(std::remove(type_str.begin(), type_str.end(), '"'), type_str.end());
        command.type_idx.data_type = static_cast<DataType>(utils::StringUtils::ToInt(type_str, 0));

        // 解析 point
        size_t point_start = json.find(':', point_pos) + 1;
        size_t point_end = json.find(',', point_start);
        if (point_end == std::string::npos) point_end = json.find('}', point_start);
        std::string point_str = utils::StringUtils::Trim(json.substr(point_start, point_end - point_start));
        point_str.erase(std::remove(point_str.begin(), point_str.end(), '"'), point_str.end());
        command.type_idx.point_id = utils::StringUtils::ToInt(point_str, 0);

        // 解析 value
        size_t value_start = json.find(':', value_pos) + 1;
        size_t value_end = json.find(',', value_start);
        if (value_end == std::string::npos) value_end = json.find('}', value_start);
        std::string value_str = utils::StringUtils::Trim(json.substr(value_start, value_end - value_start));
        value_str.erase(std::remove(value_str.begin(), value_str.end(), '"'), value_str.end());
        command.value = utils::StringUtils::ToDouble(value_str, 0.0);

        // 解析 command_id (可选)
        if (cmd_id_pos != std::string::npos) {
            size_t cmd_id_start = json.find(':', cmd_id_pos) + 1;
            size_t cmd_id_end = json.find(',', cmd_id_start);
            if (cmd_id_end == std::string::npos) cmd_id_end = json.find('}', cmd_id_start);
            command.command_id = utils::StringUtils::Trim(json.substr(cmd_id_start, cmd_id_end - cmd_id_start));
            command.command_id.erase(std::remove(command.command_id.begin(), command.command_id.end(), '"'), command.command_id.end());
        }

        command.timestamp = utils::TimeUtils::GetCurrentTimestamp();

    } catch (const std::exception& e) {
        return Result<ControlCommand>(ErrorCode::INVALID_PARAM, "JSON parsing error: " + std::string(e.what()));
    }

    return ValidateCommand(command).IsSuccess() ?
           Result<ControlCommand>(command) :
           Result<ControlCommand>(ErrorCode::INVALID_PARAM, "Invalid command data");
}

Result<ControlCommand> ControlCommandParser::ParseLegacyCommand(const std::string& legacy) {
    // 格式: type:point=value 或 type:point=value@command_id
    ControlCommand command;

    size_t colon_pos = legacy.find(':');
    size_t equal_pos = legacy.find('=');

    if (colon_pos == std::string::npos || equal_pos == std::string::npos || colon_pos >= equal_pos) {
        return Result<ControlCommand>(ErrorCode::INVALID_PARAM, "Invalid legacy format");
    }

    try {
        // 解析 type
        std::string type_str = utils::StringUtils::Trim(legacy.substr(0, colon_pos));
        command.type_idx.data_type = static_cast<DataType>(utils::StringUtils::ToInt(type_str, 0));

        // 解析 point
        std::string point_str = utils::StringUtils::Trim(legacy.substr(colon_pos + 1, equal_pos - colon_pos - 1));
        command.type_idx.point_id = utils::StringUtils::ToInt(point_str, 0);

        // 解析 value 和可选的 command_id
        std::string value_part = utils::StringUtils::Trim(legacy.substr(equal_pos + 1));
        size_t at_pos = value_part.find('@');

        if (at_pos != std::string::npos) {
            command.value = utils::StringUtils::ToDouble(value_part.substr(0, at_pos), 0.0);
            command.command_id = utils::StringUtils::Trim(value_part.substr(at_pos + 1));
        } else {
            command.value = utils::StringUtils::ToDouble(value_part, 0.0);
        }

        command.timestamp = utils::TimeUtils::GetCurrentTimestamp();

    } catch (const std::exception& e) {
        return Result<ControlCommand>(ErrorCode::INVALID_PARAM, "Legacy parsing error: " + std::string(e.what()));
    }

    return ValidateCommand(command).IsSuccess() ?
           Result<ControlCommand>(command) :
           Result<ControlCommand>(ErrorCode::INVALID_PARAM, "Invalid command data");
}

Result<bool> ControlCommandParser::ValidateCommand(const ControlCommand& command) {
    if (!IsValidDataType(static_cast<int>(command.type_idx.data_type))) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid data type");
    }

    if (!IsValidPointId(command.type_idx.point_id)) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid point ID");
    }

    if (!IsValidValue(command.value, command.type_idx.data_type)) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid value");
    }

    return Result<bool>(true);
}

bool ControlCommandParser::IsValidDataType(int data_type) {
    return data_type >= 1 && data_type <= 4;
}

bool ControlCommandParser::IsValidPointId(int point_id) {
    return point_id >= 0 && point_id <= 99999;
}

bool ControlCommandParser::IsValidValue(double value, DataType data_type) {
    switch (data_type) {
        case DataType::YK:  // 遥控，通常是 0 或 1
            return value >= 0.0 && value <= 1.0;
        case DataType::YT:  // 遥调，范围较大
            return value >= -999999.0 && value <= 999999.0;
        default:
            return false;  // YC 和 YX 不应该用于控制
    }
}

// ControlCommandProcessor 实现
ControlCommandProcessor::ControlCommandProcessor() {
}

void ControlCommandProcessor::SetCommandHandler(CommandHandler handler) {
    command_handler_ = handler;
}

Result<bool> ControlCommandProcessor::ProcessCommand(const ControlCommand& command) {
    if (!command_handler_) {
        return Result<bool>(ErrorCode::NOT_INITIALIZED, "Command handler not set");
    }

    processed_count_++;

    try {
        auto result = command_handler_(command);

        if (result.IsSuccess()) {
            success_count_++;
        } else {
            error_count_++;
        }

        return result;

    } catch (const std::exception& e) {
        error_count_++;
        return Result<bool>(ErrorCode::UNKNOWN_ERROR, "Command handler exception: " + std::string(e.what()));
    }
}

Result<std::vector<bool>> ControlCommandProcessor::ProcessCommands(const std::vector<ControlCommand>& commands) {
    std::vector<bool> results;
    results.reserve(commands.size());

    for (const auto& command : commands) {
        auto result = ProcessCommand(command);
        results.push_back(result.IsSuccess());
    }

    return Result<std::vector<bool>>(results);
}

void ControlCommandProcessor::EnableCommandQueue(bool enable) {
    queue_enabled_ = enable;
}

int ControlCommandProcessor::GetQueueSize() const {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    return command_queue_.size();
}

void ControlCommandProcessor::ClearQueue() {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    while (!command_queue_.empty()) {
        command_queue_.pop();
    }
}

void ControlCommandProcessor::GetStats(int& processed_count, int& success_count, int& error_count) const {
    processed_count = processed_count_.load();
    success_count = success_count_.load();
    error_count = error_count_.load();
}

void ControlCommandProcessor::ResetStats() {
    processed_count_ = 0;
    success_count_ = 0;
    error_count_ = 0;
}

} // namespace modbus
