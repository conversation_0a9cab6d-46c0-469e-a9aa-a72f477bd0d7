#include "redis_publisher.h"
#include "redis_message_handler.h"
#include "../utils/utils.h"
#include <sstream>
#include <algorithm>
#include <cstring>

namespace modbus {

#ifdef HAVE_HIREDIS

// RedisPublisher 实现
RedisPublisher::RedisPublisher(const RedisConnectionParam& param)
    : param_(param)
    , redis_context_(nullptr)
    , stop_event_(true) {  // manual reset event
}

RedisPublisher::~RedisPublisher() {
    StopAsyncWorker();
    StopAutoReconnect();
    Disconnect();
}

Result<bool> RedisPublisher::Connect() {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (is_connected_) {
        return Result<bool>(true);
    }
    
    auto result = CreateConnection();
    if (!result.IsSuccess()) {
        UpdateConnectionStatus(false, result.error_message);
        return result;
    }
    
    // 认证
    if (param_.enable_auth && !param_.password.empty()) {
        auto auth_result = Authenticate();
        if (!auth_result.IsSuccess()) {
            DestroyConnection();
            UpdateConnectionStatus(false, auth_result.error_message);
            return auth_result;
        }
    }
    
    // 选择数据库
    if (param_.database != 0) {
        auto db_result = SelectDatabase();
        if (!db_result.IsSuccess()) {
            DestroyConnection();
            UpdateConnectionStatus(false, db_result.error_message);
            return db_result;
        }
    }
    
    // 测试连接
    auto test_result = TestConnection();
    if (!test_result.IsSuccess()) {
        DestroyConnection();
        UpdateConnectionStatus(false, test_result.error_message);
        return test_result;
    }
    
    is_connected_ = true;
    UpdateConnectionStatus(true);
    
    // 启动异步工作线程
    if (async_mode_enabled_) {
        StartAsyncWorker();
    }
    
    WRITE_INFO_LOG("Redis 发布器已连接到 %s:%d", param_.host.c_str(), param_.port);
    
    return Result<bool>(true);
}

void RedisPublisher::Disconnect() {
    StopAsyncWorker();
    
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (is_connected_) {
        is_connected_ = false;
        UpdateConnectionStatus(false);
        WRITE_INFO_LOG("Redis 发布器已断开连接 %s:%d", param_.host.c_str(), param_.port);
    }
    
    DestroyConnection();
}

bool RedisPublisher::IsConnected() const {
    return is_connected_ && redis_context_ != nullptr;
}

Result<bool> RedisPublisher::Reconnect() {
    WRITE_INFO_LOG("正在尝试重连 Redis 发布器到 %s:%d", param_.host.c_str(), param_.port);
    
    Disconnect();
    utils::TimeUtils::SleepMs(param_.retry_interval_ms);
    
    return Connect();
}

Result<PublishResult> RedisPublisher::Publish(const std::string& channel, const std::string& message) {
    if (!IsConnected()) {
        if (auto_reconnect_enabled_) {
            auto reconnect_result = Reconnect();
            if (!reconnect_result.IsSuccess()) {
                RecordError();
                return Result<PublishResult>(ErrorCode::COMM_ERROR, "Not connected and reconnect failed");
            }
        } else {
            RecordError();
            return Result<PublishResult>(ErrorCode::COMM_ERROR, "Not connected");
        }
    }
    
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    redisReply* reply = static_cast<redisReply*>(redisCommand(redis_context_, "PUBLISH %s %s", 
                                                             channel.c_str(), message.c_str()));
    
    if (!reply) {
        std::string error = GetRedisError();
        RecordError();
        is_connected_ = false;
        UpdateConnectionStatus(false, error);
        
        if (auto_reconnect_enabled_) {
            StartAutoReconnect();
        }
        
        return Result<PublishResult>(ErrorCode::COMM_ERROR, error);
    }
    
    PublishResult result;
    result.success = true;
    result.subscriber_count = reply->integer;
    result.publish_time = utils::TimeUtils::GetCurrentTimestamp();
    
    freeReplyObject(reply);
    RecordSuccess();
    
    // 触发发布状态回调
    if (publish_callback_) {
        PublishMessage msg(channel, message);
        msg.timestamp = result.publish_time;
        
        std::lock_guard<std::mutex> cb_lock(callback_mutex_);
        try {
            publish_callback_(msg, result);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("发布回调函数异常: %s", e.what());
        }
    }
    
    return Result<PublishResult>(result);
}

Result<PublishResult> RedisPublisher::PublishAsync(const std::string& channel, const std::string& message) {
    if (!async_mode_enabled_) {
        return Publish(channel, message);
    }
    
    PublishMessage msg(channel, message);
    msg.timestamp = utils::TimeUtils::GetCurrentTimestamp();
    
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        
        if (static_cast<int>(message_queue_.size()) >= max_queue_size_) {
            RecordError();
            return Result<PublishResult>(ErrorCode::QUEUE_FULL, "Message queue is full");
        }
        
        message_queue_.push(msg);
        pending_count_++;
    }
    
    queue_condition_.notify_one();
    
    PublishResult result;
    result.success = true;
    result.publish_time = msg.timestamp;
    
    return Result<PublishResult>(result);
}

Result<std::vector<PublishResult>> RedisPublisher::PublishBatch(const std::vector<PublishMessage>& messages) {
    std::vector<PublishResult> results;
    results.reserve(messages.size());
    
    for (const auto& msg : messages) {
        auto result = Publish(msg.channel, msg.message);
        if (result.IsSuccess()) {
            results.push_back(result.data);
        } else {
            PublishResult failed_result;
            failed_result.success = false;
            failed_result.error_message = result.error_message;
            results.push_back(failed_result);
        }
    }
    
    return Result<std::vector<PublishResult>>(results);
}

Result<PublishResult> RedisPublisher::PublishBinary(const std::string& channel, const void* data, size_t size) {
    if (!IsConnected()) {
        if (auto_reconnect_enabled_) {
            auto reconnect_result = Reconnect();
            if (!reconnect_result.IsSuccess()) {
                RecordError();
                return Result<PublishResult>(ErrorCode::COMM_ERROR, "Not connected and reconnect failed");
            }
        } else {
            RecordError();
            return Result<PublishResult>(ErrorCode::COMM_ERROR, "Not connected");
        }
    }

    std::lock_guard<std::mutex> lock(connection_mutex_);

    redisReply* reply = static_cast<redisReply*>(redisCommand(redis_context_, "PUBLISH %s %b",
                                                             channel.c_str(), data, size));

    if (!reply) {
        std::string error = GetRedisError();
        RecordError();
        is_connected_ = false;
        UpdateConnectionStatus(false, error);

        if (auto_reconnect_enabled_) {
            StartAutoReconnect();
        }

        return Result<PublishResult>(ErrorCode::COMM_ERROR, error);
    }

    PublishResult result;
    result.success = true;
    result.subscriber_count = reply->integer;
    result.publish_time = utils::TimeUtils::GetCurrentTimestamp();

    freeReplyObject(reply);
    RecordSuccess();

    return Result<PublishResult>(result);
}

Result<PublishResult> RedisPublisher::PublishBinaryAsync(const std::string& channel, const void* data, size_t size) {
    if (!async_mode_enabled_) {
        return PublishBinary(channel, data, size);
    }

    // 创建二进制消息的副本
    std::string binary_data(static_cast<const char*>(data), size);
    PublishMessage msg(channel, binary_data);
    msg.timestamp = utils::TimeUtils::GetCurrentTimestamp();
    msg.is_binary = true;

    {
        std::lock_guard<std::mutex> lock(queue_mutex_);

        if (static_cast<int>(message_queue_.size()) >= max_queue_size_) {
            RecordError();
            return Result<PublishResult>(ErrorCode::QUEUE_FULL, "Message queue is full");
        }

        message_queue_.push(msg);
        pending_count_++;
    }

    queue_condition_.notify_one();

    PublishResult result;
    result.success = true;
    result.publish_time = msg.timestamp;

    return Result<PublishResult>(result);
}

void RedisPublisher::SetConnectionParam(const RedisConnectionParam& param) {
    param_ = param;
    
    // 如果已连接，需要重新连接
    if (is_connected_) {
        Reconnect();
    }
}

RedisConnectionParam RedisPublisher::GetConnectionParam() const {
    return param_;
}

void RedisPublisher::GetStats(int& success_count, int& error_count, int& pending_count) const {
    success_count = success_count_.load();
    error_count = error_count_.load();
    pending_count = pending_count_.load();
}

void RedisPublisher::ResetStats() {
    success_count_ = 0;
    error_count_ = 0;
    pending_count_ = 0;
}

void RedisPublisher::EnableAsyncMode(bool enable) {
    if (async_mode_enabled_ == enable) {
        return;
    }
    
    async_mode_enabled_ = enable;
    
    if (enable && is_connected_) {
        StartAsyncWorker();
    } else if (!enable) {
        StopAsyncWorker();
    }
}

void RedisPublisher::SetConnectionStatusCallback(ConnectionStatusCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    connection_callback_ = callback;
}

void RedisPublisher::SetPublishStatusCallback(PublishStatusCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    publish_callback_ = callback;
}

// 内部方法实现
Result<bool> RedisPublisher::CreateConnection() {
    struct timeval timeout = { param_.timeout_ms / 1000, (param_.timeout_ms % 1000) * 1000 };
    
    redis_context_ = redisConnectWithTimeout(param_.host.c_str(), param_.port, timeout);
    
    if (!redis_context_ || redis_context_->err) {
        std::string error = redis_context_ ? redis_context_->errstr : "Failed to allocate redis context";
        DestroyConnection();
        return Result<bool>(ErrorCode::COMM_ERROR, error);
    }
    
    return Result<bool>(true);
}

void RedisPublisher::DestroyConnection() {
    if (redis_context_) {
        redisFree(redis_context_);
        redis_context_ = nullptr;
    }
}

Result<bool> RedisPublisher::Authenticate() {
    redisReply* reply = static_cast<redisReply*>(redisCommand(redis_context_, "AUTH %s", param_.password.c_str()));
    
    if (!reply) {
        return Result<bool>(ErrorCode::COMM_ERROR, GetRedisError());
    }
    
    bool success = (reply->type == REDIS_REPLY_STATUS && strcmp(reply->str, "OK") == 0);
    std::string error = success ? "" : (reply->str ? reply->str : "Authentication failed");
    
    freeReplyObject(reply);
    
    return success ? Result<bool>(true) : Result<bool>(ErrorCode::AUTH_ERROR, error);
}

Result<bool> RedisPublisher::SelectDatabase() {
    redisReply* reply = static_cast<redisReply*>(redisCommand(redis_context_, "SELECT %d", param_.database));
    
    if (!reply) {
        return Result<bool>(ErrorCode::COMM_ERROR, GetRedisError());
    }
    
    bool success = (reply->type == REDIS_REPLY_STATUS && strcmp(reply->str, "OK") == 0);
    std::string error = success ? "" : (reply->str ? reply->str : "Database selection failed");
    
    freeReplyObject(reply);
    
    return success ? Result<bool>(true) : Result<bool>(ErrorCode::CONFIG_ERROR, error);
}

Result<bool> RedisPublisher::TestConnection() {
    redisReply* reply = static_cast<redisReply*>(redisCommand(redis_context_, "PING"));
    
    if (!reply) {
        return Result<bool>(ErrorCode::COMM_ERROR, GetRedisError());
    }
    
    bool success = (reply->type == REDIS_REPLY_STATUS && strcmp(reply->str, "PONG") == 0);
    std::string error = success ? "" : "PING test failed";
    
    freeReplyObject(reply);
    
    return success ? Result<bool>(true) : Result<bool>(ErrorCode::COMM_ERROR, error);
}

// 异步处理
void RedisPublisher::StartAsyncWorker() {
    if (worker_running_) {
        return;
    }

    worker_running_ = true;
    stop_event_.Reset();
    async_worker_ = std::make_unique<std::thread>(&RedisPublisher::AsyncWorkerThread, this);
}

void RedisPublisher::StopAsyncWorker() {
    if (worker_running_) {
        worker_running_ = false;
        stop_event_.Set();
        queue_condition_.notify_all();

        if (async_worker_ && async_worker_->joinable()) {
            async_worker_->join();
        }
        async_worker_.reset();
    }
}

void RedisPublisher::AsyncWorkerThread() {
    while (worker_running_) {
        std::unique_lock<std::mutex> lock(queue_mutex_);

        // 等待消息或停止信号
        queue_condition_.wait(lock, [this] {
            return !worker_running_ || !message_queue_.empty();
        });

        if (!worker_running_) {
            break;
        }

        // 处理队列中的消息
        while (!message_queue_.empty() && worker_running_) {
            PublishMessage msg = message_queue_.front();
            message_queue_.pop();
            pending_count_--;

            lock.unlock();
            ProcessMessage(msg);
            lock.lock();
        }
    }
}

void RedisPublisher::ProcessMessage(const PublishMessage& msg) {
    Result<PublishResult> result;

    if (msg.is_binary) {
        // 二进制数据发布
        result = PublishBinary(msg.channel, msg.message.data(), msg.message.size());
    } else {
        // 文本数据发布
        result = Publish(msg.channel, msg.message);
    }

    // 触发发布状态回调
    if (publish_callback_) {
        std::lock_guard<std::mutex> cb_lock(callback_mutex_);
        try {
            PublishResult pub_result;
            if (result.IsSuccess()) {
                pub_result = result.data;
            } else {
                pub_result.success = false;
                pub_result.error_message = result.error_message;
            }
            publish_callback_(msg, pub_result);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("异步发布回调函数异常: %s", e.what());
        }
    }
}

// 自动重连
void RedisPublisher::StartAutoReconnect() {
    if (reconnect_running_) {
        return;
    }

    reconnect_running_ = true;
    reconnect_thread_ = std::make_unique<std::thread>(&RedisPublisher::AutoReconnectThread, this);
}

void RedisPublisher::StopAutoReconnect() {
    if (reconnect_running_) {
        reconnect_running_ = false;

        if (reconnect_thread_ && reconnect_thread_->joinable()) {
            reconnect_thread_->join();
        }
        reconnect_thread_.reset();
    }
}

void RedisPublisher::AutoReconnectThread() {
    int attempt_count = 0;

    while (reconnect_running_ && attempt_count < param_.max_retry_count) {
        if (!IsConnected()) {
            attempt_count++;

            WRITE_INFO_LOG("Auto reconnect attempt %d/%d for Redis %s:%d",
                          attempt_count, param_.max_retry_count,
                          param_.host.c_str(), param_.port);

            auto result = Reconnect();
            if (result.IsSuccess()) {
                reconnect_running_ = false;
                return;
            }

            // 等待重连间隔
            utils::TimeUtils::SleepMs(param_.retry_interval_ms);
        } else {
            reconnect_running_ = false;
            return;
        }
    }

    if (attempt_count >= param_.max_retry_count) {
        WRITE_ERROR_LOG("Auto reconnect failed after %d attempts for Redis %s:%d",
                       param_.max_retry_count, param_.host.c_str(), param_.port);
    }

    reconnect_running_ = false;
}

// 错误处理
std::string RedisPublisher::GetRedisError() const {
    if (redis_context_ && redis_context_->err) {
        return std::string(redis_context_->errstr);
    }
    return "Unknown Redis error";
}

void RedisPublisher::UpdateConnectionStatus(bool connected, const std::string& error) {
    if (connection_callback_) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        try {
            connection_callback_(connected, error);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("连接回调函数异常: %s", e.what());
        }
    }
}

void RedisPublisher::RecordSuccess() {
    success_count_++;
}

void RedisPublisher::RecordError() {
    error_count_++;
}

Result<bool> RedisPublisher::ExecuteCommand(const std::string& command) {
    if (!IsConnected()) {
        if (auto_reconnect_enabled_) {
            auto reconnect_result = Reconnect();
            if (!reconnect_result.IsSuccess()) {
                RecordError();
                return Result<bool>(ErrorCode::COMM_ERROR, "Not connected and reconnect failed");
            }
        } else {
            RecordError();
            return Result<bool>(ErrorCode::COMM_ERROR, "Not connected");
        }
    }

    std::lock_guard<std::mutex> lock(connection_mutex_);

    // 直接执行Redis命令，不使用%s格式化
    redisReply* reply = static_cast<redisReply*>(redisCommand(redis_context_, command.c_str()));

    if (!reply) {
        std::string error = GetRedisError();
        RecordError();
        is_connected_ = false;
        UpdateConnectionStatus(false, error);

        if (auto_reconnect_enabled_) {
            StartAutoReconnect();
        }

        return Result<bool>(ErrorCode::COMM_ERROR, error);
    }

    bool success = false;
    std::string debug_info;

    // 添加详细的调试信息
    switch (reply->type) {
        case REDIS_REPLY_STATUS:
            debug_info = "STATUS: " + std::string(reply->str ? reply->str : "NULL");
            if (reply->str && strcmp(reply->str, "OK") == 0) {
                success = true;
            }
            break;
        case REDIS_REPLY_INTEGER:
            debug_info = "INTEGER: " + std::to_string(reply->integer);
            if (reply->integer >= 0) {
                success = true;
            }
            break;
        case REDIS_REPLY_ERROR:
            debug_info = "ERROR: " + std::string(reply->str ? reply->str : "NULL");
            break;
        case REDIS_REPLY_NIL:
            debug_info = "NIL";
            break;
        case REDIS_REPLY_STRING:
            debug_info = "STRING: " + std::string(reply->str ? reply->str : "NULL");
            break;
        case REDIS_REPLY_ARRAY:
            debug_info = "ARRAY: " + std::to_string(reply->elements) + " elements";
            break;
        default:
            debug_info = "UNKNOWN: " + std::to_string(reply->type);
            break;
    }

    freeReplyObject(reply);

    if (success) {
        RecordSuccess();
        return Result<bool>(true);
    } else {
        RecordError();
        return Result<bool>(ErrorCode::COMM_ERROR, "Redis command failed - " + debug_info);
    }
}

// xj_svg 兼容的发布方法实现 - 严格按照 xj_svg 接口定义
bool RedisPublisher::sendYTOrder(const int deviceid, const int dataid, double val, unsigned char status) {
    REDIS_YT ytdata;
    ytdata.deviceId = deviceid;
    ytdata.dataId = dataid;
    ytdata.val = val;
    ytdata.status = status;

    auto result = PublishBinary(EVENT_RC_SCADA_YT, &ytdata, sizeof(REDIS_YT));
    return result.IsSuccess();
}

bool RedisPublisher::sendYKOrder(const int deviceid, const int dataid, unsigned short val, unsigned char status) {
    REDIS_YK ykdata;
    ykdata.deviceId = deviceid;
    ykdata.dataId = dataid;
    ykdata.val = val;
    ykdata.status = status;

    auto result = PublishBinary(EVENT_RC_SCADA_YK, &ykdata, sizeof(REDIS_YK));
    return result.IsSuccess();
}

int RedisPublisher::setAgvcYCValue(int rtuid, int dataid, double realVal) {
    // xj_svg 项目中此方法调用 RDBI->SetYCRealValue()
    // 这里我们使用现有的 Redis 发布机制来模拟相同的行为
    REDIS_YC ycdata;
    ycdata.deviceId = rtuid;
    ycdata.dataId = dataid;
    ycdata.val = realVal;
    ycdata.status = 0; // 默认状态为正常

    // 发布到遥测数据频道
    std::string channel = ChannelNameManager::GetDataChannel(rtuid, DataType::YC);
    auto result = PublishBinary(channel, &ycdata, sizeof(REDIS_YC));
    return result.IsSuccess() ? 0 : -1;
}

int RedisPublisher::setAgvcYCValue(int rtuid, int dataid, double realVal, double calcVal) {
    // 使用与xj_svg项目完全一致的RDBI方式写入二进制格式
    // 格式: HMSET yc:%d:%d realValue %b calValue %b status %d updateTime %b

    if (!IsConnected()) {
        if (auto_reconnect_enabled_) {
            auto reconnect_result = Reconnect();
            if (!reconnect_result.IsSuccess()) {
                return -1;
            }
        } else {
            return -1;
        }
    }

    std::lock_guard<std::mutex> lock(connection_mutex_);

    // 获取当前时间戳
    uint64_t now = utils::TimeUtils::GetCurrentTimestamp();
    uint8_t status = 1; // 状态1表示正常(DATA_STATUS_VALID)，与xj_svg项目一致

    // 使用与RDBI完全一致的二进制格式命令
    redisReply* reply = static_cast<redisReply*>(
        redisCommand(redis_context_, "HMSET yc:%d:%d realValue %b calValue %b status %d updateTime %b",
                     rtuid, dataid,
                     &realVal, sizeof(double),
                     &calcVal, sizeof(double),
                     status,
                     &now, sizeof(uint64_t))
    );

    if (!reply) {
        return -1;
    }

    bool success = (reply->type == REDIS_REPLY_STATUS && strcmp(reply->str, "OK") == 0);
    freeReplyObject(reply);

    return success ? 0 : -1;
}

int RedisPublisher::setAgvcYXValue(int rtuid, int dataid, short realVal) {
    // 使用与xj_svg项目完全一致的RDBI方式写入二进制格式
    // 格式: HMSET yx:%d:%d realValue %b status %d updateTime %b

    if (!IsConnected()) {
        if (auto_reconnect_enabled_) {
            auto reconnect_result = Reconnect();
            if (!reconnect_result.IsSuccess()) {
                return -1;
            }
        } else {
            return -1;
        }
    }

    std::lock_guard<std::mutex> lock(connection_mutex_);

    // 获取当前时间戳
    uint64_t now = utils::TimeUtils::GetCurrentTimestamp();
    uint8_t status = 1; // 状态1表示正常(DATA_STATUS_VALID)，与xj_svg项目一致

    // 使用与RDBI完全一致的二进制格式命令
    redisReply* reply = static_cast<redisReply*>(
        redisCommand(redis_context_, "HMSET yx:%d:%d realValue %b status %d updateTime %b",
                     rtuid, dataid,
                     &realVal, sizeof(short),
                     status,
                     &now, sizeof(uint64_t))
    );

    if (!reply) {
        return -1;
    }

    bool success = (reply->type == REDIS_REPLY_STATUS && strcmp(reply->str, "OK") == 0);
    freeReplyObject(reply);

    return success ? 0 : -1;
}

#endif // HAVE_HIREDIS

// RedisPublisherFactory 实现
std::unique_ptr<RedisPublisherInterface> RedisPublisherFactory::Create(const RedisConnectionParam& param) {
    return std::make_unique<RedisPublisher>(param);
}

bool RedisPublisherFactory::IsRedisSupported() {
#ifdef HAVE_HIREDIS
    return true;
#else
    return false;
#endif
}

// ModbusDataFormatter 实现
std::string ModbusDataFormatter::FormatDataPoint(const modbus::TypeIndex& type_idx, double value, uint64_t timestamp, int quality) {
    std::map<std::string, std::string> data;
    data["type"] = std::to_string(static_cast<int>(type_idx.data_type));
    data["point"] = std::to_string(type_idx.point_id);
    data["value"] = utils::StringUtils::ToString(value, 6);
    data["timestamp"] = std::to_string(timestamp);
    data["quality"] = std::to_string(quality);

    return ToJson(data);
}

std::string ModbusDataFormatter::FormatDataPoints(const std::map<TypeIndex, double>& values, uint64_t timestamp) {
    std::ostringstream oss;
    oss << "{\"timestamp\":" << timestamp << ",\"points\":[";

    bool first = true;
    for (const auto& pair : values) {
        if (!first) oss << ",";
        first = false;

        oss << "{\"type\":" << static_cast<int>(pair.first.data_type)
            << ",\"point\":" << pair.first.point_id
            << ",\"value\":" << pair.second << "}";
    }

    oss << "]}";
    return oss.str();
}

std::string ModbusDataFormatter::FormatDeviceStatus(int device_id, DeviceStatus status, const std::string& message) {
    std::map<std::string, std::string> data;
    data["device_id"] = std::to_string(device_id);
    data["status"] = std::to_string(static_cast<int>(status));
    data["message"] = message;
    data["timestamp"] = std::to_string(utils::TimeUtils::GetCurrentTimestamp());

    return ToJson(data);
}

std::string ModbusDataFormatter::FormatAlarm(const TypeIndex& type_idx, const std::string& alarm_type,
                                            double value, const std::string& description) {
    std::map<std::string, std::string> data;
    data["type"] = std::to_string(static_cast<int>(type_idx.data_type));
    data["point"] = std::to_string(type_idx.point_id);
    data["alarm_type"] = alarm_type;
    data["value"] = utils::StringUtils::ToString(value, 6);
    data["description"] = description;
    data["timestamp"] = std::to_string(utils::TimeUtils::GetCurrentTimestamp());

    return ToJson(data);
}

std::string ModbusDataFormatter::ToJson(const std::map<std::string, std::string>& data) {
    std::ostringstream oss;
    oss << "{";

    bool first = true;
    for (const auto& pair : data) {
        if (!first) oss << ",";
        first = false;
        oss << "\"" << pair.first << "\":\"" << pair.second << "\"";
    }

    oss << "}";
    return oss.str();
}

std::string ModbusDataFormatter::FormatLegacyData(int data_type, int point_id, double value, uint64_t timestamp) {
    // 兼容现有系统的格式：data_type:point_id=value@timestamp
    return utils::StringUtils::Format("%d:%05d=%.6f@%llu", data_type, point_id, value, timestamp);
}

// ChannelNameManager 实现
std::string ChannelNameManager::GetDataChannel(int device_id) {
    return utils::StringUtils::Format("modbus:device:%d:data", device_id);
}

std::string ChannelNameManager::GetDataChannel(int device_id, DataType data_type) {
    const char* type_name = "";
    switch (data_type) {
        case DataType::YC: type_name = "yc"; break;
        case DataType::YX: type_name = "yx"; break;
        case DataType::YT: type_name = "yt"; break;
        case DataType::YK: type_name = "yk"; break;
    }
    return utils::StringUtils::Format("modbus:device:%d:data:%s", device_id, type_name);
}

std::string ChannelNameManager::GetStatusChannel(int device_id) {
    return utils::StringUtils::Format("modbus:device:%d:status", device_id);
}

std::string ChannelNameManager::GetAlarmChannel(int device_id) {
    return utils::StringUtils::Format("modbus:device:%d:alarm", device_id);
}

std::string ChannelNameManager::GetControlChannel(int device_id) {
    return utils::StringUtils::Format("modbus:device:%d:control", device_id);
}

std::string ChannelNameManager::GetLegacyChannel(const std::string& prefix, int device_id) {
    return utils::StringUtils::Format("%s:%d", prefix.c_str(), device_id);
}

std::string ChannelNameManager::BuildChannel(const std::vector<std::string>& parts) {
    std::ostringstream oss;
    for (size_t i = 0; i < parts.size(); ++i) {
        if (i > 0) oss << ":";
        oss << parts[i];
    }
    return oss.str();
}

} // namespace modbus

