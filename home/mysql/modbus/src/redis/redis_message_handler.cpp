#include "redis_message_handler.h"
#include "../utils/utils.h"
#include <sstream>
#include <algorithm>
#include <iostream>

namespace modbus {

// RedisMessageHandler 实现
RedisMessageHandler::RedisMessageHandler()
    : stop_event_(true) {  // manual reset event
}

RedisMessageHandler::~RedisMessageHandler() {
    Shutdown();
}

Result<bool> RedisMessageHandler::Initialize() {
    WRITE_INFO_LOG("Redis message handler initialized");
    return Result<bool>(true);
}

void RedisMessageHandler::Shutdown() {
    Stop();
    ClearQueue();
    ClearMessageFilters();
    
    WRITE_INFO_LOG("Redis message handler shutdown");
}

Result<bool> RedisMessageHandler::Start() {
    if (is_running_) {
        return Result<bool>(true);
    }
    
    is_running_ = true;
    stop_event_.Reset();
    
    if (queue_enabled_) {
        StartQueueProcessor();
    }
    
    WRITE_INFO_LOG("Redis message handler started");
    return Result<bool>(true);
}

void RedisMessageHandler::Stop() {
    if (!is_running_) {
        return;
    }
    
    is_running_ = false;
    stop_event_.Set();
    
    StopQueueProcessor();
    
    WRITE_INFO_LOG("Redis message handler stopped");
}

MessageProcessResult RedisMessageHandler::ProcessMessage(const SubscribeMessage& message) {
    MessageProcessResult result;

    try {
        // 检查是否为二进制消息，优先处理 xj_svg 兼容的二进制消息
        if (message.is_binary && (message.channel == EVENT_RC_SCADA_YK || message.channel == EVENT_RC_SCADA_YT)) {
            return ProcessBinaryMessage(message);
        }

        // 确定消息类型
        MessageType type = DetermineMessageType(message.channel, message.message);

        // 解析消息
        Result<ProcessedMessage> parse_result;

        if (legacy_format_enabled_ && LegacyMessageHandler::IsLegacyFormat(message.message)) {
            parse_result = ParseLegacyMessage(message.message);
        } else {
            parse_result = ParseJsonMessage(message.message);
        }

        if (!parse_result.IsSuccess()) {
            result.error_message = parse_result.error_message;
            UpdateStatistics(ProcessedMessage(), false);
            return result;
        }

        ProcessedMessage processed = parse_result.data;
        processed.type = type;
        processed.raw_message = message.message;
        processed.channel = message.channel;
        processed.timestamp = message.timestamp;

        // 从通道中提取设备ID
        processed.device_id = ExtractDeviceId(message.channel);

        // 验证消息
        auto validate_result = ValidateMessage(processed);
        if (!validate_result.IsSuccess()) {
            result.error_message = validate_result.error_message;
            UpdateStatistics(processed, false);
            return result;
        }

        // 应用消息过滤器
        if (!ApplyMessageFilters(processed)) {
            result.error_message = "Message filtered out";
            UpdateStatistics(processed, false);
            return result;
        }
        
        // 触发回调
        TriggerCallbacks(processed);
        
        result.success = true;
        result.processed_msg = processed;
        UpdateStatistics(processed, true);
        
    } catch (const std::exception& e) {
        result.error_message = "Exception in message processing: " + std::string(e.what());
        UpdateStatistics(ProcessedMessage(), false);
    }
    
    return result;
}

MessageProcessResult RedisMessageHandler::ProcessLegacyMessage(const std::string& channel, const std::string& message) {
    SubscribeMessage sub_msg(channel, message);
    sub_msg.timestamp = utils::TimeUtils::GetCurrentTimestamp();
    
    return ProcessMessage(sub_msg);
}

std::vector<MessageProcessResult> RedisMessageHandler::ProcessMessages(const std::vector<SubscribeMessage>& messages) {
    std::vector<MessageProcessResult> results;
    results.reserve(messages.size());
    
    for (const auto& message : messages) {
        results.push_back(ProcessMessage(message));
    }
    
    return results;
}

std::string RedisMessageHandler::FormatDataMessage(int device_id, const TypeIndex& type_idx, double value, uint64_t timestamp) {
    if (timestamp == 0) {
        timestamp = utils::TimeUtils::GetCurrentTimestamp();
    }
    
    std::ostringstream oss;
    oss << "{";
    oss << "\"device_id\":" << device_id << ",";
    oss << "\"type\":" << static_cast<int>(type_idx.data_type) << ",";
    oss << "\"point\":" << type_idx.point_id << ",";
    oss << "\"value\":" << value << ",";
    oss << "\"timestamp\":" << timestamp;
    oss << "}";
    
    return oss.str();
}

std::string RedisMessageHandler::FormatStatusMessage(int device_id, DeviceStatus status, const std::string& description) {
    uint64_t timestamp = utils::TimeUtils::GetCurrentTimestamp();
    
    std::ostringstream oss;
    oss << "{";
    oss << "\"device_id\":" << device_id << ",";
    oss << "\"status\":" << static_cast<int>(status) << ",";
    oss << "\"description\":\"" << description << "\",";
    oss << "\"timestamp\":" << timestamp;
    oss << "}";
    
    return oss.str();
}

std::string RedisMessageHandler::FormatAlarmMessage(int device_id, const TypeIndex& type_idx, const std::string& alarm_type, double value, const std::string& description) {
    uint64_t timestamp = utils::TimeUtils::GetCurrentTimestamp();
    
    std::ostringstream oss;
    oss << "{";
    oss << "\"device_id\":" << device_id << ",";
    oss << "\"type\":" << static_cast<int>(type_idx.data_type) << ",";
    oss << "\"point\":" << type_idx.point_id << ",";
    oss << "\"alarm_type\":\"" << alarm_type << "\",";
    oss << "\"value\":" << value << ",";
    oss << "\"description\":\"" << description << "\",";
    oss << "\"timestamp\":" << timestamp;
    oss << "}";
    
    return oss.str();
}

Result<bool> RedisMessageHandler::ValidateMessage(const ProcessedMessage& message) {
    if (message.device_id <= 0) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid device ID");
    }
    
    if (message.type == MessageType::UNKNOWN) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Unknown message type");
    }
    
    switch (message.type) {
        case MessageType::YT_COMMAND:
        case MessageType::YK_COMMAND:
            return ValidateControlCommand(message);
            
        case MessageType::YC_DATA:
        case MessageType::YX_DATA:
            return ValidateDataMessage(message);

        case MessageType::DEVICE_STATUS:
        case MessageType::ALARM:
        case MessageType::UNKNOWN:
        default:
            return Result<bool>(true);  // 其他类型暂时通过
    }
}

Result<bool> RedisMessageHandler::ValidateControlCommand(const ProcessedMessage& message) {
    if (message.type_idx.data_type != DataType::YT && message.type_idx.data_type != DataType::YK) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid data type for control command");
    }
    
    if (message.type_idx.point_id < 0 || message.type_idx.point_id > 99999) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid point ID");
    }
    
    // YK 命令值应该是 0 或 1
    if (message.type_idx.data_type == DataType::YK) {
        if (message.value != 0.0 && message.value != 1.0) {
            return Result<bool>(ErrorCode::INVALID_PARAM, "YK command value must be 0 or 1");
        }
    }
    
    return Result<bool>(true);
}

Result<bool> RedisMessageHandler::ValidateDataMessage(const ProcessedMessage& message) {
    if (message.type_idx.data_type != DataType::YC && message.type_idx.data_type != DataType::YX) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid data type for data message");
    }
    
    if (message.type_idx.point_id < 0 || message.type_idx.point_id > 99999) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid point ID");
    }
    
    return Result<bool>(true);
}

// 移除了不必要的现代 C++ 回调函数设置方法，只保留 xj_svg 兼容的接口

void RedisMessageHandler::EnableMessageQueue(bool enable) {
    if (queue_enabled_ == enable) {
        return;
    }
    
    queue_enabled_ = enable;
    
    if (enable && is_running_) {
        StartQueueProcessor();
    } else if (!enable) {
        StopQueueProcessor();
    }
}

int RedisMessageHandler::GetQueueSize() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(queue_mutex_));
    return message_queue_.size();
}

void RedisMessageHandler::ClearQueue() {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    while (!message_queue_.empty()) {
        message_queue_.pop();
    }
}

RedisMessageHandler::Statistics RedisMessageHandler::GetStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

void RedisMessageHandler::ResetStatistics() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_ = Statistics();
}

void RedisMessageHandler::AddMessageFilter(const std::string& name, MessageFilter filter) {
    std::lock_guard<std::mutex> lock(filter_mutex_);
    message_filters_[name] = filter;
}

void RedisMessageHandler::RemoveMessageFilter(const std::string& name) {
    std::lock_guard<std::mutex> lock(filter_mutex_);
    message_filters_.erase(name);
}

void RedisMessageHandler::ClearMessageFilters() {
    std::lock_guard<std::mutex> lock(filter_mutex_);
    message_filters_.clear();
}

// 内部方法实现
MessageType RedisMessageHandler::DetermineMessageType(const std::string& channel, const std::string& message) {
    // 从通道名称判断
    if (channel.find(":data:") != std::string::npos) {
        if (channel.find(":yc") != std::string::npos) return MessageType::YC_DATA;
        if (channel.find(":yx") != std::string::npos) return MessageType::YX_DATA;
    }
    
    if (channel.find(":control") != std::string::npos) {
        // 从消息内容判断是 YT 还是 YK
        if (message.find("\"type\":3") != std::string::npos || message.find("3:") != std::string::npos) {
            return MessageType::YT_COMMAND;
        }
        if (message.find("\"type\":4") != std::string::npos || message.find("4:") != std::string::npos) {
            return MessageType::YK_COMMAND;
        }
    }
    
    if (channel.find(":status") != std::string::npos) {
        return MessageType::DEVICE_STATUS;
    }
    
    if (channel.find(":alarm") != std::string::npos) {
        return MessageType::ALARM;
    }
    
    // 兼容旧格式
    if (legacy_format_enabled_) {
        return LegacyMessageHandler::DetectLegacyMessageType(channel);
    }
    
    return MessageType::UNKNOWN;
}

int RedisMessageHandler::ExtractDeviceId(const std::string& channel) {
    // 解析通道格式: modbus:device:ID:xxx
    size_t device_pos = channel.find("device:");
    if (device_pos != std::string::npos) {
        size_t id_start = device_pos + 7;  // "device:" 长度
        size_t id_end = channel.find(':', id_start);
        if (id_end != std::string::npos) {
            std::string id_str = channel.substr(id_start, id_end - id_start);
            return utils::StringUtils::ToInt(id_str, 0);
        }
    }
    
    return 0;
}

// 消息解析方法
Result<ProcessedMessage> RedisMessageHandler::ParseJsonMessage(const std::string& message) {
    ProcessedMessage processed;

    try {
        // 简单的 JSON 解析
        size_t type_pos = message.find("\"type\":");
        size_t point_pos = message.find("\"point\":");
        size_t value_pos = message.find("\"value\":");
        size_t device_pos = message.find("\"device_id\":");
        size_t cmd_id_pos = message.find("\"command_id\":");

        if (type_pos != std::string::npos) {
            size_t start = message.find(':', type_pos) + 1;
            size_t end = message.find(',', start);
            if (end == std::string::npos) end = message.find('}', start);
            std::string type_str = utils::StringUtils::Trim(message.substr(start, end - start));
            type_str.erase(std::remove(type_str.begin(), type_str.end(), '"'), type_str.end());
            processed.type_idx.data_type = static_cast<DataType>(utils::StringUtils::ToInt(type_str, 0));
        }

        if (point_pos != std::string::npos) {
            size_t start = message.find(':', point_pos) + 1;
            size_t end = message.find(',', start);
            if (end == std::string::npos) end = message.find('}', start);
            std::string point_str = utils::StringUtils::Trim(message.substr(start, end - start));
            point_str.erase(std::remove(point_str.begin(), point_str.end(), '"'), point_str.end());
            processed.type_idx.point_id = utils::StringUtils::ToInt(point_str, 0);
        }

        if (value_pos != std::string::npos) {
            size_t start = message.find(':', value_pos) + 1;
            size_t end = message.find(',', start);
            if (end == std::string::npos) end = message.find('}', start);
            std::string value_str = utils::StringUtils::Trim(message.substr(start, end - start));
            value_str.erase(std::remove(value_str.begin(), value_str.end(), '"'), value_str.end());
            processed.value = utils::StringUtils::ToDouble(value_str, 0.0);
        }

        if (device_pos != std::string::npos) {
            size_t start = message.find(':', device_pos) + 1;
            size_t end = message.find(',', start);
            if (end == std::string::npos) end = message.find('}', start);
            std::string device_str = utils::StringUtils::Trim(message.substr(start, end - start));
            device_str.erase(std::remove(device_str.begin(), device_str.end(), '"'), device_str.end());
            processed.device_id = utils::StringUtils::ToInt(device_str, 0);
        }

        if (cmd_id_pos != std::string::npos) {
            size_t start = message.find(':', cmd_id_pos) + 1;
            size_t end = message.find(',', start);
            if (end == std::string::npos) end = message.find('}', start);
            processed.command_id = utils::StringUtils::Trim(message.substr(start, end - start));
            processed.command_id.erase(std::remove(processed.command_id.begin(), processed.command_id.end(), '"'), processed.command_id.end());
        }

    } catch (const std::exception& e) {
        return Result<ProcessedMessage>(ErrorCode::INVALID_PARAM, "JSON parsing error: " + std::string(e.what()));
    }

    return Result<ProcessedMessage>(processed);
}

Result<ProcessedMessage> RedisMessageHandler::ParseLegacyMessage(const std::string& message) {
    ProcessedMessage processed;

    // 格式: type:point=value 或 type:point=value@command_id
    size_t colon_pos = message.find(':');
    size_t equal_pos = message.find('=');

    if (colon_pos == std::string::npos || equal_pos == std::string::npos || colon_pos >= equal_pos) {
        return Result<ProcessedMessage>(ErrorCode::INVALID_PARAM, "Invalid legacy format");
    }

    try {
        // 解析 type
        std::string type_str = utils::StringUtils::Trim(message.substr(0, colon_pos));
        processed.type_idx.data_type = static_cast<DataType>(utils::StringUtils::ToInt(type_str, 0));

        // 解析 point
        std::string point_str = utils::StringUtils::Trim(message.substr(colon_pos + 1, equal_pos - colon_pos - 1));
        processed.type_idx.point_id = utils::StringUtils::ToInt(point_str, 0);

        // 解析 value 和可选的 command_id
        std::string value_part = utils::StringUtils::Trim(message.substr(equal_pos + 1));
        size_t at_pos = value_part.find('@');

        if (at_pos != std::string::npos) {
            processed.value = utils::StringUtils::ToDouble(value_part.substr(0, at_pos), 0.0);
            processed.command_id = utils::StringUtils::Trim(value_part.substr(at_pos + 1));
        } else {
            processed.value = utils::StringUtils::ToDouble(value_part, 0.0);
        }

    } catch (const std::exception& e) {
        return Result<ProcessedMessage>(ErrorCode::INVALID_PARAM, "Legacy parsing error: " + std::string(e.what()));
    }

    return Result<ProcessedMessage>(processed);
}

// 消息队列处理
void RedisMessageHandler::StartQueueProcessor() {
    if (processor_running_) {
        return;
    }

    processor_running_ = true;
    queue_processor_ = std::make_unique<std::thread>(&RedisMessageHandler::QueueProcessorThread, this);
}

void RedisMessageHandler::StopQueueProcessor() {
    if (processor_running_) {
        processor_running_ = false;
        queue_condition_.notify_all();

        if (queue_processor_ && queue_processor_->joinable()) {
            queue_processor_->join();
        }
        queue_processor_.reset();
    }
}

void RedisMessageHandler::QueueProcessorThread() {
    while (processor_running_) {
        std::unique_lock<std::mutex> lock(queue_mutex_);

        queue_condition_.wait(lock, [this] {
            return !processor_running_ || !message_queue_.empty();
        });

        if (!processor_running_) {
            break;
        }

        while (!message_queue_.empty() && processor_running_) {
            SubscribeMessage message = message_queue_.front();
            message_queue_.pop();

            lock.unlock();
            ProcessQueuedMessage(message);
            lock.lock();
        }
    }
}

void RedisMessageHandler::ProcessQueuedMessage(const SubscribeMessage& message) {
    auto result = ProcessMessage(message);

    if (!result.success) {
        WRITE_WARN_LOG("Failed to process queued message: %s", result.error_message.c_str());
    }
}

// 回调触发 - 移除了现代 C++ 回调函数，只保留 xj_svg 兼容的接口
void RedisMessageHandler::TriggerCallbacks(const ProcessedMessage& message) {
    // xj_svg 兼容的回调函数通过 ProcessBinaryMessage 方法直接调用
    // 这里不需要额外的回调处理
}

// 消息过滤
bool RedisMessageHandler::ApplyMessageFilters(const ProcessedMessage& message) {
    std::lock_guard<std::mutex> lock(filter_mutex_);

    for (const auto& filter_pair : message_filters_) {
        try {
            if (!filter_pair.second(message)) {
                return false;  // 被过滤器拒绝
            }
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("Message filter exception: %s", e.what());
            return false;
        }
    }

    return true;  // 通过所有过滤器
}

// 统计更新
void RedisMessageHandler::UpdateStatistics(const ProcessedMessage& message, bool success) {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    stats_.total_processed++;
    stats_.last_process_time = utils::TimeUtils::GetCurrentTimestamp();

    if (success) {
        stats_.success_count++;

        switch (message.type) {
            case MessageType::YC_DATA: stats_.yc_count++; break;
            case MessageType::YX_DATA: stats_.yx_count++; break;
            case MessageType::YT_COMMAND: stats_.yt_count++; break;
            case MessageType::YK_COMMAND: stats_.yk_count++; break;
            case MessageType::ALARM: stats_.alarm_count++; break;
            case MessageType::DEVICE_STATUS: stats_.status_count++; break;
            case MessageType::UNKNOWN:
            default: break;
        }
    } else {
        stats_.error_count++;
    }
}

// LegacyMessageHandler 实现
Result<REDIS_YC> LegacyMessageHandler::ParseYCMessage(const std::string& message) {
    auto parts = SplitMessage(message, '|');
    if (!ValidateMessageFormat(parts, 4)) {
        return Result<REDIS_YC>(ErrorCode::INVALID_PARAM, "Invalid YC message format");
    }

    REDIS_YC yc_data;
    try {
        yc_data.dataId = utils::StringUtils::ToInt(parts[0], 0);
        yc_data.val = utils::StringUtils::ToDouble(parts[1], 0.0);
        yc_data.status = static_cast<unsigned char>(utils::StringUtils::ToInt(parts[2], 0));
    } catch (const std::exception& e) {
        return Result<REDIS_YC>(ErrorCode::INVALID_PARAM, "YC parsing error: " + std::string(e.what()));
    }

    return Result<REDIS_YC>(yc_data);
}

Result<REDIS_YX> LegacyMessageHandler::ParseYXMessage(const std::string& message) {
    auto parts = SplitMessage(message, '|');
    if (!ValidateMessageFormat(parts, 4)) {
        return Result<REDIS_YX>(ErrorCode::INVALID_PARAM, "Invalid YX message format");
    }

    REDIS_YX yx_data;
    try {
        yx_data.dataId = utils::StringUtils::ToInt(parts[0], 0);
        yx_data.val = static_cast<short>(utils::StringUtils::ToInt(parts[1], 0));
        yx_data.status = static_cast<unsigned char>(utils::StringUtils::ToInt(parts[2], 0));
    } catch (const std::exception& e) {
        return Result<REDIS_YX>(ErrorCode::INVALID_PARAM, "YX parsing error: " + std::string(e.what()));
    }

    return Result<REDIS_YX>(yx_data);
}

std::string LegacyMessageHandler::FormatYCMessage(const REDIS_YC& yc_data) {
    return utils::StringUtils::Format("%05d|%.6f|%d|%s",
                                    yc_data.dataId, yc_data.val,
                                    static_cast<int>(yc_data.status), "YC Data");
}

std::string LegacyMessageHandler::FormatYXMessage(const REDIS_YX& yx_data) {
    return utils::StringUtils::Format("%05d|%d|%d|%s",
                                    yx_data.dataId, static_cast<int>(yx_data.val),
                                    static_cast<int>(yx_data.status), "YX Data");
}

bool LegacyMessageHandler::IsLegacyFormat(const std::string& message) {
    // 检查是否包含管道符分隔符
    return message.find('|') != std::string::npos;
}

MessageType LegacyMessageHandler::DetectLegacyMessageType(const std::string& channel) {
    if (channel.find("YC") != std::string::npos) return MessageType::YC_DATA;
    if (channel.find("YX") != std::string::npos) return MessageType::YX_DATA;
    if (channel.find("YT") != std::string::npos) return MessageType::YT_COMMAND;
    if (channel.find("YK") != std::string::npos) return MessageType::YK_COMMAND;

    return MessageType::UNKNOWN;
}

std::vector<std::string> LegacyMessageHandler::SplitMessage(const std::string& message, char delimiter) {
    std::vector<std::string> parts;
    std::stringstream ss(message);
    std::string part;

    while (std::getline(ss, part, delimiter)) {
        parts.push_back(utils::StringUtils::Trim(part));
    }

    return parts;
}

bool LegacyMessageHandler::ValidateMessageFormat(const std::vector<std::string>& parts, size_t expected_size) {
    return parts.size() >= expected_size;
}

// MessageRouter 实现
MessageRouter::MessageRouter(std::shared_ptr<RedisMessageHandler> handler)
    : message_handler_(handler) {
}

void MessageRouter::AddRoute(MessageType type, RouteHandler handler) {
    std::lock_guard<std::mutex> lock(route_mutex_);
    type_routes_[type] = handler;
}

void MessageRouter::RemoveRoute(MessageType type) {
    std::lock_guard<std::mutex> lock(route_mutex_);
    type_routes_.erase(type);
}

void MessageRouter::ClearRoutes() {
    std::lock_guard<std::mutex> lock(route_mutex_);
    type_routes_.clear();
    device_routes_.clear();
}

void MessageRouter::AddDeviceRoute(int device_id, RouteHandler handler) {
    std::lock_guard<std::mutex> lock(route_mutex_);
    device_routes_[device_id] = handler;
}

void MessageRouter::RemoveDeviceRoute(int device_id) {
    std::lock_guard<std::mutex> lock(route_mutex_);
    device_routes_.erase(device_id);
}

void MessageRouter::RouteMessage(const ProcessedMessage& message) {
    std::lock_guard<std::mutex> lock(route_mutex_);

    // 优先检查设备路由
    auto device_it = device_routes_.find(message.device_id);
    if (device_it != device_routes_.end()) {
        try {
            device_it->second(message);
            return;
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("设备路由处理器异常: %s", e.what());
        }
    }

    // 检查类型路由
    auto type_it = type_routes_.find(message.type);
    if (type_it != type_routes_.end()) {
        try {
            type_it->second(message);
            return;
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("类型路由处理器异常: %s", e.what());
        }
    }

    // 使用默认处理器
    if (default_handler_) {
        try {
            default_handler_(message);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("Default route handler exception: %s", e.what());
        }
    }
}

void MessageRouter::RouteMessages(const std::vector<ProcessedMessage>& messages) {
    for (const auto& message : messages) {
        RouteMessage(message);
    }
}

void MessageRouter::SetDefaultHandler(RouteHandler handler) {
    std::lock_guard<std::mutex> lock(route_mutex_);
    default_handler_ = handler;
}

// xj_svg 兼容的回调函数设置
void RedisMessageHandler::SetYKCallback(YKMessageCallback callback, void* userdata) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    yk_callback_ = callback;
    yk_userdata_ = userdata;
}

void RedisMessageHandler::SetYTCallback(YTMessageCallback callback, void* userdata) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    yt_callback_ = callback;
    yt_userdata_ = userdata;
}

// 二进制消息处理
MessageProcessResult RedisMessageHandler::ProcessBinaryMessage(const SubscribeMessage& message) {
    MessageProcessResult result;
    result.success = true;

    try {
        if (message.channel == EVENT_RC_SCADA_YK) {
            // 处理遥控命令
            if (message.binary_size == sizeof(REDIS_YK) && yk_callback_) {
                std::lock_guard<std::mutex> lock(callback_mutex_);
                try {
                    // 直接调用 C 风格回调函数，传递二进制数据指针
                    yk_callback_(const_cast<char*>(message.channel.c_str()),
                                const_cast<char*>(message.message.data()),
                                static_cast<int>(message.binary_size),
                                yk_userdata_);
                } catch (const std::exception& e) {
                    WRITE_WARN_LOG("YK callback exception: %s", e.what());
                    result.success = false;
                    result.error_message = e.what();
                }
            }
        } else if (message.channel == EVENT_RC_SCADA_YT) {
            // 处理遥调命令
            if (message.binary_size == sizeof(REDIS_YT) && yt_callback_) {
                std::lock_guard<std::mutex> lock(callback_mutex_);
                try {
                    // 直接调用 C 风格回调函数，传递二进制数据指针
                    yt_callback_(const_cast<char*>(message.channel.c_str()),
                                const_cast<char*>(message.message.data()),
                                static_cast<int>(message.binary_size),
                                yt_userdata_);
                } catch (const std::exception& e) {
                    WRITE_WARN_LOG("YT callback exception: %s", e.what());
                    result.success = false;
                    result.error_message = e.what();
                }
            }
        }

        // 更新统计信息
        ProcessedMessage processed;
        processed.channel = message.channel;
        processed.timestamp = message.timestamp;
        processed.type = (message.channel == EVENT_RC_SCADA_YK) ? MessageType::YK_COMMAND : MessageType::YT_COMMAND;
        UpdateStatistics(processed, result.success);

    } catch (const std::exception& e) {
        WRITE_ERROR_LOG("二进制消息处理错误: %s", e.what());
        result.success = false;
        result.error_message = e.what();
    }

    return result;
}

} // namespace modbus
