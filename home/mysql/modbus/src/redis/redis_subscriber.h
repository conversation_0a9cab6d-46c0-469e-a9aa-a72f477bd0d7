#ifndef REDIS_SUBSCRIBER_H
#define REDIS_SUBSCRIBER_H

#include "../types/modbus_types.h"
#include "../utils/thread_pool.h"
#include "../utils/logger.h"
#include "redis_publisher.h"
#include <string>
#include <memory>
#include <atomic>
#include <set>
#include <functional>

#ifdef HAVE_HIREDIS
#include <hiredis/hiredis.h>
#endif

namespace modbus {

// 订阅消息结构
struct SubscribeMessage {
    std::string channel;
    std::string message;
    uint64_t timestamp;
    bool is_binary = false;  // 是否为二进制数据
    size_t binary_size = 0;  // 二进制数据大小

    SubscribeMessage() : timestamp(0) {}
    SubscribeMessage(const std::string& ch, const std::string& msg)
        : channel(ch), message(msg), timestamp(0) {}
};

// 控制指令结构
struct ControlCommand {
    TypeIndex type_idx;
    double value;
    std::string command_id;
    uint64_t timestamp;
    int priority = 0;
    
    ControlCommand() : value(0.0), timestamp(0) {}
};

// Redis 订阅者接口
class RedisSubscriberInterface {
public:
    virtual ~RedisSubscriberInterface() = default;
    
    // 连接管理
    virtual Result<bool> Connect() = 0;
    virtual void Disconnect() = 0;
    virtual bool IsConnected() const = 0;
    virtual Result<bool> Reconnect() = 0;
    
    // 订阅管理
    virtual Result<bool> Subscribe(const std::string& channel) = 0;
    virtual Result<bool> Unsubscribe(const std::string& channel) = 0;
    virtual Result<bool> SubscribePattern(const std::string& pattern) = 0;
    virtual Result<bool> UnsubscribePattern(const std::string& pattern) = 0;

    // xj_svg 兼容的二进制订阅方法
    virtual Result<bool> SubscribeBinary(const std::string& channel) = 0;
    
    // 启动和停止
    virtual Result<bool> Start() = 0;
    virtual void Stop() = 0;
    virtual bool IsRunning() const = 0;
    
    // 配置管理
    virtual void SetConnectionParam(const RedisConnectionParam& param) = 0;
    virtual RedisConnectionParam GetConnectionParam() const = 0;
    
    // 统计信息
    virtual void GetStats(int& message_count, int& error_count, int& channel_count) const = 0;
    virtual void ResetStats() = 0;

    // 回调函数设置 (在接口中添加)
    using MessageCallback = std::function<void(const SubscribeMessage& message)>;
    using ConnectionStatusCallback = std::function<void(bool connected, const std::string& error)>;
    virtual void SetMessageCallback(MessageCallback callback) = 0;
    virtual void SetConnectionStatusCallback(ConnectionStatusCallback callback) = 0;

    // 扩展功能
    virtual void EnableAutoReconnect(bool enable = true) = 0;
};

#ifdef HAVE_HIREDIS

// Redis 订阅者实现
class RedisSubscriber : public RedisSubscriberInterface {
public:
    explicit RedisSubscriber(const RedisConnectionParam& param = RedisConnectionParam());
    virtual ~RedisSubscriber();
    
    // 禁止拷贝和赋值
    RedisSubscriber(const RedisSubscriber&) = delete;
    RedisSubscriber& operator=(const RedisSubscriber&) = delete;
    
    // 实现接口
    Result<bool> Connect() override;
    void Disconnect() override;
    bool IsConnected() const override;
    Result<bool> Reconnect() override;
    
    Result<bool> Subscribe(const std::string& channel) override;
    Result<bool> Unsubscribe(const std::string& channel) override;
    Result<bool> SubscribePattern(const std::string& pattern) override;
    Result<bool> UnsubscribePattern(const std::string& pattern) override;
    Result<bool> SubscribeBinary(const std::string& channel) override;
    
    Result<bool> Start() override;
    void Stop() override;
    bool IsRunning() const override { return is_running_; }
    
    void SetConnectionParam(const RedisConnectionParam& param) override;
    RedisConnectionParam GetConnectionParam() const override;
    
    void GetStats(int& message_count, int& error_count, int& channel_count) const override;
    void ResetStats() override;
    
    // 扩展功能
    void EnableAutoReconnect(bool enable = true) { auto_reconnect_enabled_ = enable; }
    bool IsAutoReconnectEnabled() const { return auto_reconnect_enabled_; }
    
    // 消息处理回调
    using MessageCallback = std::function<void(const SubscribeMessage& message)>;
    void SetMessageCallback(MessageCallback callback);
    
    // 连接状态回调
    using ConnectionStatusCallback = std::function<void(bool connected, const std::string& error)>;
    void SetConnectionStatusCallback(ConnectionStatusCallback callback);
    
    // 获取订阅的通道列表
    std::set<std::string> GetSubscribedChannels() const;
    std::set<std::string> GetSubscribedPatterns() const;
    
private:
    // 内部方法
    Result<bool> CreateConnection();
    void DestroyConnection();
    Result<bool> Authenticate();
    Result<bool> SelectDatabase();
    Result<bool> TestConnection();
    
    // 订阅处理
    void SubscribeThread();
    void ProcessMessage(redisReply* reply);
    void HandleSubscribeReply(redisReply* reply);
    void HandleMessageReply(redisReply* reply);
    
    // 自动重连
    void StartAutoReconnect();
    void StopAutoReconnect();
    void AutoReconnectThread();
    
    // 错误处理
    std::string GetRedisError() const;
    void UpdateConnectionStatus(bool connected, const std::string& error = "");
    
    // 统计更新
    void RecordMessage();
    void RecordError();
    
private:
    RedisConnectionParam param_;
    redisContext* redis_context_;
    
    // 状态控制
    std::atomic<bool> is_connected_{false};
    std::atomic<bool> is_running_{false};
    std::atomic<bool> auto_reconnect_enabled_{true};
    
    // 订阅管理
    std::set<std::string> subscribed_channels_;
    std::set<std::string> subscribed_patterns_;
    mutable std::mutex subscription_mutex_;
    
    // 线程管理
    std::unique_ptr<std::thread> subscribe_thread_;
    std::unique_ptr<std::thread> reconnect_thread_;
    std::atomic<bool> reconnect_running_{false};
    Event stop_event_;
    
    // 回调函数
    MessageCallback message_callback_;
    ConnectionStatusCallback connection_callback_;
    std::mutex callback_mutex_;
    
    // 统计信息
    std::atomic<int> message_count_{0};
    std::atomic<int> error_count_{0};
    
    // 线程安全
    mutable std::mutex connection_mutex_;
};

#else

// 当没有 hiredis 时的空实现
class RedisSubscriber : public RedisSubscriberInterface {
public:
    explicit RedisSubscriber(const RedisConnectionParam& param = RedisConnectionParam()) {
        WRITE_WARN_LOG("Redis 订阅器创建时未支持 hiredis");
    }
    
    Result<bool> Connect() override {
        return Result<bool>(ErrorCode::NOT_SUPPORTED, "Redis not supported - hiredis not available");
    }
    
    void Disconnect() override {}
    bool IsConnected() const override { return false; }
    Result<bool> Reconnect() override { return Connect(); }
    
    Result<bool> Subscribe(const std::string& channel) override {
        return Result<bool>(ErrorCode::NOT_SUPPORTED, "Redis not supported");
    }
    
    Result<bool> Unsubscribe(const std::string& channel) override {
        return Result<bool>(ErrorCode::NOT_SUPPORTED, "Redis not supported");
    }
    
    Result<bool> SubscribePattern(const std::string& pattern) override {
        return Result<bool>(ErrorCode::NOT_SUPPORTED, "Redis not supported");
    }
    
    Result<bool> UnsubscribePattern(const std::string& pattern) override {
        return Result<bool>(ErrorCode::NOT_SUPPORTED, "Redis not supported");
    }

    Result<bool> SubscribeBinary(const std::string& channel) override {
        return Result<bool>(ErrorCode::NOT_SUPPORTED, "Redis not supported");
    }

    Result<bool> Start() override {
        return Result<bool>(ErrorCode::NOT_SUPPORTED, "Redis not supported");
    }
    
    void Stop() override {}
    bool IsRunning() const override { return false; }
    
    void SetConnectionParam(const RedisConnectionParam& param) override {}
    RedisConnectionParam GetConnectionParam() const override { return RedisConnectionParam(); }
    
    void GetStats(int& message_count, int& error_count, int& channel_count) const override {
        message_count = error_count = channel_count = 0;
    }
    void ResetStats() override {}

    void SetMessageCallback(MessageCallback callback) override {}
    void SetConnectionStatusCallback(ConnectionStatusCallback callback) override {}
    void EnableAutoReconnect(bool enable = true) override {}
};

#endif // HAVE_HIREDIS

// Redis 订阅者工厂
class RedisSubscriberFactory {
public:
    static std::unique_ptr<RedisSubscriberInterface> Create(const RedisConnectionParam& param = RedisConnectionParam());
};

// 控制指令解析器
class ControlCommandParser {
public:
    // 解析控制指令
    static Result<ControlCommand> ParseCommand(const std::string& message);
    
    // 解析 JSON 格式的控制指令
    static Result<ControlCommand> ParseJsonCommand(const std::string& json);
    
    // 解析兼容格式的控制指令 (type:point=value)
    static Result<ControlCommand> ParseLegacyCommand(const std::string& legacy);
    
    // 验证控制指令
    static Result<bool> ValidateCommand(const ControlCommand& command);
    
private:
    static bool IsValidDataType(int data_type);
    static bool IsValidPointId(int point_id);
    static bool IsValidValue(double value, DataType data_type);
};

// 控制指令处理器
class ControlCommandProcessor {
public:
    using CommandHandler = std::function<Result<bool>(const ControlCommand& command)>;
    
    explicit ControlCommandProcessor();
    virtual ~ControlCommandProcessor() = default;
    
    // 设置命令处理器
    void SetCommandHandler(CommandHandler handler);
    
    // 处理控制指令
    Result<bool> ProcessCommand(const ControlCommand& command);
    
    // 批量处理控制指令
    Result<std::vector<bool>> ProcessCommands(const std::vector<ControlCommand>& commands);
    
    // 启用命令队列
    void EnableCommandQueue(bool enable = true);
    bool IsCommandQueueEnabled() const { return queue_enabled_; }
    
    // 设置队列大小限制
    void SetMaxQueueSize(int max_size) { max_queue_size_ = max_size; }
    int GetMaxQueueSize() const { return max_queue_size_; }
    
    // 获取队列状态
    int GetQueueSize() const;
    void ClearQueue();
    
    // 统计信息
    void GetStats(int& processed_count, int& success_count, int& error_count) const;
    void ResetStats();
    
private:
    void ProcessQueuedCommands();
    
private:
    CommandHandler command_handler_;
    
    // 命令队列
    std::atomic<bool> queue_enabled_{false};
    std::atomic<int> max_queue_size_{1000};
    std::queue<ControlCommand> command_queue_;
    mutable std::mutex queue_mutex_;
    
    // 统计信息
    std::atomic<int> processed_count_{0};
    std::atomic<int> success_count_{0};
    std::atomic<int> error_count_{0};
};

} // namespace modbus

#endif // REDIS_SUBSCRIBER_H
