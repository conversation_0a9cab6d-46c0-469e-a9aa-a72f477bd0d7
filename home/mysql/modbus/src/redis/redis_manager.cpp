#include "redis_manager.h"
#include "../utils/utils.h"

namespace modbus {

// 静态成员初始化
std::unique_ptr<RedisManager> GlobalRedisManager::instance_;
std::mutex GlobalRedisManager::instance_mutex_;

// RedisManager 实现
RedisManager::RedisManager(const RedisManagerConfig& config)
    : config_(config)
    , stop_event_(true) {  // manual reset event
    
    command_processor_ = std::make_unique<ControlCommandProcessor>();
}

RedisManager::~RedisManager() {
    Shutdown();
}

Result<bool> RedisManager::Initialize() {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (is_initialized_) {
        return Result<bool>(true);
    }
    
    // 初始化发布者
    if (config_.enable_publisher) {
        InitializePublisher();
    }
    
    // 初始化订阅者
    if (config_.enable_subscriber) {
        InitializeSubscriber();
    }
    
    // 设置回调
    SetupCallbacks();
    
    is_initialized_ = true;
    
    WRITE_INFO_LOG("Redis 管理器初始化完成");
    
    return Result<bool>(true);
}

void RedisManager::Shutdown() {
    Stop();
    
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (subscriber_) {
        subscriber_->Disconnect();
    }
    
    if (publisher_) {
        publisher_->Disconnect();
    }
    
    is_initialized_ = false;
    
    WRITE_INFO_LOG("Redis 管理器已关闭");
}

Result<bool> RedisManager::Start() {
    if (!is_initialized_) {
        auto init_result = Initialize();
        if (!init_result.IsSuccess()) {
            return init_result;
        }
    }
    
    if (is_running_) {
        return Result<bool>(true);
    }
    
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    // 启动发布者
    if (publisher_) {
        auto pub_result = publisher_->Connect();
        if (!pub_result.IsSuccess()) {
            WRITE_WARN_LOG("Redis 发布器连接失败: %s", pub_result.error_message.c_str());
        }
    }
    
    // 启动订阅者
    if (subscriber_) {
        auto sub_result = subscriber_->Start();
        if (!sub_result.IsSuccess()) {
            WRITE_WARN_LOG("Redis 订阅器启动失败: %s", sub_result.error_message.c_str());
        }
    }
    
    // 启动数据上报
    if (data_reporting_enabled_) {
        StartDataReporting();
    }
    
    is_running_ = true;
    
    WRITE_INFO_LOG("Redis 管理器已启动");
    
    return Result<bool>(true);
}

void RedisManager::Stop() {
    if (!is_running_) {
        return;
    }
    
    StopDataReporting();
    
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (subscriber_) {
        subscriber_->Stop();
    }
    
    is_running_ = false;
    
    WRITE_INFO_LOG("Redis 管理器已停止");
}

void RedisManager::SetConfig(const RedisManagerConfig& config) {
    bool was_running = is_running_;
    
    if (was_running) {
        Stop();
    }
    
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    config_ = config;
    
    if (publisher_) {
        publisher_->SetConnectionParam(config_.connection_param);
    }
    
    if (subscriber_) {
        subscriber_->SetConnectionParam(config_.connection_param);
    }
    
    if (was_running) {
        Start();
    }
}

Result<PublishResult> RedisManager::PublishData(int device_id, const TypeIndex& type_idx, double value, uint64_t timestamp) {
    if (!publisher_ || !is_running_) {
        return Result<PublishResult>(ErrorCode::NOT_INITIALIZED, "Publisher not initialized or not running");
    }
    
    if (timestamp == 0) {
        timestamp = utils::TimeUtils::GetCurrentTimestamp();
    }
    
    std::string channel = ChannelNameManager::GetDataChannel(device_id, type_idx.data_type);
    std::string message = ModbusDataFormatter::FormatDataPoint(type_idx, value, timestamp);
    
    return publisher_->PublishAsync(channel, message);
}

Result<PublishResult> RedisManager::PublishDataBatch(int device_id, const std::map<TypeIndex, double>& values, uint64_t timestamp) {
    if (!publisher_ || !is_running_) {
        return Result<PublishResult>(ErrorCode::NOT_INITIALIZED, "Publisher not initialized or not running");
    }
    
    if (timestamp == 0) {
        timestamp = utils::TimeUtils::GetCurrentTimestamp();
    }
    
    std::string channel = ChannelNameManager::GetDataChannel(device_id);
    std::string message = ModbusDataFormatter::FormatDataPoints(values, timestamp);
    
    return publisher_->PublishAsync(channel, message);
}

Result<PublishResult> RedisManager::PublishDeviceStatus(int device_id, DeviceStatus status, const std::string& message) {
    if (!publisher_ || !is_running_) {
        return Result<PublishResult>(ErrorCode::NOT_INITIALIZED, "Publisher not initialized or not running");
    }
    
    std::string channel = ChannelNameManager::GetStatusChannel(device_id);
    std::string status_message = ModbusDataFormatter::FormatDeviceStatus(device_id, status, message);
    
    return publisher_->PublishAsync(channel, status_message);
}

Result<PublishResult> RedisManager::PublishAlarm(int device_id, const TypeIndex& type_idx, const std::string& alarm_type, double value, const std::string& description) {
    if (!publisher_ || !is_running_) {
        return Result<PublishResult>(ErrorCode::NOT_INITIALIZED, "Publisher not initialized or not running");
    }
    
    std::string channel = ChannelNameManager::GetAlarmChannel(device_id);
    std::string alarm_message = ModbusDataFormatter::FormatAlarm(type_idx, alarm_type, value, description);
    
    return publisher_->PublishAsync(channel, alarm_message);
}

Result<bool> RedisManager::SubscribeControlChannel(int device_id) {
    if (!subscriber_ || !is_running_) {
        return Result<bool>(ErrorCode::NOT_INITIALIZED, "Subscriber not initialized or not running");
    }
    
    std::string channel = ChannelNameManager::GetControlChannel(device_id);
    return subscriber_->Subscribe(channel);
}

Result<bool> RedisManager::UnsubscribeControlChannel(int device_id) {
    if (!subscriber_ || !is_running_) {
        return Result<bool>(ErrorCode::NOT_INITIALIZED, "Subscriber not initialized or not running");
    }
    
    std::string channel = ChannelNameManager::GetControlChannel(device_id);
    return subscriber_->Unsubscribe(channel);
}

Result<bool> RedisManager::SubscribeAllControlChannels() {
    if (!subscriber_ || !is_running_) {
        return Result<bool>(ErrorCode::NOT_INITIALIZED, "Subscriber not initialized or not running");
    }
    
    // 订阅所有控制通道的模式
    std::string pattern = "modbus:device:*:control";
    return subscriber_->SubscribePattern(pattern);
}

void RedisManager::SetControlCommandHandler(ControlCommandHandler handler) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    control_handler_ = handler;
}

void RedisManager::EnableDataReporting(bool enable) {
    if (data_reporting_enabled_ == enable) {
        return;
    }
    
    data_reporting_enabled_ = enable;
    
    if (enable && is_running_) {
        StartDataReporting();
    } else if (!enable) {
        StopDataReporting();
    }
}

void RedisManager::SetDataReportInterval(int interval_ms) {
    config_.data_report_interval_ms = interval_ms;
}

RedisManager::Statistics RedisManager::GetStatistics() const {
    Statistics stats;
    
    if (publisher_) {
        publisher_->GetStats(stats.publish_success_count, stats.publish_error_count, stats.publish_pending_count);
        stats.publisher_connected = publisher_->IsConnected();
    }
    
    if (subscriber_) {
        subscriber_->GetStats(stats.message_count, stats.subscribe_error_count, stats.subscribed_channel_count);
        stats.subscriber_connected = subscriber_->IsConnected();
    }
    
    if (command_processor_) {
        command_processor_->GetStats(stats.command_processed_count, stats.command_success_count, stats.command_error_count);
    }
    
    return stats;
}

void RedisManager::ResetStatistics() {
    if (publisher_) {
        publisher_->ResetStats();
    }
    
    if (subscriber_) {
        subscriber_->ResetStats();
    }
    
    if (command_processor_) {
        command_processor_->ResetStats();
    }
}

void RedisManager::SetStatusCallback(StatusCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    status_callback_ = callback;
}

bool RedisManager::IsRedisSupported() {
    return RedisPublisherFactory::IsRedisSupported();
}

// 内部方法实现
void RedisManager::InitializePublisher() {
    publisher_ = RedisPublisherFactory::Create(config_.connection_param);

#ifdef HAVE_HIREDIS
    // 扩展功能只在具体实现类中可用
    auto concrete_publisher = std::dynamic_pointer_cast<RedisPublisher>(publisher_);
    if (concrete_publisher) {
        concrete_publisher->EnableAsyncMode(true);
        concrete_publisher->SetMaxQueueSize(config_.max_publish_queue_size);
        concrete_publisher->EnableAutoReconnect(config_.enable_auto_reconnect);
    }
#endif
}

void RedisManager::InitializeSubscriber() {
    subscriber_ = RedisSubscriberFactory::Create(config_.connection_param);

#ifdef HAVE_HIREDIS
    // 扩展功能只在具体实现类中可用
    auto concrete_subscriber = std::dynamic_pointer_cast<RedisSubscriber>(subscriber_);
    if (concrete_subscriber) {
        concrete_subscriber->EnableAutoReconnect(config_.enable_auto_reconnect);
    }
#endif
}

void RedisManager::SetupCallbacks() {
    if (publisher_) {
        publisher_->SetConnectionStatusCallback([this](bool connected, const std::string& error) {
            HandlePublisherStatus(connected, error);
        });
    }
    
    if (subscriber_) {
        subscriber_->SetConnectionStatusCallback([this](bool connected, const std::string& error) {
            HandleSubscriberStatus(connected, error);
        });
        
        subscriber_->SetMessageCallback([this](const SubscribeMessage& message) {
            HandleSubscribeMessage(message);
        });
    }
    
    if (command_processor_) {
        command_processor_->SetCommandHandler([this](const ControlCommand& command) {
            // 从通道名称中提取设备ID
            int device_id = 0;
            
            // 处理控制指令
            if (control_handler_) {
                return control_handler_(device_id, command);
            }
            
            return Result<bool>(ErrorCode::NOT_INITIALIZED, "Control handler not set");
        });
    }
}

// 消息处理
void RedisManager::HandleSubscribeMessage(const SubscribeMessage& message) {
    // 解析控制指令
    auto command_result = ControlCommandParser::ParseCommand(message.message);
    if (!command_result.IsSuccess()) {
        WRITE_WARN_LOG("Failed to parse control command: %s", command_result.error_message.c_str());
        return;
    }

    // 从通道名称中提取设备ID
    int device_id = 0;
    std::string channel = message.channel;

    // 解析通道名称格式: modbus:device:ID:control
    size_t device_pos = channel.find("device:");
    if (device_pos != std::string::npos) {
        size_t id_start = device_pos + 7;  // "device:" 长度
        size_t id_end = channel.find(':', id_start);
        if (id_end != std::string::npos) {
            std::string id_str = channel.substr(id_start, id_end - id_start);
            device_id = utils::StringUtils::ToInt(id_str, 0);
        }
    }

    HandleControlCommand(device_id, command_result.data);
}

void RedisManager::HandleControlCommand(int device_id, const ControlCommand& command) {
    if (command_processor_) {
        command_processor_->ProcessCommand(command);
    }
}

// 状态处理
void RedisManager::HandlePublisherStatus(bool connected, const std::string& error) {
    if (status_callback_) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        try {
            status_callback_("publisher", connected, error);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("Status callback exception: %s", e.what());
        }
    }

    if (connected) {
        WRITE_INFO_LOG("Redis publisher connected");
    } else {
        WRITE_WARN_LOG("Redis publisher disconnected: %s", error.c_str());
    }
}

void RedisManager::HandleSubscriberStatus(bool connected, const std::string& error) {
    if (status_callback_) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        try {
            status_callback_("subscriber", connected, error);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("Status callback exception: %s", e.what());
        }
    }

    if (connected) {
        WRITE_INFO_LOG("Redis subscriber connected");
    } else {
        WRITE_WARN_LOG("Redis subscriber disconnected: %s", error.c_str());
    }
}

// 数据上报
void RedisManager::StartDataReporting() {
    if (report_running_) {
        return;
    }

    report_running_ = true;
    stop_event_.Reset();
    report_thread_ = std::make_unique<std::thread>(&RedisManager::DataReportingThread, this);
}

void RedisManager::StopDataReporting() {
    if (report_running_) {
        report_running_ = false;
        stop_event_.Set();

        if (report_thread_ && report_thread_->joinable()) {
            report_thread_->join();
        }
        report_thread_.reset();
    }
}

void RedisManager::DataReportingThread() {
    while (report_running_) {
        if (stop_event_.WaitFor(config_.data_report_interval_ms)) {
            break;  // 收到停止信号
        }

        // 这里可以添加定期数据上报逻辑
        // 例如：定期发送心跳、状态信息等
    }
}

// DeviceRedisManager 实现
DeviceRedisManager::DeviceRedisManager(int device_id, std::shared_ptr<RedisManager> redis_manager)
    : device_id_(device_id)
    , device_name_("Device_" + std::to_string(device_id))
    , redis_manager_(redis_manager) {
}

Result<bool> DeviceRedisManager::Initialize() {
    if (!redis_manager_) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Redis manager is null");
    }

    // 延迟订阅控制通道，等待Redis管理器完全启动
    // 控制通道订阅将在Start()方法中进行

    WRITE_INFO_LOG("设备 %d Redis 管理器初始化完成", device_id_);

    return Result<bool>(true);
}

Result<bool> DeviceRedisManager::Start() {
    if (!redis_manager_) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Redis manager is null");
    }

    // 现在Redis管理器已经启动，可以安全地订阅控制通道
    auto subscribe_result = redis_manager_->SubscribeControlChannel(device_id_);
    if (!subscribe_result.IsSuccess()) {
        WRITE_WARN_LOG("设备 %d 控制通道订阅失败: %s",
                      device_id_, subscribe_result.error_message.c_str());
        // 不返回错误，因为控制通道订阅失败不应该阻止设备启动
    } else {
        WRITE_INFO_LOG("设备 %d 控制通道订阅成功", device_id_);
    }

    return Result<bool>(true);
}

void DeviceRedisManager::Shutdown() {
    if (redis_manager_) {
        redis_manager_->UnsubscribeControlChannel(device_id_);
    }

    WRITE_INFO_LOG("设备 %d Redis 管理器已关闭", device_id_);
}

Result<PublishResult> DeviceRedisManager::PublishData(const TypeIndex& type_idx, double value, uint64_t timestamp) {
    if (!redis_manager_) {
        return Result<PublishResult>(ErrorCode::NOT_INITIALIZED, "Redis manager not initialized");
    }

    // 直接内联Redis写入，最大化性能（完全兼容xj_svg项目）
    auto publisher_interface = redis_manager_->GetPublisher();
    if (!publisher_interface) {
        return Result<PublishResult>(ErrorCode::NOT_INITIALIZED, "Publisher not initialized");
    }

    RedisPublisher* publisher = static_cast<RedisPublisher*>(publisher_interface.get());

    // 直接调用Redis命令，避免额外函数调用
    int redis_result = 0;
    switch (type_idx.data_type) {
        case DataType::YC:  // 遥测
            redis_result = publisher->setAgvcYCValue(device_id_, type_idx.point_id, value, value);
            break;
        case DataType::YX:  // 遥信
            redis_result = publisher->setAgvcYXValue(device_id_, type_idx.point_id, static_cast<short>(value));
            break;
        default:
            return Result<PublishResult>(ErrorCode::INVALID_PARAM, "Unsupported data type");
    }

    if (redis_result == 0) {  // 成功
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.data_published_count++;
        stats_.last_publish_time = utils::TimeUtils::GetCurrentTimestamp();

        PublishResult result;
        result.success = true;
        result.publish_time = stats_.last_publish_time;
        return Result<PublishResult>(result);
    } else {  // 失败
        return Result<PublishResult>(ErrorCode::COMM_ERROR, "Redis write failed");
    }
}

Result<PublishResult> DeviceRedisManager::PublishDataBatch(const std::map<TypeIndex, double>& values, uint64_t timestamp) {
    if (!redis_manager_) {
        return Result<PublishResult>(ErrorCode::NOT_INITIALIZED, "Redis manager not initialized");
    }

    auto result = redis_manager_->PublishDataBatch(device_id_, values, timestamp);

    if (result.IsSuccess()) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.data_published_count += values.size();
        stats_.last_publish_time = utils::TimeUtils::GetCurrentTimestamp();
    }

    return result;
}

Result<PublishResult> DeviceRedisManager::PublishStatus(DeviceStatus status, const std::string& message) {
    if (!redis_manager_) {
        return Result<PublishResult>(ErrorCode::NOT_INITIALIZED, "Redis manager not initialized");
    }

    return redis_manager_->PublishDeviceStatus(device_id_, status, message);
}

Result<PublishResult> DeviceRedisManager::PublishAlarm(const TypeIndex& type_idx, const std::string& alarm_type, double value, const std::string& description) {
    if (!redis_manager_) {
        return Result<PublishResult>(ErrorCode::NOT_INITIALIZED, "Redis manager not initialized");
    }

    return redis_manager_->PublishAlarm(device_id_, type_idx, alarm_type, value, description);
}

void DeviceRedisManager::SetControlHandler(DeviceControlHandler handler) {
    control_handler_ = handler;
}

DeviceRedisManager::DeviceStatistics DeviceRedisManager::GetStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

void DeviceRedisManager::ResetStatistics() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_ = DeviceStatistics();
}

Result<bool> DeviceRedisManager::WriteDataToRedisKey(const TypeIndex& type_idx, double value, uint64_t timestamp) {
    if (!redis_manager_ || !redis_manager_->GetPublisher()) {
        return Result<bool>(ErrorCode::NOT_INITIALIZED, "Redis manager or publisher not initialized");
    }

    // 构造Redis键名，兼容xj_svg格式
    std::string key;
    switch (type_idx.data_type) {
        case DataType::YC:  // 遥测
            key = "yc:" + std::to_string(device_id_) + ":" + std::to_string(type_idx.point_id);
            break;
        case DataType::YX:  // 遥信
            key = "yx:" + std::to_string(device_id_) + ":" + std::to_string(type_idx.point_id);
            break;
        case DataType::YT:  // 遥调
            key = "yt:" + std::to_string(device_id_) + ":" + std::to_string(type_idx.point_id);
            break;
        case DataType::YK:  // 遥控
            key = "yk:" + std::to_string(device_id_) + ":" + std::to_string(type_idx.point_id);
            break;
        default:
            return Result<bool>(ErrorCode::INVALID_PARAM, "Unsupported data type");
    }

    // 使用与xj_svg完全一致的二进制格式
    // 需要使用RDBI的方式来写入二进制数据

    // 暂时使用字符串格式，但需要改为二进制格式以完全兼容xj_svg
    char command_buffer[512];
    snprintf(command_buffer, sizeof(command_buffer),
             "HMSET %s realValue %.6f calValue %.6f status 2 updateTime %lu",
             key.c_str(), value, value, timestamp);

    // TODO: 需要实现与xj_svg完全一致的二进制格式存储
    // 格式: HMSET yc:%d:%d realValue %b calValue %b status %d updateTime %b

    // 执行Redis命令
    auto publisher = redis_manager_->GetPublisher();
    std::string command(command_buffer);

    // 旧的字符串格式方法已被XJSVG格式替代，暂时禁用
    WRITE_DEBUG_LOG("设备 %d 旧格式Redis命令已禁用，使用XJSVG格式: %s", device_id_, command.c_str());

    // 返回成功，实际写入由XJSVG方法处理
    return Result<bool>(true);
}

Result<bool> DeviceRedisManager::WriteDataToRedisKeyXJSVG(const TypeIndex& type_idx, double value, uint64_t timestamp) {
    if (!redis_manager_ || !redis_manager_->GetPublisher()) {
        return Result<bool>(ErrorCode::NOT_INITIALIZED, "Redis manager or publisher not initialized");
    }

    // 直接获取RedisPublisher指针，避免动态转换开销
    auto publisher_interface = redis_manager_->GetPublisher();
    RedisPublisher* publisher = static_cast<RedisPublisher*>(publisher_interface.get());

    // 直接调用Redis命令，避免额外的函数调用层级
    int result = 0;
    switch (type_idx.data_type) {
        case DataType::YC:  // 遥测
            result = publisher->setAgvcYCValue(device_id_, type_idx.point_id, value, value);
            break;
        case DataType::YX:  // 遥信
            result = publisher->setAgvcYXValue(device_id_, type_idx.point_id, static_cast<short>(value));
            break;
        default:
            return Result<bool>(ErrorCode::INVALID_PARAM, "Unsupported data type for XJSVG format");
    }

    // 简化结果处理
    return (result == 0) ? Result<bool>(true) : Result<bool>(ErrorCode::COMM_ERROR, "Redis write failed");
}

void DeviceRedisManager::HandleControlCommand(const ControlCommand& command) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.commands_received_count++;
    stats_.last_command_time = utils::TimeUtils::GetCurrentTimestamp();

    if (control_handler_) {
        auto result = control_handler_(command);
        if (result.IsSuccess()) {
            stats_.commands_processed_count++;
        }
    }
}

// GlobalRedisManager 实现
RedisManager& GlobalRedisManager::GetInstance() {
    std::lock_guard<std::mutex> lock(instance_mutex_);

    if (!instance_) {
        instance_ = std::make_unique<RedisManager>();
    }

    return *instance_;
}

bool GlobalRedisManager::Initialize(const RedisManagerConfig& config) {
    std::lock_guard<std::mutex> lock(instance_mutex_);

    if (!instance_) {
        instance_ = std::make_unique<RedisManager>(config);
    } else {
        instance_->SetConfig(config);
    }

    return instance_->Initialize().IsSuccess();
}

void GlobalRedisManager::Shutdown() {
    std::lock_guard<std::mutex> lock(instance_mutex_);

    if (instance_) {
        instance_->Shutdown();
        instance_.reset();
    }
}

// RedisDataReporter 实现
RedisDataReporter::RedisDataReporter(std::shared_ptr<RedisPublisherInterface> publisher)
    : publisher_(publisher) {
}

Result<bool> RedisDataReporter::ReportData(int device_id, const TypeIndex& type_idx, double value, uint64_t timestamp) {
    if (!publisher_) {
        return Result<bool>(ErrorCode::NOT_INITIALIZED, "Publisher not initialized");
    }

    if (timestamp == 0) {
        timestamp = utils::TimeUtils::GetCurrentTimestamp();
    }

    std::string channel = ChannelNameManager::GetDataChannel(device_id, type_idx.data_type);
    std::string message = FormatData(device_id, type_idx, value, timestamp);

    auto result = publisher_->PublishAsync(channel, message);
    return Result<bool>(result.IsSuccess());
}

Result<bool> RedisDataReporter::ReportDataBatch(int device_id, const std::map<TypeIndex, double>& values, uint64_t timestamp) {
    if (!publisher_) {
        return Result<bool>(ErrorCode::NOT_INITIALIZED, "Publisher not initialized");
    }

    if (timestamp == 0) {
        timestamp = utils::TimeUtils::GetCurrentTimestamp();
    }

    std::string channel = ChannelNameManager::GetDataChannel(device_id);
    std::string message = ModbusDataFormatter::FormatDataPoints(values, timestamp);

    auto result = publisher_->PublishAsync(channel, message);
    return Result<bool>(result.IsSuccess());
}

Result<bool> RedisDataReporter::ReportDeviceStatus(int device_id, DeviceStatus status, const std::string& message) {
    if (!publisher_) {
        return Result<bool>(ErrorCode::NOT_INITIALIZED, "Publisher not initialized");
    }

    std::string channel = ChannelNameManager::GetStatusChannel(device_id);
    std::string status_message = ModbusDataFormatter::FormatDeviceStatus(device_id, status, message);

    auto result = publisher_->PublishAsync(channel, status_message);
    return Result<bool>(result.IsSuccess());
}

Result<bool> RedisDataReporter::ReportAlarm(int device_id, const TypeIndex& type_idx, const std::string& alarm_type, double value, const std::string& description) {
    if (!publisher_) {
        return Result<bool>(ErrorCode::NOT_INITIALIZED, "Publisher not initialized");
    }

    std::string channel = ChannelNameManager::GetAlarmChannel(device_id);
    std::string alarm_message = ModbusDataFormatter::FormatAlarm(type_idx, alarm_type, value, description);

    auto result = publisher_->PublishAsync(channel, alarm_message);
    return Result<bool>(result.IsSuccess());
}

void RedisDataReporter::SetCustomFormatter(CustomFormatter formatter) {
    custom_formatter_ = formatter;
}

std::string RedisDataReporter::FormatData(int device_id, const TypeIndex& type_idx, double value, uint64_t timestamp) {
    switch (report_format_) {
        case ReportFormat::JSON:
            return ModbusDataFormatter::FormatDataPoint(type_idx, value, timestamp);

        case ReportFormat::LEGACY:
            return ModbusDataFormatter::FormatLegacyData(static_cast<int>(type_idx.data_type), type_idx.point_id, value, timestamp);

        case ReportFormat::CUSTOM:
            if (custom_formatter_) {
                return custom_formatter_(device_id, type_idx, value, timestamp);
            }
            // 如果没有自定义格式化器，回退到 JSON
            return ModbusDataFormatter::FormatDataPoint(type_idx, value, timestamp);

        default:
            return ModbusDataFormatter::FormatDataPoint(type_idx, value, timestamp);
    }
}

} // namespace modbus
