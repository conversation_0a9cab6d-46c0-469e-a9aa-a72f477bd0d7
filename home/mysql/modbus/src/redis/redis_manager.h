#ifndef REDIS_MANAGER_H
#define REDIS_MANAGER_H

#include "redis_publisher.h"
#include "redis_subscriber.h"
#include "../types/modbus_types.h"
#include "../utils/thread_pool.h"
#include <memory>
#include <functional>
#include <atomic>

namespace modbus {

// Redis 管理器配置
struct RedisManagerConfig {
    RedisConnectionParam connection_param;
    bool enable_publisher = true;
    bool enable_subscriber = true;
    bool enable_auto_reconnect = true;
    int data_report_interval_ms = 200;
    int max_publish_queue_size = 10000;
    int max_command_queue_size = 1000;
    
    RedisManagerConfig() = default;
    RedisManagerConfig(const std::string& host, int port) {
        connection_param.host = host;
        connection_param.port = port;
    }
};

// Redis 管理器 - 统一管理发布者和订阅者
class RedisManager {
public:
    explicit RedisManager(const RedisManagerConfig& config = RedisManagerConfig());
    virtual ~RedisManager();
    
    // 禁止拷贝和赋值
    RedisManager(const RedisManager&) = delete;
    RedisManager& operator=(const RedisManager&) = delete;
    
    // 初始化和控制
    Result<bool> Initialize();
    void Shutdown();
    Result<bool> Start();
    void Stop();
    bool IsRunning() const { return is_running_; }
    
    // 配置管理
    void SetConfig(const RedisManagerConfig& config);
    RedisManagerConfig GetConfig() const { return config_; }
    
    // 发布功能
    Result<PublishResult> PublishData(int device_id, const TypeIndex& type_idx, double value, uint64_t timestamp = 0);
    Result<PublishResult> PublishDataBatch(int device_id, const std::map<TypeIndex, double>& values, uint64_t timestamp = 0);
    Result<PublishResult> PublishDeviceStatus(int device_id, DeviceStatus status, const std::string& message = "");
    Result<PublishResult> PublishAlarm(int device_id, const TypeIndex& type_idx, const std::string& alarm_type, double value, const std::string& description = "");
    
    // 订阅功能
    Result<bool> SubscribeControlChannel(int device_id);
    Result<bool> UnsubscribeControlChannel(int device_id);
    Result<bool> SubscribeAllControlChannels();
    
    // 控制指令处理
    using ControlCommandHandler = std::function<Result<bool>(int device_id, const ControlCommand& command)>;
    void SetControlCommandHandler(ControlCommandHandler handler);
    
    // 数据上报控制
    void EnableDataReporting(bool enable = true);
    bool IsDataReportingEnabled() const { return data_reporting_enabled_; }
    void SetDataReportInterval(int interval_ms);
    
    // 获取组件
    std::shared_ptr<RedisPublisherInterface> GetPublisher() const { return publisher_; }
    std::shared_ptr<RedisSubscriberInterface> GetSubscriber() const { return subscriber_; }
    
    // 统计信息
    struct Statistics {
        // 发布统计
        int publish_success_count = 0;
        int publish_error_count = 0;
        int publish_pending_count = 0;
        
        // 订阅统计
        int message_count = 0;
        int subscribe_error_count = 0;
        int subscribed_channel_count = 0;
        
        // 控制指令统计
        int command_processed_count = 0;
        int command_success_count = 0;
        int command_error_count = 0;
        
        // 连接状态
        bool publisher_connected = false;
        bool subscriber_connected = false;
    };
    
    Statistics GetStatistics() const;
    void ResetStatistics();
    
    // 状态回调
    using StatusCallback = std::function<void(const std::string& component, bool connected, const std::string& error)>;
    void SetStatusCallback(StatusCallback callback);
    
    // 检查 Redis 支持
    static bool IsRedisSupported();
    
private:
    // 内部方法
    void InitializePublisher();
    void InitializeSubscriber();
    void SetupCallbacks();
    
    // 消息处理
    void HandleSubscribeMessage(const SubscribeMessage& message);
    void HandleControlCommand(int device_id, const ControlCommand& command);
    
    // 状态处理
    void HandlePublisherStatus(bool connected, const std::string& error);
    void HandleSubscriberStatus(bool connected, const std::string& error);
    
    // 数据上报
    void StartDataReporting();
    void StopDataReporting();
    void DataReportingThread();
    
private:
    RedisManagerConfig config_;
    
    // 组件
    std::shared_ptr<RedisPublisherInterface> publisher_;
    std::shared_ptr<RedisSubscriberInterface> subscriber_;
    std::unique_ptr<ControlCommandProcessor> command_processor_;
    
    // 状态控制
    std::atomic<bool> is_initialized_{false};
    std::atomic<bool> is_running_{false};
    std::atomic<bool> data_reporting_enabled_{true};
    
    // 数据上报
    std::unique_ptr<std::thread> report_thread_;
    std::atomic<bool> report_running_{false};
    Event stop_event_;
    
    // 回调函数
    ControlCommandHandler control_handler_;
    StatusCallback status_callback_;
    std::mutex callback_mutex_;
    
    // 线程安全
    mutable std::mutex manager_mutex_;
};

// 设备 Redis 管理器 - 管理单个设备的 Redis 通信
class DeviceRedisManager {
public:
    explicit DeviceRedisManager(int device_id, std::shared_ptr<RedisManager> redis_manager);
    virtual ~DeviceRedisManager() = default;
    
    // 禁止拷贝和赋值
    DeviceRedisManager(const DeviceRedisManager&) = delete;
    DeviceRedisManager& operator=(const DeviceRedisManager&) = delete;
    
    // 初始化
    Result<bool> Initialize();
    Result<bool> Start();
    void Shutdown();
    
    // 数据发布
    Result<PublishResult> PublishData(const TypeIndex& type_idx, double value, uint64_t timestamp = 0);
    Result<PublishResult> PublishDataBatch(const std::map<TypeIndex, double>& values, uint64_t timestamp = 0);
    Result<PublishResult> PublishStatus(DeviceStatus status, const std::string& message = "");
    Result<PublishResult> PublishAlarm(const TypeIndex& type_idx, const std::string& alarm_type, double value, const std::string& description = "");
    
    // 控制指令处理
    using DeviceControlHandler = std::function<Result<bool>(const ControlCommand& command)>;
    void SetControlHandler(DeviceControlHandler handler);
    
    // 设备信息
    int GetDeviceId() const { return device_id_; }
    std::string GetDeviceName() const { return device_name_; }
    void SetDeviceName(const std::string& name) { device_name_ = name; }
    
    // 统计信息
    struct DeviceStatistics {
        int data_published_count = 0;
        int commands_received_count = 0;
        int commands_processed_count = 0;
        uint64_t last_publish_time = 0;
        uint64_t last_command_time = 0;
    };
    
    DeviceStatistics GetStatistics() const;
    void ResetStatistics();
    
private:
    void HandleControlCommand(const ControlCommand& command);
    Result<bool> WriteDataToRedisKey(const TypeIndex& type_idx, double value, uint64_t timestamp);
    Result<bool> WriteDataToRedisKeyXJSVG(const TypeIndex& type_idx, double value, uint64_t timestamp);

private:
    int device_id_;
    std::string device_name_;
    std::shared_ptr<RedisManager> redis_manager_;
    DeviceControlHandler control_handler_;
    
    // 统计信息
    mutable DeviceStatistics stats_;
    mutable std::mutex stats_mutex_;
};

// 全局 Redis 管理器
class GlobalRedisManager {
public:
    static RedisManager& GetInstance();
    static bool Initialize(const RedisManagerConfig& config = RedisManagerConfig());
    static void Shutdown();
    
private:
    GlobalRedisManager() = default;
    ~GlobalRedisManager() = default;
    
    GlobalRedisManager(const GlobalRedisManager&) = delete;
    GlobalRedisManager& operator=(const GlobalRedisManager&) = delete;
    
private:
    static std::unique_ptr<RedisManager> instance_;
    static std::mutex instance_mutex_;
};

// 便捷宏定义
#define REDIS_MANAGER() modbus::GlobalRedisManager::GetInstance()

// Redis 数据上报器 - 专门用于数据上报的组件
class RedisDataReporter {
public:
    explicit RedisDataReporter(std::shared_ptr<RedisPublisherInterface> publisher);
    virtual ~RedisDataReporter() = default;
    
    // 上报单个数据点
    Result<bool> ReportData(int device_id, const TypeIndex& type_idx, double value, uint64_t timestamp = 0);
    
    // 批量上报数据点
    Result<bool> ReportDataBatch(int device_id, const std::map<TypeIndex, double>& values, uint64_t timestamp = 0);
    
    // 上报设备状态
    Result<bool> ReportDeviceStatus(int device_id, DeviceStatus status, const std::string& message = "");
    
    // 上报报警信息
    Result<bool> ReportAlarm(int device_id, const TypeIndex& type_idx, const std::string& alarm_type, double value, const std::string& description = "");
    
    // 配置上报格式
    enum class ReportFormat {
        JSON,       // JSON 格式
        LEGACY,     // 兼容格式
        CUSTOM      // 自定义格式
    };
    
    void SetReportFormat(ReportFormat format) { report_format_ = format; }
    ReportFormat GetReportFormat() const { return report_format_; }
    
    // 自定义格式化函数
    using CustomFormatter = std::function<std::string(int device_id, const TypeIndex& type_idx, double value, uint64_t timestamp)>;
    void SetCustomFormatter(CustomFormatter formatter);
    
private:
    std::string FormatData(int device_id, const TypeIndex& type_idx, double value, uint64_t timestamp);
    
private:
    std::shared_ptr<RedisPublisherInterface> publisher_;
    ReportFormat report_format_ = ReportFormat::JSON;
    CustomFormatter custom_formatter_;
};

} // namespace modbus

#endif // REDIS_MANAGER_H
