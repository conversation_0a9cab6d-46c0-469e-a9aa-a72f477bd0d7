#ifndef REDIS_PUBLISHER_H
#define REDIS_PUBLISHER_H

#include "../types/modbus_types.h"
#include "../utils/thread_pool.h"
#include "../utils/logger.h"
#include <string>
#include <memory>
#include <atomic>
#include <queue>
#include <functional>

#ifdef HAVE_HIREDIS
#include <hiredis/hiredis.h>
#endif

namespace modbus {

// Redis 连接参数
struct RedisConnectionParam {
    std::string host = "127.0.0.1";
    int port = 6379;
    std::string password;
    int database = 0;
    int timeout_ms = 3000;
    int retry_interval_ms = 5000;
    int max_retry_count = 10;
    bool enable_auth = false;
    
    RedisConnectionParam() = default;
    RedisConnectionParam(const std::string& h, int p) : host(h), port(p) {}
};

// 发布消息结构
struct PublishMessage {
    std::string channel;
    std::string message;
    uint64_t timestamp;
    int priority = 0;  // 优先级，数字越大优先级越高
    bool is_binary = false;  // 是否为二进制数据

    PublishMessage() : timestamp(0) {}
    PublishMessage(const std::string& ch, const std::string& msg)
        : channel(ch), message(msg), timestamp(0) {}
};

// 发布结果
struct PublishResult {
    bool success = false;
    std::string error_message;
    int subscriber_count = 0;
    uint64_t publish_time = 0;
    
    PublishResult() = default;
    PublishResult(bool s, const std::string& err = "") : success(s), error_message(err) {}
};

// Redis 发布者接口
class RedisPublisherInterface {
public:
    virtual ~RedisPublisherInterface() = default;
    
    // 连接管理
    virtual Result<bool> Connect() = 0;
    virtual void Disconnect() = 0;
    virtual bool IsConnected() const = 0;
    virtual Result<bool> Reconnect() = 0;
    
    // 发布消息
    virtual Result<PublishResult> Publish(const std::string& channel, const std::string& message) = 0;
    virtual Result<PublishResult> PublishAsync(const std::string& channel, const std::string& message) = 0;

    // xj_svg 兼容的二进制数据发布
    virtual Result<PublishResult> PublishBinary(const std::string& channel, const void* data, size_t size) = 0;
    virtual Result<PublishResult> PublishBinaryAsync(const std::string& channel, const void* data, size_t size) = 0;
    
    // 批量发布
    virtual Result<std::vector<PublishResult>> PublishBatch(const std::vector<PublishMessage>& messages) = 0;
    
    // 配置管理
    virtual void SetConnectionParam(const RedisConnectionParam& param) = 0;
    virtual RedisConnectionParam GetConnectionParam() const = 0;
    
    // 统计信息
    virtual void GetStats(int& success_count, int& error_count, int& pending_count) const = 0;
    virtual void ResetStats() = 0;

    // 回调函数设置 (在接口中添加)
    using ConnectionStatusCallback = std::function<void(bool connected, const std::string& error)>;
    using PublishStatusCallback = std::function<void(const PublishMessage& msg, const PublishResult& result)>;
    virtual void SetConnectionStatusCallback(ConnectionStatusCallback callback) = 0;
    virtual void SetPublishStatusCallback(PublishStatusCallback callback) = 0;

    // 扩展功能
    virtual void EnableAutoReconnect(bool enable = true) = 0;

    // 执行原始Redis命令
    virtual Result<bool> ExecuteCommand(const std::string& command) = 0;
};

#ifdef HAVE_HIREDIS

// Redis 发布者实现
class RedisPublisher : public RedisPublisherInterface {
public:
    explicit RedisPublisher(const RedisConnectionParam& param = RedisConnectionParam());
    virtual ~RedisPublisher();
    
    // 禁止拷贝和赋值
    RedisPublisher(const RedisPublisher&) = delete;
    RedisPublisher& operator=(const RedisPublisher&) = delete;
    
    // 实现接口
    Result<bool> Connect() override;
    void Disconnect() override;
    bool IsConnected() const override;
    Result<bool> Reconnect() override;

    Result<PublishResult> Publish(const std::string& channel, const std::string& message) override;
    Result<PublishResult> PublishAsync(const std::string& channel, const std::string& message) override;
    Result<PublishResult> PublishBinary(const std::string& channel, const void* data, size_t size) override;
    Result<PublishResult> PublishBinaryAsync(const std::string& channel, const void* data, size_t size) override;
    Result<std::vector<PublishResult>> PublishBatch(const std::vector<PublishMessage>& messages) override;
    
    void SetConnectionParam(const RedisConnectionParam& param) override;
    RedisConnectionParam GetConnectionParam() const override;
    
    void GetStats(int& success_count, int& error_count, int& pending_count) const override;
    void ResetStats() override;
    
    // 扩展功能
    void EnableAsyncMode(bool enable = true);
    bool IsAsyncModeEnabled() const { return async_mode_enabled_; }
    
    void SetMaxQueueSize(int max_size) { max_queue_size_ = max_size; }
    int GetMaxQueueSize() const { return max_queue_size_; }
    
    void EnableAutoReconnect(bool enable = true) override { auto_reconnect_enabled_ = enable; }
    bool IsAutoReconnectEnabled() const { return auto_reconnect_enabled_; }

    // 执行原始Redis命令
    Result<bool> ExecuteCommand(const std::string& command) override;

    // 连接状态回调
    using ConnectionStatusCallback = std::function<void(bool connected, const std::string& error)>;
    void SetConnectionStatusCallback(ConnectionStatusCallback callback);
    
    // xj_svg 兼容的发布方法 - 严格按照 xj_svg 接口定义
    bool sendYTOrder(const int deviceid, const int dataid, double val, unsigned char status);
    bool sendYKOrder(const int deviceid, const int dataid, unsigned short val, unsigned char status);
    int setAgvcYCValue(int rtuid, int dataid, double realVal);
    int setAgvcYCValue(int rtuid, int dataid, double realVal, double calcVal);
    int setAgvcYXValue(int rtuid, int dataid, short realVal);

    // 发布状态回调
    using PublishStatusCallback = std::function<void(const PublishMessage& msg, const PublishResult& result)>;
    void SetPublishStatusCallback(PublishStatusCallback callback);
    
private:
    // 内部方法
    Result<bool> CreateConnection();
    void DestroyConnection();
    Result<bool> Authenticate();
    Result<bool> SelectDatabase();
    Result<bool> TestConnection();
    
    // 异步处理
    void StartAsyncWorker();
    void StopAsyncWorker();
    void AsyncWorkerThread();
    void ProcessMessage(const PublishMessage& msg);
    
    // 自动重连
    void StartAutoReconnect();
    void StopAutoReconnect();
    void AutoReconnectThread();
    
    // 错误处理
    std::string GetRedisError() const;
    void UpdateConnectionStatus(bool connected, const std::string& error = "");
    
    // 统计更新
    void RecordSuccess();
    void RecordError();
    
private:
    RedisConnectionParam param_;
    redisContext* redis_context_;
    
    // 状态控制
    std::atomic<bool> is_connected_{false};
    std::atomic<bool> async_mode_enabled_{true};
    std::atomic<bool> auto_reconnect_enabled_{true};
    std::atomic<int> max_queue_size_{10000};
    
    // 异步处理
    std::unique_ptr<std::thread> async_worker_;
    std::atomic<bool> worker_running_{false};
    std::queue<PublishMessage> message_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_condition_;
    Event stop_event_;
    
    // 自动重连
    std::unique_ptr<std::thread> reconnect_thread_;
    std::atomic<bool> reconnect_running_{false};
    
    // 回调函数
    ConnectionStatusCallback connection_callback_;
    PublishStatusCallback publish_callback_;
    std::mutex callback_mutex_;
    
    // 统计信息
    std::atomic<int> success_count_{0};
    std::atomic<int> error_count_{0};
    std::atomic<int> pending_count_{0};
    
    // 线程安全
    mutable std::mutex connection_mutex_;
};

#else

// 当没有 hiredis 时的空实现
class RedisPublisher : public RedisPublisherInterface {
public:
    explicit RedisPublisher(const RedisConnectionParam& param = RedisConnectionParam()) {
        WRITE_WARN_LOG("Redis 发布器创建时未支持 hiredis");
    }
    
    Result<bool> Connect() override {
        return Result<bool>(ErrorCode::NOT_SUPPORTED, "Redis not supported - hiredis not available");
    }
    
    void Disconnect() override {}
    bool IsConnected() const override { return false; }
    Result<bool> Reconnect() override { return Connect(); }
    
    Result<PublishResult> Publish(const std::string& channel, const std::string& message) override {
        return Result<PublishResult>(ErrorCode::NOT_SUPPORTED, "Redis not supported");
    }
    
    Result<PublishResult> PublishAsync(const std::string& channel, const std::string& message) override {
        return Publish(channel, message);
    }
    
    Result<std::vector<PublishResult>> PublishBatch(const std::vector<PublishMessage>& messages) override {
        return Result<std::vector<PublishResult>>(ErrorCode::NOT_SUPPORTED, "Redis not supported");
    }
    
    void SetConnectionParam(const RedisConnectionParam& param) override {}
    RedisConnectionParam GetConnectionParam() const override { return RedisConnectionParam(); }
    
    void GetStats(int& success_count, int& error_count, int& pending_count) const override {
        success_count = error_count = pending_count = 0;
    }
    void ResetStats() override {}

    void SetConnectionStatusCallback(ConnectionStatusCallback callback) override {}
    void SetPublishStatusCallback(PublishStatusCallback callback) override {}
    void EnableAutoReconnect(bool enable = true) override {}

    Result<PublishResult> PublishBinary(const std::string&, const void*, size_t) override {
        return Result<PublishResult>(ErrorCode::NOT_SUPPORTED, "Redis not supported");
    }

    Result<PublishResult> PublishBinaryAsync(const std::string& channel, const void* data, size_t size) override {
        return PublishBinary(channel, data, size);
    }
};

#endif // HAVE_HIREDIS

// Redis 发布者工厂
class RedisPublisherFactory {
public:
    static std::unique_ptr<RedisPublisherInterface> Create(const RedisConnectionParam& param = RedisConnectionParam());
    static bool IsRedisSupported();
};

// 数据格式化器 - 将 Modbus 数据转换为 Redis 消息格式
class ModbusDataFormatter {
public:
    // 格式化单个数据点
    static std::string FormatDataPoint(const TypeIndex& type_idx, double value, uint64_t timestamp, int quality = 0);
    
    // 格式化多个数据点
    static std::string FormatDataPoints(const std::map<TypeIndex, double>& values, uint64_t timestamp);
    
    // 格式化设备状态
    static std::string FormatDeviceStatus(int device_id, DeviceStatus status, const std::string& message = "");
    
    // 格式化报警信息
    static std::string FormatAlarm(const TypeIndex& type_idx, const std::string& alarm_type, 
                                  double value, const std::string& description = "");
    
    // JSON 格式化
    static std::string ToJson(const std::map<std::string, std::string>& data);
    
    // 兼容现有系统的格式化
    static std::string FormatLegacyData(int data_type, int point_id, double value, uint64_t timestamp);
};

// 通道名称管理器
class ChannelNameManager {
public:
    // 数据通道
    static std::string GetDataChannel(int device_id);
    static std::string GetDataChannel(int device_id, DataType data_type);
    
    // 状态通道
    static std::string GetStatusChannel(int device_id);
    
    // 报警通道
    static std::string GetAlarmChannel(int device_id);
    
    // 控制通道
    static std::string GetControlChannel(int device_id);
    
    // 兼容现有系统的通道名
    static std::string GetLegacyChannel(const std::string& prefix, int device_id);
    
    // 自定义通道
    static std::string BuildChannel(const std::vector<std::string>& parts);
};

} // namespace modbus

#endif // REDIS_PUBLISHER_H
