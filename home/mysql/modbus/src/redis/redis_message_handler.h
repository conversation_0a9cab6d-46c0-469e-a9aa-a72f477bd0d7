#ifndef REDIS_MESSAGE_HANDLER_H
#define REDIS_MESSAGE_HANDLER_H

#include "../types/modbus_types.h"
#include "redis_subscriber.h"
#include "redis_publisher.h"
#include "../utils/thread_pool.h"
#include "../utils/logger.h"
#include <memory>
#include <functional>
#include <queue>
#include <atomic>

namespace modbus {

// Redis 频道名定义 - 与 xj_svg 项目一致
#define EVENT_RC_SCADA_YK "EVENT_RC_SCADA_YK"
#define EVENT_RC_SCADA_YT "EVENT_RC_SCADA_YT"

// Redis 数据结构定义 - 与 xj_svg 项目完全一致
typedef struct {
    int deviceId;           // 设备ID
    int dataId;             // 数据点ID
    double val;             // 数值
    unsigned char status;   // 状态
} REDIS_YT;

typedef struct {
    int deviceId;           // 设备ID
    int dataId;             // 数据点ID
    unsigned short val;     // 数值
    unsigned char status;   // 状态
} REDIS_YK;

typedef struct {
    int deviceId;           // 设备ID
    int dataId;             // 数据点ID
    double val;             // 数值
    unsigned char status;   // 状态
} REDIS_YC;

typedef struct {
    int deviceId;           // 设备ID
    int dataId;             // 数据点ID
    short val;              // 数值
    unsigned char status;   // 状态
} REDIS_YX;

// 移除兼容性别名以避免与 types/modbus_types.h 中的结构体冲突
// 直接使用 REDIS_* 类型

// 消息类型枚举
enum class MessageType {
    YC_DATA = 1,    // 遥测数据
    YX_DATA = 2,    // 遥信数据
    YT_COMMAND = 3, // 遥调命令
    YK_COMMAND = 4, // 遥控命令
    DEVICE_STATUS = 5, // 设备状态
    ALARM = 6,      // 报警信息
    UNKNOWN = 0     // 未知类型
};

// 处理后的消息结构
struct ProcessedMessage {
    MessageType type;
    int device_id;
    TypeIndex type_idx;
    double value;
    std::string command_id;
    uint64_t timestamp;
    std::string raw_message;
    std::string channel;
    
    ProcessedMessage() : type(MessageType::UNKNOWN), device_id(0), value(0.0), timestamp(0) {}
};

// 消息处理结果
struct MessageProcessResult {
    bool success = false;
    std::string error_message;
    ProcessedMessage processed_msg;
    
    MessageProcessResult() = default;
    MessageProcessResult(bool s, const std::string& err = "") : success(s), error_message(err) {}
};

// Redis 消息处理器
class RedisMessageHandler {
public:
    explicit RedisMessageHandler();
    virtual ~RedisMessageHandler();
    
    // 禁止拷贝和赋值
    RedisMessageHandler(const RedisMessageHandler&) = delete;
    RedisMessageHandler& operator=(const RedisMessageHandler&) = delete;
    
    // 初始化和控制
    Result<bool> Initialize();
    void Shutdown();
    Result<bool> Start();
    void Stop();
    bool IsRunning() const { return is_running_; }
    
    // 消息处理
    MessageProcessResult ProcessMessage(const SubscribeMessage& message);
    MessageProcessResult ProcessLegacyMessage(const std::string& channel, const std::string& message);
    
    // 批量处理
    std::vector<MessageProcessResult> ProcessMessages(const std::vector<SubscribeMessage>& messages);
    
    // 消息格式化
    std::string FormatDataMessage(int device_id, const TypeIndex& type_idx, double value, uint64_t timestamp = 0);
    std::string FormatStatusMessage(int device_id, DeviceStatus status, const std::string& description = "");
    std::string FormatAlarmMessage(int device_id, const TypeIndex& type_idx, const std::string& alarm_type, double value, const std::string& description = "");
    
    // 消息验证
    Result<bool> ValidateMessage(const ProcessedMessage& message);
    Result<bool> ValidateControlCommand(const ProcessedMessage& message);
    Result<bool> ValidateDataMessage(const ProcessedMessage& message);
    
    // xj_svg 兼容的回调函数类型定义 - 严格按照 xj_svg 接口定义
    using YKMessageCallback = void(*)(char *channel, char *msg, int msglen, void *pridata);
    using YTMessageCallback = void(*)(char *channel, char *msg, int msglen, void *pridata);
    
    // xj_svg 兼容的回调函数设置 - 严格按照 xj_svg 接口定义
    void SetYKCallback(YKMessageCallback callback, void* userdata = nullptr);
    void SetYTCallback(YTMessageCallback callback, void* userdata = nullptr);

    // 二进制消息处理 - 仅用于 xj_svg 兼容
    MessageProcessResult ProcessBinaryMessage(const SubscribeMessage& message);
    
    // 消息队列控制
    void EnableMessageQueue(bool enable = true);
    bool IsMessageQueueEnabled() const { return queue_enabled_; }
    void SetMaxQueueSize(int max_size) { max_queue_size_ = max_size; }
    int GetMaxQueueSize() const { return max_queue_size_; }
    int GetQueueSize() const;
    void ClearQueue();
    
    // 统计信息
    struct Statistics {
        int total_processed = 0;
        int success_count = 0;
        int error_count = 0;
        int yc_count = 0;
        int yx_count = 0;
        int yt_count = 0;
        int yk_count = 0;
        int alarm_count = 0;
        int status_count = 0;
        uint64_t last_process_time = 0;
    };
    
    Statistics GetStatistics() const;
    void ResetStatistics();
    
    // 消息过滤
    using MessageFilter = std::function<bool(const ProcessedMessage& message)>;
    void AddMessageFilter(const std::string& name, MessageFilter filter);
    void RemoveMessageFilter(const std::string& name);
    void ClearMessageFilters();
    
    // 兼容性支持
    void EnableLegacyFormat(bool enable = true) { legacy_format_enabled_ = enable; }
    bool IsLegacyFormatEnabled() const { return legacy_format_enabled_; }
    
private:
    // 内部处理方法
    MessageType DetermineMessageType(const std::string& channel, const std::string& message);
    int ExtractDeviceId(const std::string& channel);
    
    // 消息解析
    Result<ProcessedMessage> ParseJsonMessage(const std::string& message);
    Result<ProcessedMessage> ParseLegacyMessage(const std::string& message);
    Result<ProcessedMessage> ParseChannelInfo(const std::string& channel);
    
    // 数据转换
    double ConvertValue(const std::string& value_str, DataType data_type);
    TypeIndex ParseTypeIndex(const std::string& type_str, const std::string& point_str);
    
    // 消息队列处理
    void StartQueueProcessor();
    void StopQueueProcessor();
    void QueueProcessorThread();
    void ProcessQueuedMessage(const SubscribeMessage& message);
    
    // 回调触发
    void TriggerCallbacks(const ProcessedMessage& message);
    
    // 消息过滤
    bool ApplyMessageFilters(const ProcessedMessage& message);
    
    // 统计更新
    void UpdateStatistics(const ProcessedMessage& message, bool success);
    
private:
    // 状态控制
    std::atomic<bool> is_running_{false};
    std::atomic<bool> queue_enabled_{true};
    std::atomic<bool> legacy_format_enabled_{true};
    std::atomic<int> max_queue_size_{10000};
    
    // 消息队列
    std::queue<SubscribeMessage> message_queue_;
    std::unique_ptr<std::thread> queue_processor_;
    std::atomic<bool> processor_running_{false};
    std::mutex queue_mutex_;
    std::condition_variable queue_condition_;
    Event stop_event_;
    
    // xj_svg 兼容的回调函数 - 严格按照 xj_svg 接口定义
    YKMessageCallback yk_callback_;
    YTMessageCallback yt_callback_;
    void* yk_userdata_;
    void* yt_userdata_;
    std::mutex callback_mutex_;
    
    // 消息过滤器
    std::map<std::string, MessageFilter> message_filters_;
    std::mutex filter_mutex_;
    
    // 统计信息
    mutable Statistics stats_;
    mutable std::mutex stats_mutex_;
};

// 兼容性消息处理器 - 处理现有系统的消息格式
class LegacyMessageHandler {
public:
    // 解析现有系统的 YC 消息格式
    static Result<REDIS_YC> ParseYCMessage(const std::string& message);

    // 解析现有系统的 YX 消息格式
    static Result<REDIS_YX> ParseYXMessage(const std::string& message);

    // 解析现有系统的 YT 命令格式
    static Result<REDIS_YT> ParseYTCommand(const std::string& message);

    // 解析现有系统的 YK 命令格式
    static Result<REDIS_YK> ParseYKCommand(const std::string& message);
    
    // 格式化为现有系统的消息格式
    static std::string FormatYCMessage(const REDIS_YC& yc_data);
    static std::string FormatYXMessage(const REDIS_YX& yx_data);
    static std::string FormatYTCommand(const REDIS_YT& yt_command);
    static std::string FormatYKCommand(const REDIS_YK& yk_command);
    
    // 通道名称转换
    static std::string GetLegacyChannelName(DataType data_type);
    static DataType ParseLegacyChannelName(const std::string& channel);
    
    // 消息格式检测
    static bool IsLegacyFormat(const std::string& message);
    static MessageType DetectLegacyMessageType(const std::string& channel);
    
private:
    // 内部解析工具
    static std::vector<std::string> SplitMessage(const std::string& message, char delimiter = '|');
    static bool ValidateMessageFormat(const std::vector<std::string>& parts, size_t expected_size);
};

// 消息路由器 - 根据消息类型路由到不同的处理器
class MessageRouter {
public:
    explicit MessageRouter(std::shared_ptr<RedisMessageHandler> handler);
    virtual ~MessageRouter() = default;
    
    // 路由规则管理
    using RouteHandler = std::function<void(const ProcessedMessage& message)>;
    void AddRoute(MessageType type, RouteHandler handler);
    void RemoveRoute(MessageType type);
    void ClearRoutes();
    
    // 设备路由
    void AddDeviceRoute(int device_id, RouteHandler handler);
    void RemoveDeviceRoute(int device_id);
    
    // 消息路由
    void RouteMessage(const ProcessedMessage& message);
    
    // 批量路由
    void RouteMessages(const std::vector<ProcessedMessage>& messages);
    
    // 默认路由处理器
    void SetDefaultHandler(RouteHandler handler);
    
private:
    std::shared_ptr<RedisMessageHandler> message_handler_;
    std::map<MessageType, RouteHandler> type_routes_;
    std::map<int, RouteHandler> device_routes_;
    RouteHandler default_handler_;
    std::mutex route_mutex_;
};

} // namespace modbus

#endif // REDIS_MESSAGE_HANDLER_H
