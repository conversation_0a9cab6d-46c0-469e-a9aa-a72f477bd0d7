#ifndef MODBUS_COMM_INTERFACE_H
#define MODBUS_COMM_INTERFACE_H

#include "../types/modbus_types.h"
#include <memory>
#include <functional>
#include <mutex>

namespace modbus {

// 连接状态回调函数类型
using ConnectionStatusCallback = std::function<void(int device_id, DeviceStatus status)>;

// Modbus 通信接口基类
class ModbusCommInterface {
public:
    virtual ~ModbusCommInterface() = default;
    
    // 初始化通信
    virtual Result<bool> Initialize() = 0;
    
    // 关闭通信
    virtual void Shutdown() = 0;
    
    // 连接设备
    virtual Result<bool> Connect() = 0;
    
    // 断开连接
    virtual void Disconnect() = 0;
    
    // 重新连接
    virtual Result<bool> Reconnect() = 0;
    
    // 检查连接状态
    virtual bool IsConnected() const = 0;
    
    // 设置从站地址
    virtual Result<bool> SetSlaveId(int slave_id) = 0;
    
    // 获取当前从站地址
    virtual int GetSlaveId() const = 0;
    
    // 设置超时时间
    virtual void SetTimeout(int timeout_ms) = 0;
    
    // 获取超时时间
    virtual int GetTimeout() const = 0;
    
    // 读取线圈状态 (功能码 0x01)
    virtual Result<std::vector<bool>> ReadCoils(int start_addr, int count) = 0;
    
    // 读取离散输入 (功能码 0x02)
    virtual Result<std::vector<bool>> ReadDiscreteInputs(int start_addr, int count) = 0;
    
    // 读取保持寄存器 (功能码 0x03)
    virtual Result<std::vector<uint16_t>> ReadHoldingRegisters(int start_addr, int count) = 0;
    
    // 读取输入寄存器 (功能码 0x04)
    virtual Result<std::vector<uint16_t>> ReadInputRegisters(int start_addr, int count) = 0;
    
    // 写单个线圈 (功能码 0x05)
    virtual Result<bool> WriteSingleCoil(int addr, bool value) = 0;
    
    // 写单个寄存器 (功能码 0x06)
    virtual Result<bool> WriteSingleRegister(int addr, uint16_t value) = 0;
    
    // 写多个线圈 (功能码 0x0F)
    virtual Result<bool> WriteMultipleCoils(int start_addr, const std::vector<bool>& values) = 0;
    
    // 写多个寄存器 (功能码 0x10)
    virtual Result<bool> WriteMultipleRegisters(int start_addr, const std::vector<uint16_t>& values) = 0;
    
    // 设置连接状态回调
    virtual void SetConnectionStatusCallback(ConnectionStatusCallback callback) = 0;
    
    // 获取通信统计信息
    virtual void GetCommStats(int& success_count, int& error_count, int& timeout_count) const = 0;
    
    // 重置通信统计信息
    virtual void ResetCommStats() = 0;
    
    // 获取最后一次错误信息
    virtual std::string GetLastError() const = 0;
    
protected:
    // 通用的数据验证方法
    virtual bool ValidateAddress(int addr) const;
    virtual bool ValidateCount(int count, int max_count) const;
    virtual bool ValidateRegisterValue(uint16_t value) const;
    virtual bool ValidateCoilValue(bool value) const;
};

// Modbus 通信工厂类
class ModbusCommFactory {
public:
    // 创建 RTU 通信实例
    static std::unique_ptr<ModbusCommInterface> CreateRtuComm(const RtuCommParam& param);
    
    // 创建 TCP 通信实例
    static std::unique_ptr<ModbusCommInterface> CreateTcpComm(const TcpCommParam& param);
    
    // 根据通信类型创建实例
    static std::unique_ptr<ModbusCommInterface> CreateComm(CommType type, const DeviceParam& param);
};

// 通信统计信息结构
struct CommStats {
    int success_count = 0;      // 成功次数
    int error_count = 0;        // 错误次数
    int timeout_count = 0;      // 超时次数
    uint64_t last_success_time = 0;  // 最后成功时间
    uint64_t last_error_time = 0;    // 最后错误时间
    std::string last_error_msg;      // 最后错误消息
    
    // 计算成功率
    double GetSuccessRate() const {
        int total = success_count + error_count + timeout_count;
        return total > 0 ? static_cast<double>(success_count) / total : 0.0;
    }
    
    // 重置统计
    void Reset() {
        success_count = 0;
        error_count = 0;
        timeout_count = 0;
        last_success_time = 0;
        last_error_time = 0;
        last_error_msg.clear();
    }
};

// 通信基类实现
class ModbusCommBase : public ModbusCommInterface {
public:
    explicit ModbusCommBase(int device_id = 0);
    virtual ~ModbusCommBase() = default;
    
    // 实现基类接口
    int GetSlaveId() const override { return slave_id_; }
    int GetTimeout() const override { return timeout_ms_; }
    void SetConnectionStatusCallback(ConnectionStatusCallback callback) override;
    void GetCommStats(int& success_count, int& error_count, int& timeout_count) const override;
    void ResetCommStats() override;
    std::string GetLastError() const override;
    
protected:
    // 更新连接状态
    void UpdateConnectionStatus(DeviceStatus status);
    
    // 记录通信成功
    void RecordSuccess();
    
    // 记录通信错误
    void RecordError(const std::string& error_msg);
    
    // 记录通信超时
    void RecordTimeout();
    
    // 设置最后错误信息
    void SetLastError(const std::string& error_msg);
    
    // 验证参数
    bool ValidateParameters() const;
    
protected:
    int device_id_;                     // 设备ID
    int slave_id_;                      // 从站地址
    int timeout_ms_;                    // 超时时间
    DeviceStatus status_;               // 连接状态
    ConnectionStatusCallback status_callback_;  // 状态回调
    CommStats stats_;                   // 通信统计
    mutable std::mutex stats_mutex_;    // 统计信息互斥锁
    std::string last_error_;            // 最后错误信息
    mutable std::mutex error_mutex_;    // 错误信息互斥锁
};

// 内联方法实现
inline bool ModbusCommInterface::ValidateAddress(int addr) const {
    return addr >= 0 && addr <= 65535;
}

inline bool ModbusCommInterface::ValidateCount(int count, int max_count) const {
    return count > 0 && count <= max_count;
}

inline bool ModbusCommInterface::ValidateRegisterValue(uint16_t value) const {
    return true;  // 16位寄存器值总是有效的
}

inline bool ModbusCommInterface::ValidateCoilValue(bool value) const {
    return true;  // 布尔值总是有效的
}

} // namespace modbus

#endif // MODBUS_COMM_INTERFACE_H
