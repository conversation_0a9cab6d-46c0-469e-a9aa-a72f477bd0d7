#include "modbus_comm_interface.h"
#include "modbus_rtu_comm.h"
#include "modbus_tcp_comm.h"
#include "../utils/logger.h"
#include "../utils/utils.h"

namespace modbus {

// ModbusCommBase 实现
ModbusCommBase::ModbusCommBase(int device_id)
    : device_id_(device_id)
    , slave_id_(1)
    , timeout_ms_(1000)
    , status_(DeviceStatus::DISCONNECTED) {
}

void ModbusCommBase::SetConnectionStatusCallback(ConnectionStatusCallback callback) {
    status_callback_ = callback;
}

void ModbusCommBase::GetCommStats(int& success_count, int& error_count, int& timeout_count) const {
    std::unique_lock<std::mutex> lock(stats_mutex_);
    success_count = stats_.success_count;
    error_count = stats_.error_count;
    timeout_count = stats_.timeout_count;
}

void ModbusCommBase::ResetCommStats() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.Reset();
}

std::string ModbusCommBase::GetLastError() const {
    std::unique_lock<std::mutex> lock(error_mutex_);
    return last_error_;
}

void ModbusCommBase::UpdateConnectionStatus(DeviceStatus status) {
    if (status_ != status) {
        status_ = status;
        
        if (status_callback_) {
            status_callback_(device_id_, status);
        }
        
        const char* status_str = "";
        switch (status) {
            case DeviceStatus::DISCONNECTED: status_str = "DISCONNECTED"; break;
            case DeviceStatus::CONNECTING: status_str = "CONNECTING"; break;
            case DeviceStatus::CONNECTED: status_str = "CONNECTED"; break;
            case DeviceStatus::ERROR: status_str = "ERROR"; break;
            case DeviceStatus::INITIALIZED: status_str = "INITIALIZED"; break;
        }
        
        WRITE_INFO_LOG("Device %d status changed to %s", device_id_, status_str);
    }
}

void ModbusCommBase::RecordSuccess() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.success_count++;
    stats_.last_success_time = utils::TimeUtils::GetCurrentTimestamp();
}

void ModbusCommBase::RecordError(const std::string& error_msg) {
    {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.error_count++;
        stats_.last_error_time = utils::TimeUtils::GetCurrentTimestamp();
        stats_.last_error_msg = error_msg;
    }
    
    SetLastError(error_msg);
}

void ModbusCommBase::RecordTimeout() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_.timeout_count++;
    stats_.last_error_time = utils::TimeUtils::GetCurrentTimestamp();
    stats_.last_error_msg = "Timeout";
}

void ModbusCommBase::SetLastError(const std::string& error_msg) {
    std::unique_lock<std::mutex> lock(error_mutex_);
    last_error_ = error_msg;
}

bool ModbusCommBase::ValidateParameters() const {
    if (slave_id_ < 1 || slave_id_ > 247) {
        return false;
    }
    
    if (timeout_ms_ < 100 || timeout_ms_ > 60000) {
        return false;
    }
    
    return true;
}

// ModbusCommFactory 实现
std::unique_ptr<ModbusCommInterface> ModbusCommFactory::CreateRtuComm(const RtuCommParam& param) {
    return std::make_unique<ModbusRtuComm>(param);
}

std::unique_ptr<ModbusCommInterface> ModbusCommFactory::CreateTcpComm(const TcpCommParam& param) {
    return std::make_unique<ModbusTcpComm>(param);
}

std::unique_ptr<ModbusCommInterface> ModbusCommFactory::CreateComm(CommType type, const DeviceParam& param) {
    switch (type) {
        case CommType::RTU:
            return CreateRtuComm(param.rtu_param);
        case CommType::TCP:
            return CreateTcpComm(param.tcp_param);
        default:
            return nullptr;
    }
}

} // namespace modbus
