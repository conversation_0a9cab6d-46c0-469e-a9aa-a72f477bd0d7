#include "modbus_rtu_comm.h"
#include "../utils/logger.h"
#include "../utils/utils.h"
#include <cerrno>
#include <cstring>

namespace modbus {

ModbusRtuComm::ModbusRtuComm(const RtuCommParam& param, int device_id)
    : ModbusCommBase(device_id)
    , rtu_param_(param)
    , modbus_ctx_(nullptr)
    , is_connected_(false)
    , is_initialized_(false)
    , monitor_running_(false)
    , reconnect_running_(false)
    , stop_event_(true)  // manual reset event
    , enable_auto_reconnect_(true)
    , reconnect_interval_ms_(5000)
    , max_reconnect_attempts_(10)
    , connection_check_interval_ms_(30000)
    , reconnect_count_(0)
    , last_activity_time_(0) {
    
    timeout_ms_ = param.timeout_ms;
    slave_id_ = 1;  // 默认从站地址
}

ModbusRtuComm::~ModbusRtuComm() {
    Shutdown();
}

Result<bool> ModbusRtuComm::Initialize() {
    std::lock_guard<MutexLock> lock(context_mutex_);
    
    if (is_initialized_) {
        return Result<bool>(true);
    }
    
    auto result = CreateModbusContext();
    if (!result.IsSuccess()) {
        return result;
    }
    
    auto config_result = ConfigureSerialPort();
    if (!config_result.IsSuccess()) {
        DestroyModbusContext();
        return config_result;
    }
    
    is_initialized_ = true;
    
    WRITE_INFO_LOG("RTU 通信初始化成功，设备 %s:%d",
                   rtu_param_.device.c_str(), rtu_param_.baud);
    
    return Result<bool>(true);
}

void ModbusRtuComm::Shutdown() {
    StopConnectionMonitor();
    StopAutoReconnect();
    Disconnect();
    
    std::lock_guard<MutexLock> lock(context_mutex_);
    DestroyModbusContext();
    is_initialized_ = false;
    
    WRITE_INFO_LOG("RTU 通信已关闭，设备 %s", rtu_param_.device.c_str());
}

Result<bool> ModbusRtuComm::Connect() {
    if (!is_initialized_) {
        auto init_result = Initialize();
        if (!init_result.IsSuccess()) {
            return init_result;
        }
    }
    
    if (is_connected_) {
        return Result<bool>(true);
    }
    
    std::lock_guard<MutexLock> lock(comm_mutex_);
    
    if (modbus_connect(modbus_ctx_) == -1) {
        int error_code = errno;
        std::string error_msg = GetModbusErrorString(error_code);
        RecordError(error_msg);
        UpdateConnectionStatus(DeviceStatus::ERROR);
        
        WRITE_ERROR_LOG("RTU 设备 %s 连接失败: %s",
                       rtu_param_.device.c_str(), error_msg.c_str());
        
        return Result<bool>(MapModbusError(error_code), error_msg);
    }
    
    is_connected_ = true;
    last_activity_time_ = utils::TimeUtils::GetCurrentTimestamp();
    UpdateConnectionStatus(DeviceStatus::CONNECTED);
    RecordSuccess();
    
    // 启动连接监控
    if (enable_auto_reconnect_) {
        StartConnectionMonitor();
    }
    
    WRITE_INFO_LOG("RTU 设备 %s 连接成功", rtu_param_.device.c_str());
    
    return Result<bool>(true);
}

void ModbusRtuComm::Disconnect() {
    StopConnectionMonitor();
    StopAutoReconnect();
    
    std::lock_guard<MutexLock> lock(comm_mutex_);
    
    if (modbus_ctx_ && is_connected_) {
        modbus_close(modbus_ctx_);
        is_connected_ = false;
        UpdateConnectionStatus(DeviceStatus::DISCONNECTED);
        
        WRITE_INFO_LOG("RTU 设备 %s 已断开连接", rtu_param_.device.c_str());
    }
}

Result<bool> ModbusRtuComm::Reconnect() {
    WRITE_INFO_LOG("正在尝试重连 RTU 设备 %s", rtu_param_.device.c_str());
    
    Disconnect();
    
    // 等待一段时间再重连
    utils::TimeUtils::SleepMs(reconnect_interval_ms_);
    
    auto result = Connect();
    if (result.IsSuccess()) {
        reconnect_count_++;
        WRITE_INFO_LOG("RTU 设备 %s 重连成功 (第 %d 次尝试)",
                       rtu_param_.device.c_str(), reconnect_count_.load());
    } else {
        WRITE_ERROR_LOG("RTU 设备 %s 重连失败: %s",
                       rtu_param_.device.c_str(), result.error_message.c_str());
    }
    
    return result;
}

bool ModbusRtuComm::IsConnected() const {
    return is_connected_ && modbus_ctx_ != nullptr;
}

Result<bool> ModbusRtuComm::SetSlaveId(int slave_id) {
    if (slave_id < 1 || slave_id > 247) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid slave ID");
    }
    
    std::lock_guard<MutexLock> lock(comm_mutex_);
    
    if (modbus_ctx_) {
        if (modbus_set_slave(modbus_ctx_, slave_id) == -1) {
            int error_code = errno;
            std::string error_msg = GetModbusErrorString(error_code);
            RecordError(error_msg);
            return Result<bool>(MapModbusError(error_code), error_msg);
        }
    }
    
    slave_id_ = slave_id;
    return Result<bool>(true);
}

void ModbusRtuComm::SetTimeout(int timeout_ms) {
    timeout_ms_ = timeout_ms;
    
    std::lock_guard<MutexLock> lock(comm_mutex_);
    
    if (modbus_ctx_) {
        uint32_t tv_sec = timeout_ms / 1000;
        uint32_t tv_usec = (timeout_ms % 1000) * 1000;
        modbus_set_response_timeout(modbus_ctx_, tv_sec, tv_usec);
    }
}

// Modbus 功能实现
Result<std::vector<bool>> ModbusRtuComm::ReadCoils(int start_addr, int count) {
    if (!ValidateAddress(start_addr) || !ValidateCount(count, Constants::MAX_COIL_COUNT)) {
        return Result<std::vector<bool>>(ErrorCode::INVALID_PARAM, "Invalid parameters");
    }

    if (!IsConnected()) {
        auto connect_result = Connect();
        if (!connect_result.IsSuccess()) {
            return Result<std::vector<bool>>(ErrorCode::COMM_ERROR, "Failed to connect");
        }
    }

    std::lock_guard<MutexLock> lock(comm_mutex_);

    std::vector<uint8_t> data(count);
    int result = modbus_read_bits(modbus_ctx_, start_addr, count, data.data());

    if (result == -1) {
        int error_code = errno;
        RecordError(GetModbusErrorString(error_code));
        return Result<std::vector<bool>>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }

    // 转换为 bool 向量
    std::vector<bool> bool_data(count);
    for (int i = 0; i < count; ++i) {
        bool_data[i] = (data[i] != 0);
    }

    RecordSuccess();
    last_activity_time_ = utils::TimeUtils::GetCurrentTimestamp();

    return Result<std::vector<bool>>(bool_data);
}

Result<std::vector<bool>> ModbusRtuComm::ReadDiscreteInputs(int start_addr, int count) {
    if (!ValidateAddress(start_addr) || !ValidateCount(count, Constants::MAX_COIL_COUNT)) {
        return Result<std::vector<bool>>(ErrorCode::INVALID_PARAM, "Invalid parameters");
    }

    if (!IsConnected()) {
        auto connect_result = Connect();
        if (!connect_result.IsSuccess()) {
            return Result<std::vector<bool>>(ErrorCode::COMM_ERROR, "Failed to connect");
        }
    }

    std::lock_guard<MutexLock> lock(comm_mutex_);

    std::vector<uint8_t> data(count);
    int result = modbus_read_input_bits(modbus_ctx_, start_addr, count, data.data());

    if (result == -1) {
        int error_code = errno;
        RecordError(GetModbusErrorString(error_code));
        return Result<std::vector<bool>>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }

    // 转换为 bool 向量
    std::vector<bool> bool_data(count);
    for (int i = 0; i < count; ++i) {
        bool_data[i] = (data[i] != 0);
    }

    RecordSuccess();
    last_activity_time_ = utils::TimeUtils::GetCurrentTimestamp();

    return Result<std::vector<bool>>(bool_data);
}

Result<std::vector<uint16_t>> ModbusRtuComm::ReadHoldingRegisters(int start_addr, int count) {
    return ReadData<uint16_t>(start_addr, count, modbus_read_registers);
}

Result<std::vector<uint16_t>> ModbusRtuComm::ReadInputRegisters(int start_addr, int count) {
    return ReadData<uint16_t>(start_addr, count, modbus_read_input_registers);
}

Result<bool> ModbusRtuComm::WriteSingleCoil(int addr, bool value) {
    if (!ValidateAddress(addr)) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid address");
    }
    
    if (!IsConnected()) {
        auto connect_result = Connect();
        if (!connect_result.IsSuccess()) {
            return Result<bool>(ErrorCode::COMM_ERROR, "Failed to connect");
        }
    }
    
    std::lock_guard<MutexLock> lock(comm_mutex_);
    
    int result = modbus_write_bit(modbus_ctx_, addr, value ? 1 : 0);
    
    if (result == -1) {
        int error_code = errno;
        RecordError(GetModbusErrorString(error_code));
        return Result<bool>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }
    
    RecordSuccess();
    last_activity_time_ = utils::TimeUtils::GetCurrentTimestamp();
    
    return Result<bool>(true);
}

Result<bool> ModbusRtuComm::WriteSingleRegister(int addr, uint16_t value) {
    if (!ValidateAddress(addr)) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid address");
    }
    
    if (!IsConnected()) {
        auto connect_result = Connect();
        if (!connect_result.IsSuccess()) {
            return Result<bool>(ErrorCode::COMM_ERROR, "Failed to connect");
        }
    }
    
    std::lock_guard<MutexLock> lock(comm_mutex_);
    
    int result = modbus_write_register(modbus_ctx_, addr, value);
    
    if (result == -1) {
        int error_code = errno;
        RecordError(GetModbusErrorString(error_code));
        return Result<bool>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }
    
    RecordSuccess();
    last_activity_time_ = utils::TimeUtils::GetCurrentTimestamp();
    
    return Result<bool>(true);
}

Result<bool> ModbusRtuComm::WriteMultipleCoils(int start_addr, const std::vector<bool>& values) {
    if (!ValidateAddress(start_addr) || values.empty()) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid parameters");
    }
    
    if (!IsConnected()) {
        auto connect_result = Connect();
        if (!connect_result.IsSuccess()) {
            return Result<bool>(ErrorCode::COMM_ERROR, "Failed to connect");
        }
    }
    
    // 转换 bool 向量为 uint8_t 向量
    std::vector<uint8_t> data(values.size());
    for (size_t i = 0; i < values.size(); ++i) {
        data[i] = values[i] ? 1 : 0;
    }
    
    std::lock_guard<MutexLock> lock(comm_mutex_);
    
    int result = modbus_write_bits(modbus_ctx_, start_addr, data.size(), data.data());
    
    if (result == -1) {
        int error_code = errno;
        RecordError(GetModbusErrorString(error_code));
        return Result<bool>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }
    
    RecordSuccess();
    last_activity_time_ = utils::TimeUtils::GetCurrentTimestamp();
    
    return Result<bool>(true);
}

Result<bool> ModbusRtuComm::WriteMultipleRegisters(int start_addr, const std::vector<uint16_t>& values) {
    if (!ValidateAddress(start_addr) || values.empty()) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid parameters");
    }
    
    if (!IsConnected()) {
        auto connect_result = Connect();
        if (!connect_result.IsSuccess()) {
            return Result<bool>(ErrorCode::COMM_ERROR, "Failed to connect");
        }
    }
    
    std::lock_guard<MutexLock> lock(comm_mutex_);
    
    int result = modbus_write_registers(modbus_ctx_, start_addr, values.size(), values.data());
    
    if (result == -1) {
        int error_code = errno;
        RecordError(GetModbusErrorString(error_code));
        return Result<bool>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }
    
    RecordSuccess();
    last_activity_time_ = utils::TimeUtils::GetCurrentTimestamp();
    
    return Result<bool>(true);
}

// RTU 特有方法实现
void ModbusRtuComm::SetRtuParam(const RtuCommParam& param) {
    rtu_param_ = param;
    timeout_ms_ = param.timeout_ms;

    // 如果已经初始化，需要重新初始化
    if (is_initialized_) {
        Shutdown();
        Initialize();
    }
}

bool ModbusRtuComm::IsSerialPortOpen() const {
    return is_connected_ && modbus_ctx_ != nullptr;
}

std::string ModbusRtuComm::GetSerialPortInfo() const {
    return utils::StringUtils::Format("Device: %s, Baud: %d, Parity: %c, DataBit: %d, StopBit: %d",
                                     rtu_param_.device.c_str(), rtu_param_.baud, rtu_param_.parity,
                                     rtu_param_.data_bit, rtu_param_.stop_bit);
}

// 内部方法实现
Result<bool> ModbusRtuComm::CreateModbusContext() {
    if (modbus_ctx_) {
        DestroyModbusContext();
    }

    modbus_ctx_ = modbus_new_rtu(rtu_param_.device.c_str(), rtu_param_.baud,
                                rtu_param_.parity, rtu_param_.data_bit, rtu_param_.stop_bit);

    if (!modbus_ctx_) {
        std::string error_msg = "Failed to create RTU context";
        SetLastError(error_msg);
        return Result<bool>(ErrorCode::CONFIG_ERROR, error_msg);
    }

    return Result<bool>(true);
}

void ModbusRtuComm::DestroyModbusContext() {
    if (modbus_ctx_) {
        if (is_connected_) {
            modbus_close(modbus_ctx_);
            is_connected_ = false;
        }
        modbus_free(modbus_ctx_);
        modbus_ctx_ = nullptr;
    }
}

Result<bool> ModbusRtuComm::ConfigureSerialPort() {
    if (!modbus_ctx_) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Modbus context not created");
    }

    // 设置串口模式
    if (modbus_rtu_set_serial_mode(modbus_ctx_, rtu_param_.mode) == -1) {
        int error_code = errno;
        std::string error_msg = GetModbusErrorString(error_code);
        SetLastError(error_msg);
        return Result<bool>(MapModbusError(error_code), error_msg);
    }

    // 设置超时时间
    SetTimeout(timeout_ms_);

    // 设置从站地址
    if (modbus_set_slave(modbus_ctx_, slave_id_) == -1) {
        int error_code = errno;
        std::string error_msg = GetModbusErrorString(error_code);
        SetLastError(error_msg);
        return Result<bool>(MapModbusError(error_code), error_msg);
    }

    return Result<bool>(true);
}

Result<bool> ModbusRtuComm::TestConnection() {
    if (!IsConnected()) {
        return Result<bool>(ErrorCode::COMM_ERROR, "Not connected");
    }

    // 尝试读取一个寄存器来测试连接
    uint16_t test_data;
    int result = modbus_read_registers(modbus_ctx_, 0, 1, &test_data);

    if (result == -1) {
        int error_code = errno;
        return Result<bool>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }

    return Result<bool>(true);
}

// 错误处理
ErrorCode ModbusRtuComm::MapModbusError(int modbus_errno) const {
    switch (modbus_errno) {
        case EBADF:
        case ECONNRESET:
        case EPIPE:
            return ErrorCode::COMM_ERROR;
        case ETIMEDOUT:
            return ErrorCode::TIMEOUT;
        case EINVAL:
            return ErrorCode::INVALID_PARAM;
        default:
            return ErrorCode::UNKNOWN_ERROR;
    }
}

std::string ModbusRtuComm::GetModbusErrorString(int modbus_errno) const {
    return std::string(modbus_strerror(modbus_errno));
}

// 连接监控
void ModbusRtuComm::StartConnectionMonitor() {
    if (monitor_running_) {
        return;
    }

    monitor_running_ = true;
    stop_event_.Reset();
    monitor_thread_ = std::make_unique<std::thread>(&ModbusRtuComm::ConnectionMonitorThread, this);
}

void ModbusRtuComm::StopConnectionMonitor() {
    if (monitor_running_) {
        monitor_running_ = false;
        stop_event_.Set();

        if (monitor_thread_ && monitor_thread_->joinable()) {
            monitor_thread_->join();
        }
        monitor_thread_.reset();
    }
}

void ModbusRtuComm::ConnectionMonitorThread() {
    while (monitor_running_) {
        if (stop_event_.WaitFor(connection_check_interval_ms_)) {
            break;  // 收到停止信号
        }

        if (IsConnected()) {
            // 检查连接是否仍然有效
            auto test_result = TestConnection();
            if (!test_result.IsSuccess()) {
                WRITE_WARN_LOG("RTU 设备 %s 连接测试失败: %s",
                              rtu_param_.device.c_str(), test_result.error_message.c_str());

                is_connected_ = false;
                UpdateConnectionStatus(DeviceStatus::ERROR);

                // 启动自动重连
                if (enable_auto_reconnect_) {
                    StartAutoReconnect();
                }
            }
        }
    }
}

// 自动重连
void ModbusRtuComm::StartAutoReconnect() {
    if (reconnect_running_) {
        return;
    }

    reconnect_running_ = true;
    reconnect_thread_ = std::make_unique<std::thread>(&ModbusRtuComm::AutoReconnectThread, this);
}

void ModbusRtuComm::StopAutoReconnect() {
    if (reconnect_running_) {
        reconnect_running_ = false;

        if (reconnect_thread_ && reconnect_thread_->joinable()) {
            reconnect_thread_->join();
        }
        reconnect_thread_.reset();
    }
}

void ModbusRtuComm::AutoReconnectThread() {
    int attempt_count = 0;

    while (reconnect_running_ && attempt_count < max_reconnect_attempts_) {
        if (!IsConnected()) {
            attempt_count++;

            WRITE_INFO_LOG("RTU 设备 %s 自动重连尝试 %d/%d",
                          rtu_param_.device.c_str(), attempt_count, max_reconnect_attempts_);

            auto result = Reconnect();
            if (result.IsSuccess()) {
                reconnect_running_ = false;
                return;
            }

            // 等待重连间隔
            utils::TimeUtils::SleepMs(reconnect_interval_ms_);
        } else {
            reconnect_running_ = false;
            return;
        }
    }

    if (attempt_count >= max_reconnect_attempts_) {
        WRITE_ERROR_LOG("RTU 设备 %s 自动重连失败，已尝试 %d 次",
                       rtu_param_.device.c_str(), max_reconnect_attempts_);
        UpdateConnectionStatus(DeviceStatus::ERROR);
    }

    reconnect_running_ = false;
}

} // namespace modbus
