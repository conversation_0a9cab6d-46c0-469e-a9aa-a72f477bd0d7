#include "modbus_tcp_comm.h"
#include "../utils/logger.h"
#include "../utils/utils.h"
#include <cerrno>
#include <cstring>
#include <algorithm>

namespace modbus {

ModbusTcpComm::ModbusTcpComm(const TcpCommParam& param, int device_id)
    : ModbusCommBase(device_id)
    , tcp_param_(param)
    , is_connected_(false)
    , is_initialized_(false)
    , monitor_running_(false)
    , reconnect_running_(false)
    , stop_event_(true)  // manual reset event
    , enable_auto_reconnect_(true)
    , reconnect_interval_ms_(5000)
    , max_reconnect_attempts_(10)
    , connection_check_interval_ms_(30000)
    , max_connections_(10)
    , connection_timeout_ms_(5000)
    , total_reconnect_count_(0)
    , last_activity_time_(0) {
    
    timeout_ms_ = param.timeout_ms;
    slave_id_ = 1;  // 默认从站地址
    current_connection_key_ = GetConnectionKey(param.ip, param.port);
}

ModbusTcpComm::~ModbusTcpComm() {
    Shutdown();
}

Result<bool> ModbusTcpComm::Initialize() {
    std::lock_guard<MutexLock> lock(connections_mutex_);
    
    if (is_initialized_) {
        return Result<bool>(true);
    }
    
    auto result = CreateModbusContext();
    if (!result.IsSuccess()) {
        return result;
    }
    
    auto config_result = ConfigureTcpConnection();
    if (!config_result.IsSuccess()) {
        DestroyModbusContext();
        return config_result;
    }
    
    is_initialized_ = true;
    
    WRITE_INFO_LOG("TCP 通信初始化成功，地址 %s:%d",
                   tcp_param_.ip.c_str(), tcp_param_.port);
    
    return Result<bool>(true);
}

void ModbusTcpComm::Shutdown() {
    StopConnectionMonitor();
    StopAutoReconnect();
    Disconnect();
    
    std::lock_guard<MutexLock> lock(connections_mutex_);
    DestroyModbusContext();
    is_initialized_ = false;
    
    WRITE_INFO_LOG("TCP 通信已关闭，地址 %s:%d",
                   tcp_param_.ip.c_str(), tcp_param_.port);
}

Result<bool> ModbusTcpComm::Connect() {
    if (!is_initialized_) {
        auto init_result = Initialize();
        if (!init_result.IsSuccess()) {
            return init_result;
        }
    }
    
    if (is_connected_) {
        return Result<bool>(true);
    }
    
    std::lock_guard<MutexLock> lock(comm_mutex_);
    
    auto* conn = GetCurrentConnection();
    if (!conn || !conn->modbus_ctx) {
        return Result<bool>(ErrorCode::COMM_ERROR, "No valid connection context");
    }
    
    if (modbus_connect(conn->modbus_ctx) == -1) {
        int error_code = errno;
        std::string error_msg = GetModbusErrorString(error_code);
        RecordError(error_msg);
        UpdateConnectionStatus(DeviceStatus::ERROR);
        
        WRITE_ERROR_LOG("TCP 设备 %s:%d 连接失败: %s",
                       tcp_param_.ip.c_str(), tcp_param_.port, error_msg.c_str());
        
        return Result<bool>(MapModbusError(error_code), error_msg);
    }
    
    conn->is_connected = true;
    conn->last_activity_time = utils::TimeUtils::GetCurrentTimestamp();
    is_connected_ = true;
    last_activity_time_ = conn->last_activity_time;
    UpdateConnectionStatus(DeviceStatus::CONNECTED);
    RecordSuccess();
    
    // 启动连接监控
    if (enable_auto_reconnect_) {
        StartConnectionMonitor();
    }
    
    WRITE_INFO_LOG("TCP 设备 %s:%d 连接成功",
                   tcp_param_.ip.c_str(), tcp_param_.port);
    
    return Result<bool>(true);
}

void ModbusTcpComm::Disconnect() {
    StopConnectionMonitor();
    StopAutoReconnect();
    
    std::lock_guard<MutexLock> lock(comm_mutex_);
    
    auto* conn = GetCurrentConnection();
    if (conn && conn->modbus_ctx && conn->is_connected) {
        modbus_close(conn->modbus_ctx);
        conn->is_connected = false;
        is_connected_ = false;
        UpdateConnectionStatus(DeviceStatus::DISCONNECTED);
        
        WRITE_INFO_LOG("TCP 设备 %s:%d 已断开连接",
                       tcp_param_.ip.c_str(), tcp_param_.port);
    }
}

Result<bool> ModbusTcpComm::Reconnect() {
    WRITE_INFO_LOG("正在尝试重连 TCP 设备 %s:%d",
                   tcp_param_.ip.c_str(), tcp_param_.port);
    
    Disconnect();
    
    // 等待一段时间再重连
    utils::TimeUtils::SleepMs(reconnect_interval_ms_);
    
    auto result = Connect();
    if (result.IsSuccess()) {
        auto* conn = GetCurrentConnection();
        if (conn) {
            conn->reconnect_count++;
            total_reconnect_count_++;
        }
        WRITE_INFO_LOG("TCP 设备 %s:%d 重连成功 (第 %d 次尝试)",
                       tcp_param_.ip.c_str(), tcp_param_.port, total_reconnect_count_.load());
    } else {
        WRITE_ERROR_LOG("TCP 设备 %s:%d 重连失败: %s",
                       tcp_param_.ip.c_str(), tcp_param_.port, result.error_message.c_str());
    }
    
    return result;
}

bool ModbusTcpComm::IsConnected() const {
    std::lock_guard<MutexLock> lock(connections_mutex_);
    const auto* conn = GetCurrentConnection();
    return is_connected_ && conn && conn->modbus_ctx && conn->is_connected;
}

Result<bool> ModbusTcpComm::SetSlaveId(int slave_id) {
    if (slave_id < 1 || slave_id > 247) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid slave ID");
    }
    
    std::lock_guard<MutexLock> lock(comm_mutex_);
    
    auto* conn = GetCurrentConnection();
    if (conn && conn->modbus_ctx) {
        if (modbus_set_slave(conn->modbus_ctx, slave_id) == -1) {
            int error_code = errno;
            std::string error_msg = GetModbusErrorString(error_code);
            RecordError(error_msg);
            return Result<bool>(MapModbusError(error_code), error_msg);
        }
    }
    
    slave_id_ = slave_id;
    return Result<bool>(true);
}

void ModbusTcpComm::SetTimeout(int timeout_ms) {
    timeout_ms_ = timeout_ms;
    
    std::lock_guard<MutexLock> lock(comm_mutex_);
    
    auto* conn = GetCurrentConnection();
    if (conn && conn->modbus_ctx) {
        uint32_t tv_sec = timeout_ms / 1000;
        uint32_t tv_usec = (timeout_ms % 1000) * 1000;
        modbus_set_response_timeout(conn->modbus_ctx, tv_sec, tv_usec);
    }
}

// Modbus 功能实现
Result<std::vector<bool>> ModbusTcpComm::ReadCoils(int start_addr, int count) {
    if (!ValidateAddress(start_addr) || !ValidateCount(count, Constants::MAX_COIL_COUNT)) {
        return Result<std::vector<bool>>(ErrorCode::INVALID_PARAM, "Invalid parameters");
    }
    
    if (!IsConnected()) {
        auto connect_result = Connect();
        if (!connect_result.IsSuccess()) {
            return Result<std::vector<bool>>(ErrorCode::COMM_ERROR, "Failed to connect");
        }
    }
    
    std::lock_guard<MutexLock> lock(comm_mutex_);
    
    auto* conn = GetCurrentConnection();
    if (!conn || !conn->modbus_ctx) {
        return Result<std::vector<bool>>(ErrorCode::COMM_ERROR, "No active connection");
    }
    
    std::vector<uint8_t> data(count);
    int result = modbus_read_bits(conn->modbus_ctx, start_addr, count, data.data());
    
    if (result == -1) {
        int error_code = errno;
        RecordError(GetModbusErrorString(error_code));
        
        if (error_code == EBADF || error_code == ECONNRESET || error_code == EPIPE) {
            conn->is_connected = false;
            is_connected_ = false;
            UpdateConnectionStatus(DeviceStatus::DISCONNECTED);
        }
        
        return Result<std::vector<bool>>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }
    
    // 转换为 bool 向量
    std::vector<bool> bool_data(count);
    for (int i = 0; i < count; ++i) {
        bool_data[i] = (data[i] != 0);
    }
    
    RecordSuccess();
    conn->last_activity_time = utils::TimeUtils::GetCurrentTimestamp();
    last_activity_time_ = conn->last_activity_time;
    
    return Result<std::vector<bool>>(bool_data);
}

Result<std::vector<bool>> ModbusTcpComm::ReadDiscreteInputs(int start_addr, int count) {
    if (!ValidateAddress(start_addr) || !ValidateCount(count, Constants::MAX_COIL_COUNT)) {
        return Result<std::vector<bool>>(ErrorCode::INVALID_PARAM, "Invalid parameters");
    }
    
    if (!IsConnected()) {
        auto connect_result = Connect();
        if (!connect_result.IsSuccess()) {
            return Result<std::vector<bool>>(ErrorCode::COMM_ERROR, "Failed to connect");
        }
    }
    
    std::lock_guard<MutexLock> lock(comm_mutex_);
    
    auto* conn = GetCurrentConnection();
    if (!conn || !conn->modbus_ctx) {
        return Result<std::vector<bool>>(ErrorCode::COMM_ERROR, "No active connection");
    }
    
    std::vector<uint8_t> data(count);
    int result = modbus_read_input_bits(conn->modbus_ctx, start_addr, count, data.data());
    
    if (result == -1) {
        int error_code = errno;
        RecordError(GetModbusErrorString(error_code));
        
        if (error_code == EBADF || error_code == ECONNRESET || error_code == EPIPE) {
            conn->is_connected = false;
            is_connected_ = false;
            UpdateConnectionStatus(DeviceStatus::DISCONNECTED);
        }
        
        return Result<std::vector<bool>>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }
    
    // 转换为 bool 向量
    std::vector<bool> bool_data(count);
    for (int i = 0; i < count; ++i) {
        bool_data[i] = (data[i] != 0);
    }
    
    RecordSuccess();
    conn->last_activity_time = utils::TimeUtils::GetCurrentTimestamp();
    last_activity_time_ = conn->last_activity_time;
    
    return Result<std::vector<bool>>(bool_data);
}

Result<std::vector<uint16_t>> ModbusTcpComm::ReadHoldingRegisters(int start_addr, int count) {
    return ReadData<uint16_t>(start_addr, count, modbus_read_registers);
}

Result<std::vector<uint16_t>> ModbusTcpComm::ReadInputRegisters(int start_addr, int count) {
    return ReadData<uint16_t>(start_addr, count, modbus_read_input_registers);
}

Result<bool> ModbusTcpComm::WriteSingleCoil(int addr, bool value) {
    if (!ValidateAddress(addr)) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid address");
    }

    if (!IsConnected()) {
        auto connect_result = Connect();
        if (!connect_result.IsSuccess()) {
            return Result<bool>(ErrorCode::COMM_ERROR, "Failed to connect");
        }
    }

    std::lock_guard<MutexLock> lock(comm_mutex_);

    auto* conn = GetCurrentConnection();
    if (!conn || !conn->modbus_ctx) {
        return Result<bool>(ErrorCode::COMM_ERROR, "No active connection");
    }

    int result = modbus_write_bit(conn->modbus_ctx, addr, value ? 1 : 0);

    if (result == -1) {
        int error_code = errno;
        RecordError(GetModbusErrorString(error_code));

        if (error_code == EBADF || error_code == ECONNRESET || error_code == EPIPE) {
            conn->is_connected = false;
            is_connected_ = false;
            UpdateConnectionStatus(DeviceStatus::DISCONNECTED);
        }

        return Result<bool>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }

    RecordSuccess();
    conn->last_activity_time = utils::TimeUtils::GetCurrentTimestamp();
    last_activity_time_ = conn->last_activity_time;

    return Result<bool>(true);
}

Result<bool> ModbusTcpComm::WriteSingleRegister(int addr, uint16_t value) {
    if (!ValidateAddress(addr)) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid address");
    }

    if (!IsConnected()) {
        auto connect_result = Connect();
        if (!connect_result.IsSuccess()) {
            return Result<bool>(ErrorCode::COMM_ERROR, "Failed to connect");
        }
    }

    std::lock_guard<MutexLock> lock(comm_mutex_);

    auto* conn = GetCurrentConnection();
    if (!conn || !conn->modbus_ctx) {
        return Result<bool>(ErrorCode::COMM_ERROR, "No active connection");
    }

    int result = modbus_write_register(conn->modbus_ctx, addr, value);

    if (result == -1) {
        int error_code = errno;
        RecordError(GetModbusErrorString(error_code));

        if (error_code == EBADF || error_code == ECONNRESET || error_code == EPIPE) {
            conn->is_connected = false;
            is_connected_ = false;
            UpdateConnectionStatus(DeviceStatus::DISCONNECTED);
        }

        return Result<bool>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }

    RecordSuccess();
    conn->last_activity_time = utils::TimeUtils::GetCurrentTimestamp();
    last_activity_time_ = conn->last_activity_time;

    return Result<bool>(true);
}

Result<bool> ModbusTcpComm::WriteMultipleCoils(int start_addr, const std::vector<bool>& values) {
    if (!ValidateAddress(start_addr) || values.empty()) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid parameters");
    }

    if (!IsConnected()) {
        auto connect_result = Connect();
        if (!connect_result.IsSuccess()) {
            return Result<bool>(ErrorCode::COMM_ERROR, "Failed to connect");
        }
    }

    // 转换 bool 向量为 uint8_t 向量
    std::vector<uint8_t> data(values.size());
    for (size_t i = 0; i < values.size(); ++i) {
        data[i] = values[i] ? 1 : 0;
    }

    std::lock_guard<MutexLock> lock(comm_mutex_);

    auto* conn = GetCurrentConnection();
    if (!conn || !conn->modbus_ctx) {
        return Result<bool>(ErrorCode::COMM_ERROR, "No active connection");
    }

    int result = modbus_write_bits(conn->modbus_ctx, start_addr, data.size(), data.data());

    if (result == -1) {
        int error_code = errno;
        RecordError(GetModbusErrorString(error_code));

        if (error_code == EBADF || error_code == ECONNRESET || error_code == EPIPE) {
            conn->is_connected = false;
            is_connected_ = false;
            UpdateConnectionStatus(DeviceStatus::DISCONNECTED);
        }

        return Result<bool>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }

    RecordSuccess();
    conn->last_activity_time = utils::TimeUtils::GetCurrentTimestamp();
    last_activity_time_ = conn->last_activity_time;

    return Result<bool>(true);
}

Result<bool> ModbusTcpComm::WriteMultipleRegisters(int start_addr, const std::vector<uint16_t>& values) {
    if (!ValidateAddress(start_addr) || values.empty()) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid parameters");
    }

    if (!IsConnected()) {
        auto connect_result = Connect();
        if (!connect_result.IsSuccess()) {
            return Result<bool>(ErrorCode::COMM_ERROR, "Failed to connect");
        }
    }

    std::lock_guard<MutexLock> lock(comm_mutex_);

    auto* conn = GetCurrentConnection();
    if (!conn || !conn->modbus_ctx) {
        return Result<bool>(ErrorCode::COMM_ERROR, "No active connection");
    }

    int result = modbus_write_registers(conn->modbus_ctx, start_addr, values.size(), values.data());

    if (result == -1) {
        int error_code = errno;
        RecordError(GetModbusErrorString(error_code));

        if (error_code == EBADF || error_code == ECONNRESET || error_code == EPIPE) {
            conn->is_connected = false;
            is_connected_ = false;
            UpdateConnectionStatus(DeviceStatus::DISCONNECTED);
        }

        return Result<bool>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }

    RecordSuccess();
    conn->last_activity_time = utils::TimeUtils::GetCurrentTimestamp();
    last_activity_time_ = conn->last_activity_time;

    return Result<bool>(true);
}

// TCP 特有方法实现
void ModbusTcpComm::SetTcpParam(const TcpCommParam& param) {
    tcp_param_ = param;
    timeout_ms_ = param.timeout_ms;

    // 如果已经初始化，需要重新初始化
    if (is_initialized_) {
        Shutdown();
        Initialize();
    }
}

bool ModbusTcpComm::IsSocketConnected() const {
    return IsConnected();
}

std::string ModbusTcpComm::GetConnectionInfo() const {
    std::lock_guard<MutexLock> lock(connections_mutex_);
    const auto* conn = GetCurrentConnection();
    if (conn) {
        return utils::StringUtils::Format("TCP: %s:%d, Connected: %s, Reconnects: %d",
                                         tcp_param_.ip.c_str(), tcp_param_.port,
                                         conn->is_connected ? "Yes" : "No",
                                         conn->reconnect_count);
    }
    return "TCP: No connection";
}

// 多连接支持
Result<bool> ModbusTcpComm::AddConnection(const std::string& ip, int port) {
    std::string key = GetConnectionKey(ip, port);

    std::lock_guard<MutexLock> lock(connections_mutex_);

    if (connections_.find(key) != connections_.end()) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Connection already exists");
    }

    if (static_cast<int>(connections_.size()) >= max_connections_) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Maximum connections reached");
    }

    TcpConnectionInfo conn_info;
    conn_info.ip = ip;
    conn_info.port = port;

    // 创建 modbus 上下文
    conn_info.modbus_ctx = modbus_new_tcp(ip.c_str(), port);
    if (!conn_info.modbus_ctx) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Failed to create TCP context");
    }

    // 设置超时
    uint32_t tv_sec = timeout_ms_ / 1000;
    uint32_t tv_usec = (timeout_ms_ % 1000) * 1000;
    modbus_set_response_timeout(conn_info.modbus_ctx, tv_sec, tv_usec);

    // 设置从站地址
    modbus_set_slave(conn_info.modbus_ctx, slave_id_);

    connections_[key] = conn_info;

    WRITE_INFO_LOG("已添加 TCP 连接: %s:%d", ip.c_str(), port);

    return Result<bool>(true);
}

Result<bool> ModbusTcpComm::RemoveConnection(const std::string& ip, int port) {
    std::string key = GetConnectionKey(ip, port);

    std::lock_guard<MutexLock> lock(connections_mutex_);

    auto it = connections_.find(key);
    if (it == connections_.end()) {
        return Result<bool>(ErrorCode::DEVICE_NOT_FOUND, "Connection not found");
    }

    // 如果是当前连接，先断开
    if (key == current_connection_key_) {
        if (it->second.is_connected) {
            modbus_close(it->second.modbus_ctx);
        }
        is_connected_ = false;
        current_connection_key_.clear();
    }

    // 释放资源
    if (it->second.modbus_ctx) {
        modbus_free(it->second.modbus_ctx);
    }

    connections_.erase(it);

    WRITE_INFO_LOG("Removed TCP connection: %s:%d", ip.c_str(), port);

    return Result<bool>(true);
}

Result<bool> ModbusTcpComm::SwitchConnection(const std::string& ip, int port) {
    std::string key = GetConnectionKey(ip, port);

    std::lock_guard<MutexLock> lock(connections_mutex_);

    auto it = connections_.find(key);
    if (it == connections_.end()) {
        return Result<bool>(ErrorCode::DEVICE_NOT_FOUND, "Connection not found");
    }

    // 断开当前连接
    if (!current_connection_key_.empty()) {
        auto current_it = connections_.find(current_connection_key_);
        if (current_it != connections_.end() && current_it->second.is_connected) {
            modbus_close(current_it->second.modbus_ctx);
            current_it->second.is_connected = false;
        }
    }

    current_connection_key_ = key;
    is_connected_ = it->second.is_connected;

    WRITE_INFO_LOG("Switched to TCP connection: %s:%d", ip.c_str(), port);

    return Result<bool>(true);
}

std::vector<std::string> ModbusTcpComm::GetActiveConnections() const {
    std::lock_guard<MutexLock> lock(connections_mutex_);

    std::vector<std::string> active_connections;
    for (const auto& pair : connections_) {
        if (pair.second.is_connected) {
            active_connections.push_back(pair.first);
        }
    }

    return active_connections;
}

int ModbusTcpComm::GetActiveConnectionCount() const {
    std::lock_guard<MutexLock> lock(connections_mutex_);

    int count = 0;
    for (const auto& pair : connections_) {
        if (pair.second.is_connected) {
            count++;
        }
    }

    return count;
}

// 内部方法实现
Result<bool> ModbusTcpComm::CreateModbusContext() {
    std::string key = GetConnectionKey(tcp_param_.ip, tcp_param_.port);

    auto it = connections_.find(key);
    if (it != connections_.end()) {
        // 连接已存在，清理旧的上下文
        if (it->second.modbus_ctx) {
            if (it->second.is_connected) {
                modbus_close(it->second.modbus_ctx);
            }
            modbus_free(it->second.modbus_ctx);
        }
    }

    TcpConnectionInfo conn_info;
    conn_info.ip = tcp_param_.ip;
    conn_info.port = tcp_param_.port;
    conn_info.modbus_ctx = modbus_new_tcp(tcp_param_.ip.c_str(), tcp_param_.port);

    if (!conn_info.modbus_ctx) {
        std::string error_msg = "Failed to create TCP context";
        SetLastError(error_msg);
        return Result<bool>(ErrorCode::CONFIG_ERROR, error_msg);
    }

    connections_[key] = conn_info;
    current_connection_key_ = key;

    return Result<bool>(true);
}

void ModbusTcpComm::DestroyModbusContext() {
    for (auto& pair : connections_) {
        if (pair.second.modbus_ctx) {
            if (pair.second.is_connected) {
                modbus_close(pair.second.modbus_ctx);
                pair.second.is_connected = false;
            }
            modbus_free(pair.second.modbus_ctx);
            pair.second.modbus_ctx = nullptr;
        }
    }
    connections_.clear();
    current_connection_key_.clear();
    is_connected_ = false;
}

Result<bool> ModbusTcpComm::ConfigureTcpConnection() {
    auto* conn = GetCurrentConnection();
    if (!conn || !conn->modbus_ctx) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Modbus context not created");
    }

    // 设置超时时间
    SetTimeout(timeout_ms_);

    // 设置从站地址
    if (modbus_set_slave(conn->modbus_ctx, slave_id_) == -1) {
        int error_code = errno;
        std::string error_msg = GetModbusErrorString(error_code);
        SetLastError(error_msg);
        return Result<bool>(MapModbusError(error_code), error_msg);
    }

    return Result<bool>(true);
}

Result<bool> ModbusTcpComm::TestConnection() {
    if (!IsConnected()) {
        return Result<bool>(ErrorCode::COMM_ERROR, "Not connected");
    }

    auto* conn = GetCurrentConnection();
    if (!conn || !conn->modbus_ctx) {
        return Result<bool>(ErrorCode::COMM_ERROR, "No active connection");
    }

    // 尝试读取一个寄存器来测试连接
    uint16_t test_data;
    int result = modbus_read_registers(conn->modbus_ctx, 0, 1, &test_data);

    if (result == -1) {
        int error_code = errno;
        return Result<bool>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }

    return Result<bool>(true);
}

// 连接管理辅助方法
std::string ModbusTcpComm::GetConnectionKey(const std::string& ip, int port) const {
    return ip + ":" + std::to_string(port);
}

TcpConnectionInfo* ModbusTcpComm::GetCurrentConnection() {
    if (current_connection_key_.empty()) {
        return nullptr;
    }

    auto it = connections_.find(current_connection_key_);
    return (it != connections_.end()) ? &it->second : nullptr;
}

const TcpConnectionInfo* ModbusTcpComm::GetCurrentConnection() const {
    if (current_connection_key_.empty()) {
        return nullptr;
    }

    auto it = connections_.find(current_connection_key_);
    return (it != connections_.end()) ? &it->second : nullptr;
}

// 错误处理
ErrorCode ModbusTcpComm::MapModbusError(int modbus_errno) const {
    switch (modbus_errno) {
        case EBADF:
        case ECONNRESET:
        case EPIPE:
        case ECONNREFUSED:
        case EHOSTUNREACH:
        case ENETUNREACH:
            return ErrorCode::COMM_ERROR;
        case ETIMEDOUT:
            return ErrorCode::TIMEOUT;
        case EINVAL:
            return ErrorCode::INVALID_PARAM;
        default:
            return ErrorCode::UNKNOWN_ERROR;
    }
}

std::string ModbusTcpComm::GetModbusErrorString(int modbus_errno) const {
    return std::string(modbus_strerror(modbus_errno));
}

// 连接监控
void ModbusTcpComm::StartConnectionMonitor() {
    if (monitor_running_) {
        return;
    }

    monitor_running_ = true;
    stop_event_.Reset();
    monitor_thread_ = std::make_unique<std::thread>(&ModbusTcpComm::ConnectionMonitorThread, this);
}

void ModbusTcpComm::StopConnectionMonitor() {
    if (monitor_running_) {
        monitor_running_ = false;
        stop_event_.Set();

        if (monitor_thread_ && monitor_thread_->joinable()) {
            monitor_thread_->join();
        }
        monitor_thread_.reset();
    }
}

void ModbusTcpComm::ConnectionMonitorThread() {
    while (monitor_running_) {
        if (stop_event_.WaitFor(connection_check_interval_ms_)) {
            break;  // 收到停止信号
        }

        if (IsConnected()) {
            // 检查连接是否仍然有效
            auto test_result = TestConnection();
            if (!test_result.IsSuccess()) {
                WRITE_WARN_LOG("TCP 设备 %s:%d 连接测试失败: %s",
                              tcp_param_.ip.c_str(), tcp_param_.port,
                              test_result.error_message.c_str());

                auto* conn = GetCurrentConnection();
                if (conn) {
                    conn->is_connected = false;
                }
                is_connected_ = false;
                UpdateConnectionStatus(DeviceStatus::ERROR);

                // 启动自动重连
                if (enable_auto_reconnect_) {
                    StartAutoReconnect();
                }
            }
        }

        // 清理不活跃的连接
        CleanupInactiveConnections();
    }
}

// 自动重连
void ModbusTcpComm::StartAutoReconnect() {
    if (reconnect_running_) {
        return;
    }

    reconnect_running_ = true;
    reconnect_thread_ = std::make_unique<std::thread>(&ModbusTcpComm::AutoReconnectThread, this);
}

void ModbusTcpComm::StopAutoReconnect() {
    if (reconnect_running_) {
        reconnect_running_ = false;

        if (reconnect_thread_ && reconnect_thread_->joinable()) {
            reconnect_thread_->join();
        }
        reconnect_thread_.reset();
    }
}

void ModbusTcpComm::AutoReconnectThread() {
    int attempt_count = 0;

    while (reconnect_running_ && attempt_count < max_reconnect_attempts_) {
        if (!IsConnected()) {
            attempt_count++;

            WRITE_INFO_LOG("Auto reconnect attempt %d/%d for TCP device %s:%d",
                          attempt_count, max_reconnect_attempts_,
                          tcp_param_.ip.c_str(), tcp_param_.port);

            auto result = Reconnect();
            if (result.IsSuccess()) {
                reconnect_running_ = false;
                return;
            }

            // 等待重连间隔
            utils::TimeUtils::SleepMs(reconnect_interval_ms_);
        } else {
            reconnect_running_ = false;
            return;
        }
    }

    if (attempt_count >= max_reconnect_attempts_) {
        WRITE_ERROR_LOG("Auto reconnect failed after %d attempts for TCP device %s:%d",
                       max_reconnect_attempts_, tcp_param_.ip.c_str(), tcp_param_.port);
        UpdateConnectionStatus(DeviceStatus::ERROR);
    }

    reconnect_running_ = false;
}

// 连接池清理
void ModbusTcpComm::CleanupInactiveConnections() {
    std::lock_guard<MutexLock> lock(connections_mutex_);

    uint64_t current_time = utils::TimeUtils::GetCurrentTimestamp();
    const uint64_t inactive_timeout = 300000; // 5分钟不活跃超时

    auto it = connections_.begin();
    while (it != connections_.end()) {
        if (!it->second.is_connected &&
            (current_time - it->second.last_activity_time) > inactive_timeout) {

            WRITE_INFO_LOG("Cleaning up inactive TCP connection: %s", it->first.c_str());

            if (it->second.modbus_ctx) {
                modbus_free(it->second.modbus_ctx);
            }

            // 如果是当前连接，清除引用
            if (it->first == current_connection_key_) {
                current_connection_key_.clear();
                is_connected_ = false;
            }

            it = connections_.erase(it);
        } else {
            ++it;
        }
    }
}

} // namespace modbus
