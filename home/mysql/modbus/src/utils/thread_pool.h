#ifndef THREAD_POOL_H
#define THREAD_POOL_H

#include <vector>
#include <queue>
#include <memory>
#include <thread>
#include <mutex>
#include <shared_mutex>
#include <condition_variable>
#include <future>
#include <functional>
#include <atomic>

namespace modbus {

// 互斥锁封装
class MutexLock {
public:
    MutexLock() = default;
    ~MutexLock() = default;

    void Lock() { mutex_.lock(); }
    void Unlock() { mutex_.unlock(); }
    bool TryLock() { return mutex_.try_lock(); }

    // 添加标准接口以兼容 std::lock_guard
    void lock() { mutex_.lock(); }
    void unlock() { mutex_.unlock(); }
    bool try_lock() { return mutex_.try_lock(); }

private:
    mutable std::mutex mutex_;
};

// 读写锁封装
class ReadWriteLock {
public:
    ReadWriteLock() = default;
    ~ReadWriteLock() = default;

    void ReadLock() { mutex_.lock_shared(); }
    void ReadUnlock() { mutex_.unlock_shared(); }
    void WriteLock() { mutex_.lock(); }
    void WriteUnlock() { mutex_.unlock(); }

private:
    mutable std::shared_mutex mutex_;
};

// 读锁 RAII
class ReadLock {
public:
    explicit ReadLock(ReadWriteLock& lock) : lock_(lock) { lock_.ReadLock(); }
    ~ReadLock() { lock_.ReadUnlock(); }

private:
    ReadWriteLock& lock_;
};

// 写锁 RAII
class WriteLock {
public:
    explicit WriteLock(ReadWriteLock& lock) : lock_(lock) { lock_.WriteLock(); }
    ~WriteLock() { lock_.WriteUnlock(); }

private:
    ReadWriteLock& lock_;
};

// 线程池类
class ThreadPool {
public:
    explicit ThreadPool(size_t thread_count = std::thread::hardware_concurrency());
    ~ThreadPool();
    
    // 禁止拷贝和赋值
    ThreadPool(const ThreadPool&) = delete;
    ThreadPool& operator=(const ThreadPool&) = delete;
    
    // 提交任务
    template<class F, class... Args>
    auto Submit(F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type>;
    
    // 获取线程数量
    size_t GetThreadCount() const { return threads_.size(); }
    
    // 获取队列中的任务数量
    size_t GetQueueSize() const;
    
    // 停止线程池
    void Stop();
    
    // 等待所有任务完成
    void WaitForTasks();
    
private:
    void WorkerThread();
    
private:
    std::vector<std::thread> threads_;
    std::queue<std::function<void()>> tasks_;
    
    mutable std::mutex queue_mutex_;
    std::condition_variable condition_;
    std::atomic<bool> stop_{false};
};

// 模板方法实现
template<class F, class... Args>
auto ThreadPool::Submit(F&& f, Args&&... args) 
    -> std::future<typename std::result_of<F(Args...)>::type> {
    
    using return_type = typename std::result_of<F(Args...)>::type;
    
    auto task = std::make_shared<std::packaged_task<return_type()>>(
        std::bind(std::forward<F>(f), std::forward<Args>(args)...)
    );
    
    std::future<return_type> result = task->get_future();
    
    {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        
        if (stop_) {
            throw std::runtime_error("Submit task to stopped ThreadPool");
        }
        
        tasks_.emplace([task](){ (*task)(); });
    }
    
    condition_.notify_one();
    return result;
}

// 定时器类
class Timer {
public:
    Timer();
    ~Timer();
    
    // 禁止拷贝和赋值
    Timer(const Timer&) = delete;
    Timer& operator=(const Timer&) = delete;
    
    // 启动定时器
    void Start(int interval_ms, std::function<void()> callback, bool repeat = true);
    
    // 停止定时器
    void Stop();
    
    // 检查是否正在运行
    bool IsRunning() const { return running_; }
    
private:
    void TimerThread();
    
private:
    std::thread timer_thread_;
    std::atomic<bool> running_{false};
    std::atomic<bool> stop_{false};
    std::atomic<int> interval_ms_{0};
    std::atomic<bool> repeat_{true};
    std::function<void()> callback_;
    std::mutex callback_mutex_;
    std::condition_variable condition_;
};

// 重复定义已删除

// 重复定义已删除

// 删除重复的别名定义

// 信号量类
class Semaphore {
public:
    explicit Semaphore(int count = 0) : count_(count) {}
    
    void Wait() {
        std::unique_lock<std::mutex> lock(mutex_);
        condition_.wait(lock, [this] { return count_ > 0; });
        --count_;
    }
    
    bool TryWait() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (count_ > 0) {
            --count_;
            return true;
        }
        return false;
    }
    
    void Signal() {
        std::lock_guard<std::mutex> lock(mutex_);
        ++count_;
        condition_.notify_one();
    }
    
    void Signal(int count) {
        std::lock_guard<std::mutex> lock(mutex_);
        count_ += count;
        condition_.notify_all();
    }
    
private:
    std::mutex mutex_;
    std::condition_variable condition_;
    int count_;
};

// 事件类
class Event {
public:
    Event(bool manual_reset = false) : manual_reset_(manual_reset) {}
    
    void Set() {
        std::lock_guard<std::mutex> lock(mutex_);
        signaled_ = true;
        if (manual_reset_) {
            condition_.notify_all();
        } else {
            condition_.notify_one();
        }
    }
    
    void Reset() {
        std::lock_guard<std::mutex> lock(mutex_);
        signaled_ = false;
    }
    
    void Wait() {
        std::unique_lock<std::mutex> lock(mutex_);
        condition_.wait(lock, [this] { return signaled_; });
        if (!manual_reset_) {
            signaled_ = false;
        }
    }
    
    bool WaitFor(int timeout_ms) {
        std::unique_lock<std::mutex> lock(mutex_);
        bool result = condition_.wait_for(lock, std::chrono::milliseconds(timeout_ms),
                                         [this] { return signaled_; });
        if (result && !manual_reset_) {
            signaled_ = false;
        }
        return result;
    }
    
private:
    std::mutex mutex_;
    std::condition_variable condition_;
    bool signaled_ = false;
    bool manual_reset_;
};

} // namespace modbus

#endif // THREAD_POOL_H
