#include "thread_pool.h"
#include <chrono>

namespace modbus {

// ThreadPool 实现
ThreadPool::ThreadPool(size_t thread_count) {
    for (size_t i = 0; i < thread_count; ++i) {
        threads_.emplace_back(&ThreadPool::WorkerThread, this);
    }
}

ThreadPool::~ThreadPool() {
    Stop();
}

size_t ThreadPool::GetQueueSize() const {
    std::unique_lock<std::mutex> lock(queue_mutex_);
    return tasks_.size();
}

void ThreadPool::Stop() {
    {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        stop_ = true;
    }
    
    condition_.notify_all();
    
    for (std::thread& worker : threads_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
    
    threads_.clear();
}

void ThreadPool::WaitForTasks() {
    while (true) {
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            if (tasks_.empty()) {
                break;
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
}

void ThreadPool::WorkerThread() {
    while (true) {
        std::function<void()> task;
        
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            condition_.wait(lock, [this] { return stop_ || !tasks_.empty(); });
            
            if (stop_ && tasks_.empty()) {
                return;
            }
            
            task = std::move(tasks_.front());
            tasks_.pop();
        }
        
        try {
            task();
        } catch (const std::exception& e) {
            // 记录异常，但不影响线程池运行
            // 这里可以添加日志记录
        }
    }
}

// Timer 实现
Timer::Timer() = default;

Timer::~Timer() {
    Stop();
}

void Timer::Start(int interval_ms, std::function<void()> callback, bool repeat) {
    Stop();  // 先停止之前的定时器
    
    {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        callback_ = std::move(callback);
    }
    
    interval_ms_ = interval_ms;
    repeat_ = repeat;
    stop_ = false;
    running_ = true;
    
    timer_thread_ = std::thread(&Timer::TimerThread, this);
}

void Timer::Stop() {
    if (running_) {
        stop_ = true;
        condition_.notify_all();
        
        if (timer_thread_.joinable()) {
            timer_thread_.join();
        }
        
        running_ = false;
    }
}

void Timer::TimerThread() {
    std::unique_lock<std::mutex> lock(callback_mutex_);
    
    while (!stop_) {
        // 等待指定的时间间隔
        if (condition_.wait_for(lock, std::chrono::milliseconds(interval_ms_),
                               [this] { return stop_.load(); })) {
            break;  // 收到停止信号
        }
        
        // 执行回调函数
        if (callback_ && !stop_) {
            try {
                lock.unlock();
                callback_();
                lock.lock();
            } catch (const std::exception& e) {
                // 记录异常，但继续运行
                // 这里可以添加日志记录
            }
        }
        
        // 如果不是重复执行，则退出
        if (!repeat_) {
            break;
        }
    }
    
    running_ = false;
}

} // namespace modbus
