#ifndef UTILS_H
#define UTILS_H

#include <string>
#include <vector>
#include <chrono>
#include <cstdint>

namespace modbus {
namespace utils {

// 字符串工具函数
class StringUtils {
public:
    // 字符串分割
    static std::vector<std::string> Split(const std::string& str, const std::string& delimiter);
    
    // 去除首尾空白字符
    static std::string Trim(const std::string& str);
    
    // 转换为大写
    static std::string ToUpper(const std::string& str);
    
    // 转换为小写
    static std::string ToLower(const std::string& str);
    
    // 字符串替换
    static std::string Replace(const std::string& str, const std::string& from, const std::string& to);
    
    // 检查字符串是否以指定前缀开始
    static bool StartsWith(const std::string& str, const std::string& prefix);
    
    // 检查字符串是否以指定后缀结束
    static bool EndsWith(const std::string& str, const std::string& suffix);
    
    // 格式化字符串 (移除模板版本)
    
    // 字符串转数字
    static int ToInt(const std::string& str, int default_value = 0);
    static double ToDouble(const std::string& str, double default_value = 0.0);
    static bool ToBool(const std::string& str, bool default_value = false);
    
    // 数字转字符串
    static std::string ToString(int value);
    static std::string ToString(double value, int precision = 6);
    static std::string ToString(bool value);

    // 格式化字符串
    static std::string Format(const char* format, ...);
};

// 时间工具函数
class TimeUtils {
public:
    // 获取当前时间戳（毫秒）
    static uint64_t GetCurrentTimestamp();
    
    // 获取当前时间戳（秒）
    static uint64_t GetCurrentTimestampSec();
    
    // 时间戳转字符串
    static std::string TimestampToString(uint64_t timestamp, const std::string& format = "%Y-%m-%d %H:%M:%S");
    
    // 字符串转时间戳
    static uint64_t StringToTimestamp(const std::string& time_str, const std::string& format = "%Y-%m-%d %H:%M:%S");
    
    // 睡眠指定毫秒数
    static void SleepMs(int milliseconds);
    
    // 睡眠指定秒数
    static void SleepSec(int seconds);
    
    // 获取程序运行时间（毫秒）
    static uint64_t GetElapsedTime();
    
private:
    static std::chrono::steady_clock::time_point start_time_;
};

// 文件工具函数
class FileUtils {
public:
    // 检查文件是否存在
    static bool Exists(const std::string& path);
    
    // 检查是否为目录
    static bool IsDirectory(const std::string& path);
    
    // 检查是否为文件
    static bool IsFile(const std::string& path);
    
    // 创建目录
    static bool CreateDirectory(const std::string& path);
    
    // 删除文件
    static bool DeleteFile(const std::string& path);
    
    // 删除目录
    static bool DeleteDirectory(const std::string& path);
    
    // 获取文件大小
    static size_t GetFileSize(const std::string& path);
    
    // 读取文件内容
    static std::string ReadFile(const std::string& path);
    
    // 写入文件内容
    static bool WriteFile(const std::string& path, const std::string& content);
    
    // 获取文件扩展名
    static std::string GetExtension(const std::string& path);
    
    // 获取文件名（不含路径）
    static std::string GetFileName(const std::string& path);
    
    // 获取目录路径
    static std::string GetDirectory(const std::string& path);
    
    // 列出目录中的文件
    static std::vector<std::string> ListFiles(const std::string& directory, const std::string& extension = "");
};

// 数据转换工具函数
class DataUtils {
public:
    // 字节序转换
    static uint16_t SwapBytes16(uint16_t value);
    static uint32_t SwapBytes32(uint32_t value);
    static uint64_t SwapBytes64(uint64_t value);
    
    // 大端序转换
    static uint16_t ToBigEndian16(uint16_t value);
    static uint32_t ToBigEndian32(uint32_t value);
    
    // 小端序转换
    static uint16_t ToLittleEndian16(uint16_t value);
    static uint32_t ToLittleEndian32(uint32_t value);
    
    // 从大端序转换
    static uint16_t FromBigEndian16(uint16_t value);
    static uint32_t FromBigEndian32(uint32_t value);
    
    // 从小端序转换
    static uint16_t FromLittleEndian16(uint16_t value);
    static uint32_t FromLittleEndian32(uint32_t value);
    
    // 字节数组转换
    static std::vector<uint8_t> ToBytes(uint16_t value, bool big_endian = true);
    static std::vector<uint8_t> ToBytes(uint32_t value, bool big_endian = true);
    static uint16_t FromBytes16(const std::vector<uint8_t>& bytes, bool big_endian = true);
    static uint32_t FromBytes32(const std::vector<uint8_t>& bytes, bool big_endian = true);
    
    // 十六进制字符串转换
    static std::string ToHexString(const std::vector<uint8_t>& data);
    static std::vector<uint8_t> FromHexString(const std::string& hex_str);
    
    // CRC 校验
    static uint16_t CalculateCRC16(const std::vector<uint8_t>& data);
    static uint32_t CalculateCRC32(const std::vector<uint8_t>& data);
};

// 配置工具函数
class ConfigUtils {
public:
    // 解析 INI 格式的配置行
    static bool ParseIniLine(const std::string& line, std::string& section, std::string& key, std::string& value);
    
    // 解析键值对
    static bool ParseKeyValue(const std::string& line, std::string& key, std::string& value, char delimiter = '=');
    
    // 解析逗号分隔的值
    static std::vector<std::string> ParseCommaSeparated(const std::string& value);
    
    // 解析数字列表
    static std::vector<int> ParseIntList(const std::string& value, char delimiter = ',');
    
    // 解析浮点数列表
    static std::vector<double> ParseDoubleList(const std::string& value, char delimiter = ',');
    
    // 验证配置值
    static bool ValidateRange(int value, int min_val, int max_val);
    static bool ValidateRange(double value, double min_val, double max_val);
    static bool ValidateEnum(const std::string& value, const std::vector<std::string>& valid_values);
};

// 网络工具函数
class NetworkUtils {
public:
    // 检查IP地址格式
    static bool IsValidIPAddress(const std::string& ip);
    
    // 检查端口号范围
    static bool IsValidPort(int port);
    
    // 解析地址字符串 "ip:port"
    static bool ParseAddress(const std::string& address, std::string& ip, int& port);
    
    // 检查网络连接
    static bool TestConnection(const std::string& ip, int port, int timeout_ms = 3000);
};

// 模板方法实现已移除，使用 C 风格的 Format 函数

} // namespace utils
} // namespace modbus

#endif // UTILS_H
