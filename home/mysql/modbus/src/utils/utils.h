#pragma once

#include <string>
#include <vector>
#include <chrono>
#include <thread>
#include <cstdint>

namespace modbus {
namespace utils {

// 时间工具类
class TimeUtils {
public:
    // 获取当前时间戳 (毫秒)
    static uint64_t GetCurrentTimestamp();
    
    // 获取当前时间戳 (微秒)
    static uint64_t GetCurrentTimestampUs();
    
    // 睡眠指定毫秒数
    static void SleepMs(int milliseconds);
    
    // 睡眠指定微秒数
    static void SleepUs(int microseconds);
    
    // 格式化时间戳为字符串
    static std::string FormatTimestamp(uint64_t timestamp);
    
    // 解析时间字符串为时间戳
    static uint64_t ParseTimestamp(const std::string& time_str);
};

// 字符串工具类
class StringUtils {
public:
    // 分割字符串
    static std::vector<std::string> Split(const std::string& str, char delimiter);
    static std::vector<std::string> Split(const std::string& str, const std::string& delimiter);
    
    // 去除首尾空白字符
    static std::string Trim(const std::string& str);
    static std::string TrimLeft(const std::string& str);
    static std::string TrimRight(const std::string& str);
    
    // 转换大小写
    static std::string ToUpper(const std::string& str);
    static std::string ToLower(const std::string& str);
    
    // 检查字符串是否以指定前缀开始
    static bool StartsWith(const std::string& str, const std::string& prefix);
    
    // 检查字符串是否以指定后缀结束
    static bool EndsWith(const std::string& str, const std::string& suffix);
    
    // 替换字符串中的所有匹配项
    static std::string Replace(const std::string& str, const std::string& from, const std::string& to);
    
    // 格式化字符串
    static std::string Format(const char* format, ...);
    
    // 十六进制字符串转换
    static std::string ToHexString(const uint8_t* data, size_t length);
    static std::vector<uint8_t> FromHexString(const std::string& hex_str);
};

// 数值转换工具类
class ConvertUtils {
public:
    // 字符串转数值
    static bool ToInt(const std::string& str, int& value);
    static bool ToUInt16(const std::string& str, uint16_t& value);
    static bool ToUInt32(const std::string& str, uint32_t& value);
    static bool ToDouble(const std::string& str, double& value);
    static bool ToBool(const std::string& str, bool& value);
    
    // 数值转字符串
    static std::string ToString(int value);
    static std::string ToString(uint16_t value);
    static std::string ToString(uint32_t value);
    static std::string ToString(double value, int precision = 2);
    static std::string ToString(bool value);
    
    // 字节序转换
    static uint16_t SwapBytes16(uint16_t value);
    static uint32_t SwapBytes32(uint32_t value);
    static float SwapBytesFloat(float value);
    
    // 数据类型转换
    static uint16_t BytesToUInt16(const uint8_t* bytes, bool big_endian = true);
    static uint32_t BytesToUInt32(const uint8_t* bytes, bool big_endian = true);
    static float BytesToFloat(const uint8_t* bytes, bool big_endian = true);
    
    static void UInt16ToBytes(uint16_t value, uint8_t* bytes, bool big_endian = true);
    static void UInt32ToBytes(uint32_t value, uint8_t* bytes, bool big_endian = true);
    static void FloatToBytes(float value, uint8_t* bytes, bool big_endian = true);
};

// 文件工具类
class FileUtils {
public:
    // 检查文件是否存在
    static bool FileExists(const std::string& filename);
    
    // 检查目录是否存在
    static bool DirectoryExists(const std::string& dirname);
    
    // 创建目录
    static bool CreateDirectory(const std::string& dirname);
    
    // 读取文件内容
    static bool ReadFile(const std::string& filename, std::string& content);
    
    // 写入文件内容
    static bool WriteFile(const std::string& filename, const std::string& content);
    
    // 获取文件大小
    static size_t GetFileSize(const std::string& filename);
    
    // 获取文件扩展名
    static std::string GetFileExtension(const std::string& filename);
    
    // 获取文件名 (不包含路径)
    static std::string GetFileName(const std::string& filepath);
    
    // 获取目录路径
    static std::string GetDirectoryPath(const std::string& filepath);
};

// 配置解析工具类
class ConfigUtils {
public:
    // 解析INI格式的配置
    static bool ParseIniFile(const std::string& filename, 
                            std::map<std::string, std::map<std::string, std::string>>& config);
    
    // 解析点表配置字符串
    static bool ParsePointString(const std::string& point_str, 
                                uint8_t& function_code,
                                uint16_t& start_address,
                                uint16_t& count,
                                uint8_t& slave_id,
                                uint8_t& data_type,
                                uint8_t& reserved);
    
    // 解析通信参数
    static bool ParseCommParam(const std::string& param_str, 
                              std::map<std::string, std::string>& params);
};

// CRC校验工具类
class CrcUtils {
public:
    // 计算CRC16 (Modbus标准)
    static uint16_t CalculateCrc16(const uint8_t* data, size_t length);
    
    // 验证CRC16
    static bool VerifyCrc16(const uint8_t* data, size_t length, uint16_t expected_crc);
    
    // 添加CRC16到数据末尾
    static void AppendCrc16(std::vector<uint8_t>& data);
};

// 数据验证工具类
class ValidationUtils {
public:
    // 验证IP地址格式
    static bool IsValidIpAddress(const std::string& ip);
    
    // 验证端口号
    static bool IsValidPort(int port);
    
    // 验证Modbus地址
    static bool IsValidModbusAddress(uint16_t address);
    
    // 验证Modbus功能码
    static bool IsValidFunctionCode(uint8_t function_code);
    
    // 验证从站ID
    static bool IsValidSlaveId(uint8_t slave_id);
    
    // 验证数据类型
    static bool IsValidDataType(uint8_t data_type);
};

// 性能统计工具类
class PerformanceUtils {
public:
    // 简单的性能计时器
    class Timer {
    public:
        Timer();
        void Reset();
        uint64_t ElapsedMs() const;
        uint64_t ElapsedUs() const;
        
    private:
        std::chrono::high_resolution_clock::time_point start_time_;
    };
    
    // 移动平均计算器
    class MovingAverage {
    public:
        explicit MovingAverage(size_t window_size);
        void AddValue(double value);
        double GetAverage() const;
        void Reset();
        
    private:
        std::vector<double> values_;
        size_t window_size_;
        size_t current_index_;
        bool is_full_;
    };
};

} // namespace utils
} // namespace modbus
