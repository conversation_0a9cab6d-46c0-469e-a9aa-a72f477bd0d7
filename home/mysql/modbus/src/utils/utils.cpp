#include "utils.h"
#include <sstream>
#include <algorithm>
#include <cctype>
#include <chrono>
#include <thread>
#include <cstdarg>
#include <iomanip>

namespace modbus {
namespace utils {

// 初始化静态成员
std::chrono::steady_clock::time_point TimeUtils::start_time_ = std::chrono::steady_clock::now();

// StringUtils 实现
std::vector<std::string> StringUtils::Split(const std::string& str, const std::string& delimiter) {
    std::vector<std::string> tokens;
    size_t start = 0;
    size_t end = str.find(delimiter);
    
    while (end != std::string::npos) {
        tokens.push_back(str.substr(start, end - start));
        start = end + delimiter.length();
        end = str.find(delimiter, start);
    }
    
    tokens.push_back(str.substr(start));
    return tokens;
}

std::string StringUtils::Trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) {
        return "";
    }
    
    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

std::string StringUtils::ToUpper(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::toupper);
    return result;
}

std::string StringUtils::ToLower(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

std::string StringUtils::Replace(const std::string& str, const std::string& from, const std::string& to) {
    std::string result = str;
    size_t pos = 0;
    
    while ((pos = result.find(from, pos)) != std::string::npos) {
        result.replace(pos, from.length(), to);
        pos += to.length();
    }
    
    return result;
}

bool StringUtils::StartsWith(const std::string& str, const std::string& prefix) {
    return str.length() >= prefix.length() && 
           str.compare(0, prefix.length(), prefix) == 0;
}

bool StringUtils::EndsWith(const std::string& str, const std::string& suffix) {
    return str.length() >= suffix.length() && 
           str.compare(str.length() - suffix.length(), suffix.length(), suffix) == 0;
}

int StringUtils::ToInt(const std::string& str, int default_value) {
    try {
        return std::stoi(str);
    } catch (const std::exception&) {
        return default_value;
    }
}

double StringUtils::ToDouble(const std::string& str, double default_value) {
    try {
        return std::stod(str);
    } catch (const std::exception&) {
        return default_value;
    }
}

bool StringUtils::ToBool(const std::string& str, bool default_value) {
    std::string lower_str = ToLower(Trim(str));
    
    if (lower_str == "true" || lower_str == "1" || lower_str == "yes" || lower_str == "on") {
        return true;
    } else if (lower_str == "false" || lower_str == "0" || lower_str == "no" || lower_str == "off") {
        return false;
    }
    
    return default_value;
}

std::string StringUtils::ToString(int value) {
    return std::to_string(value);
}

std::string StringUtils::ToString(double value, int precision) {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(precision) << value;
    return oss.str();
}

std::string StringUtils::ToString(bool value) {
    return value ? "true" : "false";
}

std::string StringUtils::Format(const char* format, ...) {
    va_list args;
    va_start(args, format);

    // 计算需要的缓冲区大小
    va_list args_copy;
    va_copy(args_copy, args);
    int size = vsnprintf(nullptr, 0, format, args_copy);
    va_end(args_copy);

    if (size < 0) {
        va_end(args);
        return "";
    }

    // 分配缓冲区并格式化
    std::vector<char> buffer(size + 1);
    vsnprintf(buffer.data(), buffer.size(), format, args);
    va_end(args);

    return std::string(buffer.data());
}

// TimeUtils 实现
uint64_t TimeUtils::GetCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
}

uint64_t TimeUtils::GetCurrentTimestampSec() {
    auto now = std::chrono::system_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::seconds>(duration).count();
}

std::string TimeUtils::TimestampToString(uint64_t timestamp, const std::string& format) {
    auto time_point = std::chrono::system_clock::from_time_t(timestamp / 1000);
    auto time_t = std::chrono::system_clock::to_time_t(time_point);
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), format.c_str());
    return oss.str();
}

void TimeUtils::SleepMs(int milliseconds) {
    std::this_thread::sleep_for(std::chrono::milliseconds(milliseconds));
}

void TimeUtils::SleepSec(int seconds) {
    std::this_thread::sleep_for(std::chrono::seconds(seconds));
}

uint64_t TimeUtils::GetElapsedTime() {
    auto now = std::chrono::steady_clock::now();
    auto duration = now - start_time_;
    return std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
}

// DataUtils 实现
uint16_t DataUtils::SwapBytes16(uint16_t value) {
    return ((value & 0xFF) << 8) | ((value >> 8) & 0xFF);
}

uint32_t DataUtils::SwapBytes32(uint32_t value) {
    return ((value & 0xFF) << 24) | 
           (((value >> 8) & 0xFF) << 16) | 
           (((value >> 16) & 0xFF) << 8) | 
           ((value >> 24) & 0xFF);
}

uint16_t DataUtils::ToBigEndian16(uint16_t value) {
    // 检查系统字节序
    static const uint16_t test = 0x1234;
    static const bool is_little_endian = (*reinterpret_cast<const uint8_t*>(&test) == 0x34);
    
    return is_little_endian ? SwapBytes16(value) : value;
}

uint32_t DataUtils::ToBigEndian32(uint32_t value) {
    static const uint32_t test = 0x12345678;
    static const bool is_little_endian = (*reinterpret_cast<const uint8_t*>(&test) == 0x78);
    
    return is_little_endian ? SwapBytes32(value) : value;
}

uint16_t DataUtils::ToLittleEndian16(uint16_t value) {
    static const uint16_t test = 0x1234;
    static const bool is_big_endian = (*reinterpret_cast<const uint8_t*>(&test) == 0x12);
    
    return is_big_endian ? SwapBytes16(value) : value;
}

uint16_t DataUtils::FromBigEndian16(uint16_t value) {
    return ToBigEndian16(value);  // 转换是对称的
}

uint16_t DataUtils::FromLittleEndian16(uint16_t value) {
    return ToLittleEndian16(value);  // 转换是对称的
}

std::vector<uint8_t> DataUtils::ToBytes(uint16_t value, bool big_endian) {
    std::vector<uint8_t> bytes(2);
    
    if (big_endian) {
        bytes[0] = (value >> 8) & 0xFF;
        bytes[1] = value & 0xFF;
    } else {
        bytes[0] = value & 0xFF;
        bytes[1] = (value >> 8) & 0xFF;
    }
    
    return bytes;
}

uint16_t DataUtils::FromBytes16(const std::vector<uint8_t>& bytes, bool big_endian) {
    if (bytes.size() < 2) {
        return 0;
    }
    
    if (big_endian) {
        return (static_cast<uint16_t>(bytes[0]) << 8) | bytes[1];
    } else {
        return (static_cast<uint16_t>(bytes[1]) << 8) | bytes[0];
    }
}

std::string DataUtils::ToHexString(const std::vector<uint8_t>& data) {
    std::ostringstream oss;
    oss << std::hex << std::uppercase;
    
    for (size_t i = 0; i < data.size(); ++i) {
        if (i > 0) oss << " ";
        oss << std::setw(2) << std::setfill('0') << static_cast<int>(data[i]);
    }
    
    return oss.str();
}

// ConfigUtils 实现
bool ConfigUtils::ParseKeyValue(const std::string& line, std::string& key, std::string& value, char delimiter) {
    size_t pos = line.find(delimiter);
    if (pos == std::string::npos) {
        return false;
    }
    
    key = StringUtils::Trim(line.substr(0, pos));
    value = StringUtils::Trim(line.substr(pos + 1));
    
    return !key.empty();
}

std::vector<std::string> ConfigUtils::ParseCommaSeparated(const std::string& value) {
    return StringUtils::Split(value, ",");
}

std::vector<int> ConfigUtils::ParseIntList(const std::string& value, char delimiter) {
    std::vector<int> result;
    auto parts = StringUtils::Split(value, std::string(1, delimiter));
    
    for (const auto& part : parts) {
        std::string trimmed = StringUtils::Trim(part);
        if (!trimmed.empty()) {
            result.push_back(StringUtils::ToInt(trimmed, 0));
        }
    }
    
    return result;
}

bool ConfigUtils::ValidateRange(int value, int min_val, int max_val) {
    return value >= min_val && value <= max_val;
}

bool ConfigUtils::ValidateRange(double value, double min_val, double max_val) {
    return value >= min_val && value <= max_val;
}

} // namespace utils
} // namespace modbus
