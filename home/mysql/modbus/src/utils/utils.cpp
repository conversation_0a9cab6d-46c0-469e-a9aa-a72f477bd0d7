#include "utils.h"
#include <sstream>
#include <algorithm>
#include <cctype>
#include <cstdarg>
#include <fstream>
#include <regex>
#include <sys/stat.h>
#include <sys/types.h>

namespace modbus {
namespace utils {

// TimeUtils 实现
uint64_t TimeUtils::GetCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
}

uint64_t TimeUtils::GetCurrentTimestampUs() {
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
}

void TimeUtils::SleepMs(int milliseconds) {
    std::this_thread::sleep_for(std::chrono::milliseconds(milliseconds));
}

void TimeUtils::SleepUs(int microseconds) {
    std::this_thread::sleep_for(std::chrono::microseconds(microseconds));
}

std::string TimeUtils::FormatTimestamp(uint64_t timestamp) {
    auto time_point = std::chrono::system_clock::from_time_t(timestamp / 1000);
    auto time_t = std::chrono::system_clock::to_time_t(time_point);
    auto tm = *std::localtime(&time_t);
    
    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << (timestamp % 1000);
    return oss.str();
}

uint64_t TimeUtils::ParseTimestamp(const std::string& time_str) {
    // 简单实现，实际项目中可能需要更复杂的解析
    return GetCurrentTimestamp();
}

// StringUtils 实现
std::vector<std::string> StringUtils::Split(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::stringstream ss(str);
    std::string token;
    
    while (std::getline(ss, token, delimiter)) {
        tokens.push_back(token);
    }
    
    return tokens;
}

std::vector<std::string> StringUtils::Split(const std::string& str, const std::string& delimiter) {
    std::vector<std::string> tokens;
    size_t start = 0;
    size_t end = str.find(delimiter);
    
    while (end != std::string::npos) {
        tokens.push_back(str.substr(start, end - start));
        start = end + delimiter.length();
        end = str.find(delimiter, start);
    }
    
    tokens.push_back(str.substr(start));
    return tokens;
}

std::string StringUtils::Trim(const std::string& str) {
    return TrimLeft(TrimRight(str));
}

std::string StringUtils::TrimLeft(const std::string& str) {
    auto start = str.begin();
    while (start != str.end() && std::isspace(*start)) {
        start++;
    }
    return std::string(start, str.end());
}

std::string StringUtils::TrimRight(const std::string& str) {
    auto end = str.end();
    while (end != str.begin() && std::isspace(*(end - 1))) {
        end--;
    }
    return std::string(str.begin(), end);
}

std::string StringUtils::ToUpper(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::toupper);
    return result;
}

std::string StringUtils::ToLower(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

bool StringUtils::StartsWith(const std::string& str, const std::string& prefix) {
    return str.length() >= prefix.length() && 
           str.compare(0, prefix.length(), prefix) == 0;
}

bool StringUtils::EndsWith(const std::string& str, const std::string& suffix) {
    return str.length() >= suffix.length() && 
           str.compare(str.length() - suffix.length(), suffix.length(), suffix) == 0;
}

std::string StringUtils::Replace(const std::string& str, const std::string& from, const std::string& to) {
    std::string result = str;
    size_t start_pos = 0;
    while ((start_pos = result.find(from, start_pos)) != std::string::npos) {
        result.replace(start_pos, from.length(), to);
        start_pos += to.length();
    }
    return result;
}

std::string StringUtils::Format(const char* format, ...) {
    va_list args;
    va_start(args, format);
    
    char buffer[4096];
    vsnprintf(buffer, sizeof(buffer), format, args);
    
    va_end(args);
    
    return std::string(buffer);
}

std::string StringUtils::ToHexString(const uint8_t* data, size_t length) {
    std::ostringstream oss;
    oss << std::hex << std::setfill('0');
    for (size_t i = 0; i < length; ++i) {
        oss << std::setw(2) << static_cast<int>(data[i]);
    }
    return oss.str();
}

std::vector<uint8_t> StringUtils::FromHexString(const std::string& hex_str) {
    std::vector<uint8_t> result;
    for (size_t i = 0; i < hex_str.length(); i += 2) {
        std::string byte_str = hex_str.substr(i, 2);
        uint8_t byte = static_cast<uint8_t>(std::stoul(byte_str, nullptr, 16));
        result.push_back(byte);
    }
    return result;
}

// ConvertUtils 实现
bool ConvertUtils::ToInt(const std::string& str, int& value) {
    try {
        value = std::stoi(str);
        return true;
    } catch (...) {
        return false;
    }
}

bool ConvertUtils::ToUInt16(const std::string& str, uint16_t& value) {
    try {
        unsigned long temp = std::stoul(str);
        if (temp > UINT16_MAX) return false;
        value = static_cast<uint16_t>(temp);
        return true;
    } catch (...) {
        return false;
    }
}

bool ConvertUtils::ToUInt32(const std::string& str, uint32_t& value) {
    try {
        value = std::stoul(str);
        return true;
    } catch (...) {
        return false;
    }
}

bool ConvertUtils::ToDouble(const std::string& str, double& value) {
    try {
        value = std::stod(str);
        return true;
    } catch (...) {
        return false;
    }
}

bool ConvertUtils::ToBool(const std::string& str, bool& value) {
    std::string lower_str = StringUtils::ToLower(StringUtils::Trim(str));
    if (lower_str == "true" || lower_str == "1" || lower_str == "yes" || lower_str == "on") {
        value = true;
        return true;
    } else if (lower_str == "false" || lower_str == "0" || lower_str == "no" || lower_str == "off") {
        value = false;
        return true;
    }
    return false;
}

std::string ConvertUtils::ToString(int value) {
    return std::to_string(value);
}

std::string ConvertUtils::ToString(uint16_t value) {
    return std::to_string(value);
}

std::string ConvertUtils::ToString(uint32_t value) {
    return std::to_string(value);
}

std::string ConvertUtils::ToString(double value, int precision) {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(precision) << value;
    return oss.str();
}

std::string ConvertUtils::ToString(bool value) {
    return value ? "true" : "false";
}

uint16_t ConvertUtils::SwapBytes16(uint16_t value) {
    return ((value & 0xFF) << 8) | ((value >> 8) & 0xFF);
}

uint32_t ConvertUtils::SwapBytes32(uint32_t value) {
    return ((value & 0xFF) << 24) | 
           (((value >> 8) & 0xFF) << 16) | 
           (((value >> 16) & 0xFF) << 8) | 
           ((value >> 24) & 0xFF);
}

float ConvertUtils::SwapBytesFloat(float value) {
    union { float f; uint32_t i; } u;
    u.f = value;
    u.i = SwapBytes32(u.i);
    return u.f;
}

uint16_t ConvertUtils::BytesToUInt16(const uint8_t* bytes, bool big_endian) {
    if (big_endian) {
        return (static_cast<uint16_t>(bytes[0]) << 8) | bytes[1];
    } else {
        return (static_cast<uint16_t>(bytes[1]) << 8) | bytes[0];
    }
}

uint32_t ConvertUtils::BytesToUInt32(const uint8_t* bytes, bool big_endian) {
    if (big_endian) {
        return (static_cast<uint32_t>(bytes[0]) << 24) |
               (static_cast<uint32_t>(bytes[1]) << 16) |
               (static_cast<uint32_t>(bytes[2]) << 8) |
               bytes[3];
    } else {
        return (static_cast<uint32_t>(bytes[3]) << 24) |
               (static_cast<uint32_t>(bytes[2]) << 16) |
               (static_cast<uint32_t>(bytes[1]) << 8) |
               bytes[0];
    }
}

float ConvertUtils::BytesToFloat(const uint8_t* bytes, bool big_endian) {
    union { float f; uint32_t i; } u;
    u.i = BytesToUInt32(bytes, big_endian);
    return u.f;
}

void ConvertUtils::UInt16ToBytes(uint16_t value, uint8_t* bytes, bool big_endian) {
    if (big_endian) {
        bytes[0] = static_cast<uint8_t>(value >> 8);
        bytes[1] = static_cast<uint8_t>(value & 0xFF);
    } else {
        bytes[0] = static_cast<uint8_t>(value & 0xFF);
        bytes[1] = static_cast<uint8_t>(value >> 8);
    }
}

void ConvertUtils::UInt32ToBytes(uint32_t value, uint8_t* bytes, bool big_endian) {
    if (big_endian) {
        bytes[0] = static_cast<uint8_t>(value >> 24);
        bytes[1] = static_cast<uint8_t>((value >> 16) & 0xFF);
        bytes[2] = static_cast<uint8_t>((value >> 8) & 0xFF);
        bytes[3] = static_cast<uint8_t>(value & 0xFF);
    } else {
        bytes[0] = static_cast<uint8_t>(value & 0xFF);
        bytes[1] = static_cast<uint8_t>((value >> 8) & 0xFF);
        bytes[2] = static_cast<uint8_t>((value >> 16) & 0xFF);
        bytes[3] = static_cast<uint8_t>(value >> 24);
    }
}

void ConvertUtils::FloatToBytes(float value, uint8_t* bytes, bool big_endian) {
    union { float f; uint32_t i; } u;
    u.f = value;
    UInt32ToBytes(u.i, bytes, big_endian);
}

} // namespace utils
} // namespace modbus
