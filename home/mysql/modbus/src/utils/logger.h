#ifndef LOGGER_H
#define LOGGER_H

#include <string>
#include <memory>
#include <mutex>
#include <thread>
#include <queue>
#include <condition_variable>
#include <atomic>
#include <chrono>
#include <cstdio>

namespace modbus {

// 日志级别
enum class LogLevel {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    WARN = 3,
    ERROR = 4,
    FATAL = 5
};

// 日志输出目标
enum class LogTarget {
    CONSOLE = 1,
    FILE = 2,
    BOTH = 3
};

// 日志记录结构
struct LogRecord {
    LogLevel level;
    std::string message;
    std::string file;
    int line;
    std::string function;
    std::thread::id thread_id;
    uint64_t timestamp;
    
    LogRecord() : level(LogLevel::INFO), line(0), timestamp(0) {}
};

// 日志格式化器接口
class LogFormatter {
public:
    virtual ~LogFormatter() = default;
    virtual std::string Format(const LogRecord& record) = 0;
};

// 默认日志格式化器
class DefaultLogFormatter : public LogFormatter {
public:
    std::string Format(const LogRecord& record) override;
    
private:
    std::string LevelToString(LogLevel level);
    std::string FormatTimestamp(uint64_t timestamp);
};

// 日志输出器接口
class LogAppender {
public:
    virtual ~LogAppender() = default;
    virtual void Append(const std::string& formatted_message) = 0;
    virtual void Flush() = 0;
};

// 控制台输出器
class ConsoleAppender : public LogAppender {
public:
    void Append(const std::string& formatted_message) override;
    void Flush() override;
};

// 文件输出器
class FileAppender : public LogAppender {
public:
    explicit FileAppender(const std::string& filename);
    ~FileAppender();
    
    void Append(const std::string& formatted_message) override;
    void Flush() override;
    
    bool IsOpen() const;
    void Rotate();  // 日志轮转
    
private:
    std::string filename_;
    FILE* file_;
    std::mutex file_mutex_;
    size_t max_file_size_;
    int max_file_count_;
    size_t current_size_;
};

// 异步日志器
class Logger {
public:
    static Logger& GetInstance();
    
    // 初始化日志系统
    void Initialize(LogLevel level = LogLevel::INFO, 
                   LogTarget target = LogTarget::BOTH,
                   const std::string& log_file = "modbus.log");
    
    // 关闭日志系统
    void Shutdown();
    
    // 设置日志级别
    void SetLevel(LogLevel level) { level_ = level; }
    LogLevel GetLevel() const { return level_; }
    
    // 设置格式化器
    void SetFormatter(std::unique_ptr<LogFormatter> formatter);
    
    // 添加输出器
    void AddAppender(std::unique_ptr<LogAppender> appender);
    void ClearAppenders();
    
    // 记录日志
    void Log(LogLevel level, const std::string& message, 
             const std::string& file = "", int line = 0, 
             const std::string& function = "");
    
    // 便捷方法
    void Trace(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    void Debug(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    void Info(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    void Warn(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    void Error(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    void Fatal(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    
    // 格式化日志
    template<typename... Args>
    void LogFormat(LogLevel level, const std::string& format, Args&&... args);
    
    template<typename... Args>
    void TraceFormat(const std::string& format, Args&&... args);
    
    template<typename... Args>
    void DebugFormat(const std::string& format, Args&&... args);
    
    template<typename... Args>
    void InfoFormat(const std::string& format, Args&&... args);
    
    template<typename... Args>
    void WarnFormat(const std::string& format, Args&&... args);
    
    template<typename... Args>
    void ErrorFormat(const std::string& format, Args&&... args);
    
    template<typename... Args>
    void FatalFormat(const std::string& format, Args&&... args);
    
    // 刷新所有输出器
    void Flush();
    
private:
    Logger() = default;
    ~Logger();
    
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;
    
    void WorkerThread();
    void ProcessRecord(const LogRecord& record);
    std::string FormatMessage(const std::string& format, ...);
    
private:
    std::atomic<LogLevel> level_{LogLevel::INFO};
    std::unique_ptr<LogFormatter> formatter_;
    std::vector<std::unique_ptr<LogAppender>> appenders_;
    
    // 异步处理
    std::queue<LogRecord> log_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_condition_;
    std::thread worker_thread_;
    std::atomic<bool> running_{false};
    
    // 输出器保护
    std::mutex appenders_mutex_;
};

// 模板方法实现
template<typename... Args>
void Logger::LogFormat(LogLevel level, const std::string& format, Args&&... args) {
    if (level < level_) return;

    char buffer[4096];
    snprintf(buffer, sizeof(buffer), format.c_str(), std::forward<Args>(args)...);
    Log(level, std::string(buffer));
}

// 特化版本处理无参数情况
template<>
inline void Logger::LogFormat<>(LogLevel level, const std::string& format) {
    if (level < level_) return;
    Log(level, format);
}

template<typename... Args>
void Logger::TraceFormat(const std::string& format, Args&&... args) {
    LogFormat(LogLevel::TRACE, format, std::forward<Args>(args)...);
}

template<typename... Args>
void Logger::DebugFormat(const std::string& format, Args&&... args) {
    LogFormat(LogLevel::DEBUG, format, std::forward<Args>(args)...);
}

template<typename... Args>
void Logger::InfoFormat(const std::string& format, Args&&... args) {
    LogFormat(LogLevel::INFO, format, std::forward<Args>(args)...);
}

template<typename... Args>
void Logger::WarnFormat(const std::string& format, Args&&... args) {
    LogFormat(LogLevel::WARN, format, std::forward<Args>(args)...);
}

template<typename... Args>
void Logger::ErrorFormat(const std::string& format, Args&&... args) {
    LogFormat(LogLevel::ERROR, format, std::forward<Args>(args)...);
}

template<typename... Args>
void Logger::FatalFormat(const std::string& format, Args&&... args) {
    LogFormat(LogLevel::FATAL, format, std::forward<Args>(args)...);
}

} // namespace modbus

// 兼容现有日志接口的宏定义
#define WRITE_TRACE_LOG(format, ...) \
    modbus::Logger::GetInstance().TraceFormat(format, ##__VA_ARGS__)

#define WRITE_DEBUG_LOG(format, ...) \
    modbus::Logger::GetInstance().DebugFormat(format, ##__VA_ARGS__)

#define WRITE_INFO_LOG(format, ...) \
    modbus::Logger::GetInstance().InfoFormat(format, ##__VA_ARGS__)

#define WRITE_WARN_LOG(format, ...) \
    modbus::Logger::GetInstance().WarnFormat(format, ##__VA_ARGS__)

#define WRITE_ERROR_LOG(format, ...) \
    modbus::Logger::GetInstance().ErrorFormat(format, ##__VA_ARGS__)

#define WRITE_FATAL_LOG(format, ...) \
    modbus::Logger::GetInstance().FatalFormat(format, ##__VA_ARGS__)

// 带文件信息的日志宏
#define LOG_TRACE(message) \
    modbus::Logger::GetInstance().Trace(message, __FILE__, __LINE__, __FUNCTION__)

#define LOG_DEBUG(message) \
    modbus::Logger::GetInstance().Debug(message, __FILE__, __LINE__, __FUNCTION__)

#define LOG_INFO(message) \
    modbus::Logger::GetInstance().Info(message, __FILE__, __LINE__, __FUNCTION__)

#define LOG_WARN(message) \
    modbus::Logger::GetInstance().Warn(message, __FILE__, __LINE__, __FUNCTION__)

#define LOG_ERROR(message) \
    modbus::Logger::GetInstance().Error(message, __FILE__, __LINE__, __FUNCTION__)

#define LOG_FATAL(message) \
    modbus::Logger::GetInstance().Fatal(message, __FILE__, __LINE__, __FUNCTION__)

#endif // LOGGER_H
