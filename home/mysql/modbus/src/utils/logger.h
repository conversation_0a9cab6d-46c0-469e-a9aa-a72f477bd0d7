#pragma once

#include <string>
#include <fstream>
#include <mutex>
#include <memory>
#include <sstream>

namespace modbus {

// 日志级别
enum class LogLevel {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    WARN = 3,
    ERROR = 4,
    FATAL = 5
};

// 日志输出目标
enum class LogTarget {
    CONSOLE = 0,
    FILE = 1,
    BOTH = 2
};

// 日志器类 (单例模式)
class Logger {
public:
    static Logger& GetInstance();
    
    // 初始化日志系统
    bool Initialize(LogLevel level = LogLevel::INFO, 
                   LogTarget target = LogTarget::BOTH,
                   const std::string& filename = "modbus_service.log");
    
    // 关闭日志系统
    void Shutdown();
    
    // 写入日志
    void WriteLog(LogLevel level, const char* file, int line, 
                  const char* func, const char* format, ...);
    
    // 设置日志级别
    void SetLogLevel(LogLevel level) { log_level_ = level; }
    
    // 获取日志级别
    LogLevel GetLogLevel() const { return log_level_; }
    
    // 检查是否应该记录指定级别的日志
    bool ShouldLog(LogLevel level) const { return level >= log_level_; }
    
private:
    Logger() = default;
    ~Logger();
    
    // 禁止拷贝和赋值
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;
    
    // 获取日志级别字符串
    const char* GetLevelString(LogLevel level) const;
    
    // 获取当前时间字符串
    std::string GetCurrentTimeString() const;
    
    // 实际写入日志的函数
    void WriteLogInternal(LogLevel level, const char* file, int line,
                         const char* func, const std::string& message);
    
private:
    LogLevel log_level_ = LogLevel::INFO;
    LogTarget log_target_ = LogTarget::BOTH;
    std::string log_filename_;
    std::unique_ptr<std::ofstream> log_file_;
    std::mutex log_mutex_;
    bool initialized_ = false;
};

} // namespace modbus

// 日志宏定义
#define LOG_TRACE(format, ...) \
    do { \
        if (modbus::Logger::GetInstance().ShouldLog(modbus::LogLevel::TRACE)) { \
            modbus::Logger::GetInstance().WriteLog(modbus::LogLevel::TRACE, \
                __FILE__, __LINE__, __FUNCTION__, format, ##__VA_ARGS__); \
        } \
    } while(0)

#define LOG_DEBUG(format, ...) \
    do { \
        if (modbus::Logger::GetInstance().ShouldLog(modbus::LogLevel::DEBUG)) { \
            modbus::Logger::GetInstance().WriteLog(modbus::LogLevel::DEBUG, \
                __FILE__, __LINE__, __FUNCTION__, format, ##__VA_ARGS__); \
        } \
    } while(0)

#define LOG_INFO(format, ...) \
    do { \
        if (modbus::Logger::GetInstance().ShouldLog(modbus::LogLevel::INFO)) { \
            modbus::Logger::GetInstance().WriteLog(modbus::LogLevel::INFO, \
                __FILE__, __LINE__, __FUNCTION__, format, ##__VA_ARGS__); \
        } \
    } while(0)

#define LOG_WARN(format, ...) \
    do { \
        if (modbus::Logger::GetInstance().ShouldLog(modbus::LogLevel::WARN)) { \
            modbus::Logger::GetInstance().WriteLog(modbus::LogLevel::WARN, \
                __FILE__, __LINE__, __FUNCTION__, format, ##__VA_ARGS__); \
        } \
    } while(0)

#define LOG_ERROR(format, ...) \
    do { \
        if (modbus::Logger::GetInstance().ShouldLog(modbus::LogLevel::ERROR)) { \
            modbus::Logger::GetInstance().WriteLog(modbus::LogLevel::ERROR, \
                __FILE__, __LINE__, __FUNCTION__, format, ##__VA_ARGS__); \
        } \
    } while(0)

#define LOG_FATAL(format, ...) \
    do { \
        modbus::Logger::GetInstance().WriteLog(modbus::LogLevel::FATAL, \
            __FILE__, __LINE__, __FUNCTION__, format, ##__VA_ARGS__); \
    } while(0)

// 简化的日志宏
#define WRITE_TRACE_LOG(format, ...) LOG_TRACE(format, ##__VA_ARGS__)
#define WRITE_DEBUG_LOG(format, ...) LOG_DEBUG(format, ##__VA_ARGS__)
#define WRITE_INFO_LOG(format, ...) LOG_INFO(format, ##__VA_ARGS__)
#define WRITE_WARN_LOG(format, ...) LOG_WARN(format, ##__VA_ARGS__)
#define WRITE_ERROR_LOG(format, ...) LOG_ERROR(format, ##__VA_ARGS__)
#define WRITE_FATAL_LOG(format, ...) LOG_FATAL(format, ##__VA_ARGS__)
