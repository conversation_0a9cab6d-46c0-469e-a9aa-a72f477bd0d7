#include "logger.h"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <chrono>
#include <cstdarg>
#include <cstdio>
#include <filesystem>

namespace modbus {

// DefaultLogFormatter 实现
std::string DefaultLogFormatter::Format(const LogRecord& record) {
    std::ostringstream oss;
    
    // 时间戳
    oss << "[" << FormatTimestamp(record.timestamp) << "] ";
    
    // 日志级别
    oss << "[" << LevelToString(record.level) << "] ";
    
    // 线程ID
    oss << "[" << record.thread_id << "] ";
    
    // 文件和行号
    if (!record.file.empty()) {
        std::filesystem::path file_path(record.file);
        oss << "[" << file_path.filename().string() << ":" << record.line << "] ";
    }
    
    // 函数名
    if (!record.function.empty()) {
        oss << "[" << record.function << "] ";
    }
    
    // 消息内容
    oss << record.message;
    
    return oss.str();
}

std::string DefaultLogFormatter::LevelToString(LogLevel level) {
    switch (level) {
        case LogLevel::TRACE: return "TRACE";
        case LogLevel::DEBUG: return "DEBUG";
        case LogLevel::INFO:  return "INFO ";
        case LogLevel::WARN:  return "WARN ";
        case LogLevel::ERROR: return "ERROR";
        case LogLevel::FATAL: return "FATAL";
        default: return "UNKNOWN";
    }
}

std::string DefaultLogFormatter::FormatTimestamp(uint64_t timestamp) {
    auto time_point = std::chrono::system_clock::from_time_t(timestamp / 1000);
    auto time_t = std::chrono::system_clock::to_time_t(time_point);
    auto ms = timestamp % 1000;
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << ms;
    
    return oss.str();
}

// ConsoleAppender 实现
void ConsoleAppender::Append(const std::string& formatted_message) {
    std::cout << formatted_message << std::endl;
}

void ConsoleAppender::Flush() {
    std::cout.flush();
}

// FileAppender 实现
FileAppender::FileAppender(const std::string& filename)
    : filename_(filename)
    , file_(nullptr)
    , max_file_size_(10 * 1024 * 1024)  // 10MB
    , max_file_count_(5)
    , current_size_(0) {

    file_ = fopen(filename_.c_str(), "a");
    if (file_) {
        fseek(file_, 0, SEEK_END);
        current_size_ = ftell(file_);
    }
}

FileAppender::~FileAppender() {
    if (file_) {
        fclose(file_);
    }
}

bool FileAppender::IsOpen() const {
    return file_ != nullptr;
}

void FileAppender::Append(const std::string& formatted_message) {
    std::lock_guard<std::mutex> lock(file_mutex_);

    if (!file_) {
        return;
    }

    fprintf(file_, "%s\n", formatted_message.c_str());
    fflush(file_);
    current_size_ += formatted_message.length() + 1;

    // 检查是否需要轮转
    if (current_size_ > max_file_size_) {
        Rotate();
    }
}

void FileAppender::Flush() {
    std::lock_guard<std::mutex> lock(file_mutex_);
    if (file_) {
        fflush(file_);
    }
}

void FileAppender::Rotate() {
    if (!file_) {
        return;
    }

    fclose(file_);
    file_ = nullptr;
    
    // 轮转文件
    for (int i = max_file_count_ - 1; i > 0; --i) {
        std::string old_name = filename_ + "." + std::to_string(i);
        std::string new_name = filename_ + "." + std::to_string(i + 1);
        
        if (std::filesystem::exists(old_name)) {
            if (i == max_file_count_ - 1) {
                std::filesystem::remove(new_name);
            }
            std::filesystem::rename(old_name, new_name);
        }
    }
    
    // 重命名当前文件
    std::string backup_name = filename_ + ".1";
    if (std::filesystem::exists(filename_)) {
        std::filesystem::rename(filename_, backup_name);
    }
    
    // 重新打开文件
    file_ = fopen(filename_.c_str(), "a");
    current_size_ = 0;
}

// Logger 实现
Logger& Logger::GetInstance() {
    static Logger instance;
    return instance;
}

Logger::~Logger() {
    Shutdown();
}

void Logger::Initialize(LogLevel level, LogTarget target, const std::string& log_file) {
    level_ = level;
    
    // 设置默认格式化器
    if (!formatter_) {
        formatter_ = std::make_unique<DefaultLogFormatter>();
    }
    
    // 清除现有输出器
    ClearAppenders();
    
    // 添加输出器
    if (target == LogTarget::CONSOLE || target == LogTarget::BOTH) {
        AddAppender(std::make_unique<ConsoleAppender>());
    }
    
    if (target == LogTarget::FILE || target == LogTarget::BOTH) {
        auto file_appender = std::make_unique<FileAppender>(log_file);
        if (file_appender->IsOpen()) {
            AddAppender(std::move(file_appender));
        }
    }
    
    // 启动工作线程
    if (!running_) {
        running_ = true;
        worker_thread_ = std::thread(&Logger::WorkerThread, this);
    }
}

void Logger::Shutdown() {
    if (running_) {
        running_ = false;
        queue_condition_.notify_all();
        
        if (worker_thread_.joinable()) {
            worker_thread_.join();
        }
        
        // 处理剩余的日志记录
        std::lock_guard<std::mutex> lock(queue_mutex_);
        while (!log_queue_.empty()) {
            ProcessRecord(log_queue_.front());
            log_queue_.pop();
        }
        
        Flush();
    }
}

void Logger::SetFormatter(std::unique_ptr<LogFormatter> formatter) {
    formatter_ = std::move(formatter);
}

void Logger::AddAppender(std::unique_ptr<LogAppender> appender) {
    std::lock_guard<std::mutex> lock(appenders_mutex_);
    appenders_.push_back(std::move(appender));
}

void Logger::ClearAppenders() {
    std::lock_guard<std::mutex> lock(appenders_mutex_);
    appenders_.clear();
}

void Logger::Log(LogLevel level, const std::string& message, 
                const std::string& file, int line, const std::string& function) {
    if (level < level_ || !running_) {
        return;
    }
    
    LogRecord record;
    record.level = level;
    record.message = message;
    record.file = file;
    record.line = line;
    record.function = function;
    record.thread_id = std::this_thread::get_id();
    record.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        log_queue_.push(record);
    }
    queue_condition_.notify_one();
}

void Logger::Trace(const std::string& message, const std::string& file, int line, const std::string& function) {
    Log(LogLevel::TRACE, message, file, line, function);
}

void Logger::Debug(const std::string& message, const std::string& file, int line, const std::string& function) {
    Log(LogLevel::DEBUG, message, file, line, function);
}

void Logger::Info(const std::string& message, const std::string& file, int line, const std::string& function) {
    Log(LogLevel::INFO, message, file, line, function);
}

void Logger::Warn(const std::string& message, const std::string& file, int line, const std::string& function) {
    Log(LogLevel::WARN, message, file, line, function);
}

void Logger::Error(const std::string& message, const std::string& file, int line, const std::string& function) {
    Log(LogLevel::ERROR, message, file, line, function);
}

void Logger::Fatal(const std::string& message, const std::string& file, int line, const std::string& function) {
    Log(LogLevel::FATAL, message, file, line, function);
}

void Logger::Flush() {
    std::lock_guard<std::mutex> lock(appenders_mutex_);
    for (auto& appender : appenders_) {
        appender->Flush();
    }
}

void Logger::WorkerThread() {
    while (running_) {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        queue_condition_.wait(lock, [this] { return !log_queue_.empty() || !running_; });
        
        while (!log_queue_.empty()) {
            LogRecord record = log_queue_.front();
            log_queue_.pop();
            lock.unlock();
            
            ProcessRecord(record);
            
            lock.lock();
        }
    }
}

void Logger::ProcessRecord(const LogRecord& record) {
    if (!formatter_) {
        return;
    }
    
    std::string formatted_message = formatter_->Format(record);
    
    std::lock_guard<std::mutex> lock(appenders_mutex_);
    for (auto& appender : appenders_) {
        appender->Append(formatted_message);
    }
}

} // namespace modbus
