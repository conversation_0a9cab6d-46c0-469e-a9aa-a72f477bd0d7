#include "logger.h"
#include <iostream>
#include <cstdarg>
#include <ctime>
#include <iomanip>
#include <cstring>

namespace modbus {

Logger& Logger::GetInstance() {
    static Logger instance;
    return instance;
}

Logger::~Logger() {
    Shutdown();
}

bool Logger::Initialize(LogLevel level, LogTarget target, const std::string& filename) {
    std::lock_guard<std::mutex> lock(log_mutex_);
    
    if (initialized_) {
        return true;
    }
    
    log_level_ = level;
    log_target_ = target;
    log_filename_ = filename;
    
    // 如果需要写入文件，打开日志文件
    if (log_target_ == LogTarget::FILE || log_target_ == LogTarget::BOTH) {
        log_file_ = std::make_unique<std::ofstream>(log_filename_, 
                                                   std::ios::out | std::ios::app);
        if (!log_file_->is_open()) {
            std::cerr << "Failed to open log file: " << log_filename_ << std::endl;
            return false;
        }
    }
    
    initialized_ = true;
    
    // 写入初始化日志
    WriteLogInternal(LogLevel::INFO, __FILE__, __LINE__, __FUNCTION__,
                    "Logger initialized successfully");
    
    return true;
}

void Logger::Shutdown() {
    std::lock_guard<std::mutex> lock(log_mutex_);
    
    if (!initialized_) {
        return;
    }
    
    WriteLogInternal(LogLevel::INFO, __FILE__, __LINE__, __FUNCTION__,
                    "Logger shutting down");
    
    if (log_file_ && log_file_->is_open()) {
        log_file_->close();
    }
    log_file_.reset();
    
    initialized_ = false;
}

void Logger::WriteLog(LogLevel level, const char* file, int line, 
                     const char* func, const char* format, ...) {
    if (!ShouldLog(level)) {
        return;
    }
    
    // 格式化消息
    va_list args;
    va_start(args, format);
    
    char buffer[4096];
    vsnprintf(buffer, sizeof(buffer), format, args);
    
    va_end(args);
    
    WriteLogInternal(level, file, line, func, std::string(buffer));
}

const char* Logger::GetLevelString(LogLevel level) const {
    switch (level) {
        case LogLevel::TRACE: return "TRACE";
        case LogLevel::DEBUG: return "DEBUG";
        case LogLevel::INFO:  return "INFO ";
        case LogLevel::WARN:  return "WARN ";
        case LogLevel::ERROR: return "ERROR";
        case LogLevel::FATAL: return "FATAL";
        default: return "UNKNOWN";
    }
}

std::string Logger::GetCurrentTimeString() const {
    auto now = std::time(nullptr);
    auto tm = *std::localtime(&now);
    
    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
    return oss.str();
}

void Logger::WriteLogInternal(LogLevel level, const char* file, int line,
                             const char* func, const std::string& message) {
    std::lock_guard<std::mutex> lock(log_mutex_);
    
    if (!initialized_) {
        return;
    }
    
    // 构建日志消息
    std::ostringstream oss;
    oss << "[" << GetCurrentTimeString() << "] "
        << "[" << GetLevelString(level) << "] ";
    
    // 提取文件名 (去掉路径)
    const char* filename = strrchr(file, '/');
    if (filename) {
        filename++;
    } else {
        filename = file;
    }
    
    oss << "[" << filename << ":" << line << "] "
        << "[" << func << "] "
        << message;
    
    std::string log_line = oss.str();
    
    // 输出到控制台
    if (log_target_ == LogTarget::CONSOLE || log_target_ == LogTarget::BOTH) {
        if (level >= LogLevel::ERROR) {
            std::cerr << log_line << std::endl;
        } else {
            std::cout << log_line << std::endl;
        }
    }
    
    // 输出到文件
    if ((log_target_ == LogTarget::FILE || log_target_ == LogTarget::BOTH) && 
        log_file_ && log_file_->is_open()) {
        *log_file_ << log_line << std::endl;
        log_file_->flush();
    }
}

} // namespace modbus
