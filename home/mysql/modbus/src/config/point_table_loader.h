#ifndef POINT_TABLE_LOADER_H
#define POINT_TABLE_LOADER_H

#include "../types/modbus_types.h"
#include "../utils/thread_pool.h"
#include "config_manager.h"
#include <functional>

namespace modbus {

// 前向声明
struct DataPointEx;

// 点表变化回调函数类型
using PointTableChangeCallback = std::function<void(const DataPointList& points)>;

// 点表配置
struct PointTableConfig {
    // 全局配置
    int yc_scan_interval = 1000;    // 遥测扫描间隔
    int yx_scan_interval = 500;     // 遥信扫描间隔
    int yt_scan_interval = 2000;    // 遥调扫描间隔
    int yk_scan_interval = 100;     // 遥控扫描间隔
    
    // 数据处理配置
    bool enable_data_validation = true;
    bool enable_change_detection = true;
    bool enable_data_filter = false;
    bool enable_alarm = true;
    
    // 默认限值
    double default_min_value = -999999.0;
    double default_max_value = 999999.0;
    double default_change_threshold = 0.01;
    double default_alarm_deadband = 0.05;
};

// 点表加载器
class PointTableLoader {
public:
    PointTableLoader() = default;
    ~PointTableLoader() = default;
    
    // 禁止拷贝和赋值
    PointTableLoader(const PointTableLoader&) = delete;
    PointTableLoader& operator=(const PointTableLoader&) = delete;
    
    // 加载点表文件
    Result<DataPointList> LoadPointTable(const std::string& point_file);
    
    // 重新加载点表
    Result<DataPointList> ReloadPointTable();
    
    // 获取扩展点表信息
    Result<std::vector<DataPointEx>> LoadPointTableEx(const std::string& point_file);
    
    // 获取点表配置
    Result<PointTableConfig> LoadPointTableConfig(const std::string& point_file);
    
    // 根据数据类型过滤点表
    DataPointList FilterByDataType(const DataPointList& points, DataType data_type) const;
    
    // 根据从站地址过滤点表
    DataPointList FilterBySlaveId(const DataPointList& points, int slave_id) const;
    
    // 验证点表
    Result<bool> ValidatePointTable(const DataPointList& points) const;
    
    // 设置点表变化回调
    void SetPointTableChangeCallback(PointTableChangeCallback callback);
    
    // 启用点表文件监控
    void EnablePointTableMonitoring(bool enable = true);
    
    // 获取当前点表文件路径
    std::string GetPointTableFile() const { return point_file_; }
    
    // 获取点表统计信息
    struct PointTableStats {
        int total_points = 0;
        int yc_points = 0;
        int yx_points = 0;
        int yt_points = 0;
        int yk_points = 0;
        std::map<int, int> slave_point_count;  // 每个从站的点数
    };
    
    PointTableStats GetPointTableStats(const DataPointList& points) const;
    
private:
    // 解析点表行
    Result<DataPoint> ParsePointLine(const std::string& line) const;
    
    // 解析扩展点表行
    Result<DataPointEx> ParsePointLineEx(const std::string& line) const;
    
    // 解析类型索引 "数据类型:点号"
    Result<TypeIndex> ParseTypeIndex(const std::string& type_str) const;
    
    // 解析点表参数 "功能码,从站,地址,数量[,比例,偏移,描述]"
    Result<bool> ParsePointParams(const std::string& params_str, DataPoint& point) const;
    
    // 解析功能码
    Result<FunctionCode> ParseFunctionCode(int code) const;
    
    // 验证数据点
    Result<bool> ValidateDataPoint(const DataPoint& point) const;
    
    // 验证功能码和数据类型的匹配性
    bool IsValidFunctionCodeForDataType(FunctionCode func_code, DataType data_type) const;
    
    // 解析报警限值
    Result<bool> ParseAlarmLimits(const std::string& point_id, DataPointEx& point, IniConfigParser& parser) const;
    
    // 解析量程配置
    Result<bool> ParseScaleConfig(const std::string& point_id, DataPointEx& point, IniConfigParser& parser) const;
    
    // 点表文件监控
    void StartPointTableMonitoring();
    void StopPointTableMonitoring();
    void PointTableMonitorThread();
    
    // 检查文件是否修改
    bool IsPointTableFileModified() const;
    
    // 获取文件修改时间
    uint64_t GetFileModificationTime(const std::string& filename) const;
    
private:
    std::string point_file_;
    uint64_t last_modification_time_ = 0;
    
    // 点表监控
    std::unique_ptr<std::thread> monitor_thread_;
    std::atomic<bool> monitoring_enabled_{false};
    std::atomic<bool> monitor_running_{false};
    Event stop_monitor_event_;
    
    // 回调函数
    PointTableChangeCallback change_callback_;
    
    // 线程安全
    mutable MutexLock point_table_mutex_;
};

// 点表管理器 - 管理多个设备的点表
class PointTableManager {
public:
    PointTableManager() = default;
    ~PointTableManager() = default;
    
    // 禁止拷贝和赋值
    PointTableManager(const PointTableManager&) = delete;
    PointTableManager& operator=(const PointTableManager&) = delete;
    
    // 加载设备点表
    Result<bool> LoadDevicePointTable(int device_id, const std::string& point_file);
    
    // 获取设备点表
    Result<DataPointList> GetDevicePointTable(int device_id) const;
    
    // 获取设备的特定类型点表
    Result<DataPointList> GetDevicePointsByType(int device_id, DataType data_type) const;
    
    // 重新加载所有点表
    Result<bool> ReloadAllPointTables();
    
    // 移除设备点表
    bool RemoveDevicePointTable(int device_id);
    
    // 获取所有设备ID
    std::vector<int> GetDeviceIds() const;
    
    // 获取点表统计信息
    std::map<int, PointTableLoader::PointTableStats> GetAllPointTableStats() const;
    
    // 设置全局点表变化回调
    void SetGlobalPointTableChangeCallback(std::function<void(int device_id, const DataPointList& points)> callback);
    
private:
    std::map<int, std::unique_ptr<PointTableLoader>> device_loaders_;
    std::map<int, DataPointList> device_point_tables_;
    
    std::function<void(int device_id, const DataPointList& points)> global_change_callback_;
    
    mutable ReadWriteLock manager_lock_;
};

// 单例点表管理器
class GlobalPointTableManager {
public:
    static PointTableManager& GetInstance();
    
private:
    GlobalPointTableManager() = default;
    ~GlobalPointTableManager() = default;
    
    GlobalPointTableManager(const GlobalPointTableManager&) = delete;
    GlobalPointTableManager& operator=(const GlobalPointTableManager&) = delete;
};

// 便捷宏定义
#define POINT_TABLE_MANAGER() modbus::GlobalPointTableManager::GetInstance()

} // namespace modbus

#endif // POINT_TABLE_LOADER_H
