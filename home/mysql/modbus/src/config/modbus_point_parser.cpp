#include "modbus_point_parser.h"
#include "../data/data_point_manager.h"
#include "../utils/utils.h"
#include "../utils/logger.h"
#include <fstream>
#include <sstream>
#include <algorithm>

namespace modbus {

Result<DevicePointConfig> ModbusPointParser::ParsePointFile(const std::string& file_path) {
    std::ifstream file(file_path);
    if (!file.is_open()) {
        return Result<DevicePointConfig>(ErrorCode::CONFIG_ERROR, 
            "无法打开点表文件: " + file_path);
    }

    DevicePointConfig config;
    std::vector<std::string> lines;
    std::string line;
    std::string current_section;

    // 读取所有行
    while (std::getline(file, line)) {
        // 去除首尾空白
        line = utils::StringUtils::Trim(line);
        
        // 跳过空行和注释行
        if (line.empty() || line[0] == '#') {
            continue;
        }
        
        // 检查是否是段标题
        if (line[0] == '[' && line.back() == ']') {
            // 处理前一个段
            if (!current_section.empty() && !lines.empty()) {
                auto section_result = ParseSection(lines, current_section);
                if (section_result.IsSuccess()) {
                    if (current_section == "YC") {
                        config.yc_configs = section_result.data;
                    } else if (current_section == "YX") {
                        config.yx_configs = section_result.data;
                    } else if (current_section == "YT") {
                        config.yt_configs = section_result.data;
                    } else if (current_section == "YK") {
                        config.yk_configs = section_result.data;
                    } else if (current_section == "YS") {
                        config.ys_configs = section_result.data;
                    }
                }
                lines.clear();
            }
            
            // 提取段名
            current_section = line.substr(1, line.length() - 2);
            continue;
        }
        
        lines.push_back(line);
    }
    
    // 处理最后一个段
    if (!current_section.empty() && !lines.empty()) {
        auto section_result = ParseSection(lines, current_section);
        if (section_result.IsSuccess()) {
            if (current_section == "YC") {
                config.yc_configs = section_result.data;
            } else if (current_section == "YX") {
                config.yx_configs = section_result.data;
            } else if (current_section == "YT") {
                config.yt_configs = section_result.data;
            } else if (current_section == "YK") {
                config.yk_configs = section_result.data;
            } else if (current_section == "YS") {
                config.ys_configs = section_result.data;
            }
        }
    }

    return Result<DevicePointConfig>(config);
}

Result<std::vector<ModbusPointConfig>> ModbusPointParser::ParseSection(
    const std::vector<std::string>& lines, 
    const std::string& section_name) {
    
    std::vector<ModbusPointConfig> configs;
    
    for (const auto& line : lines) {
        auto config_result = ParseConfigLine(line);
        if (config_result.IsSuccess()) {
            configs.push_back(config_result.data);
        } else {
            WRITE_WARN_LOG("解析配置行失败 [%s]: %s - %s", 
                          section_name.c_str(), line.c_str(), 
                          config_result.error_message.c_str());
        }
    }
    
    return Result<std::vector<ModbusPointConfig>>(configs);
}

Result<ModbusPointConfig> ModbusPointParser::ParseConfigLine(const std::string& line) {
    // 格式: 功能码,起始地址,数量,装置内序号起始,类型,参数
    // 示例: 03,0000,5,1,0
    
    std::vector<std::string> parts = utils::StringUtils::Split(line, ",");
    if (parts.size() < 5) {
        return Result<ModbusPointConfig>(ErrorCode::CONFIG_ERROR, 
            "配置行格式错误，至少需要5个参数");
    }
    
    ModbusPointConfig config;
    
    try {
        // 解析功能码
        config.function_code = std::stoi(utils::StringUtils::Trim(parts[0]));
        if (!IsValidFunctionCode(config.function_code)) {
            return Result<ModbusPointConfig>(ErrorCode::CONFIG_ERROR, 
                "无效的功能码: " + parts[0]);
        }
        
        // 解析起始地址 (16进制)
        config.start_addr = ParseHexAddress(utils::StringUtils::Trim(parts[1]));
        if (config.start_addr < 0) {
            return Result<ModbusPointConfig>(ErrorCode::CONFIG_ERROR, 
                "无效的起始地址: " + parts[1]);
        }
        
        // 解析数量
        config.count = std::stoi(utils::StringUtils::Trim(parts[2]));
        if (config.count <= 0) {
            return Result<ModbusPointConfig>(ErrorCode::CONFIG_ERROR, 
                "数量必须大于0: " + parts[2]);
        }
        
        // 解析Redis起始序号
        config.redis_start_index = std::stoi(utils::StringUtils::Trim(parts[3]));
        if (config.redis_start_index <= 0) {
            return Result<ModbusPointConfig>(ErrorCode::CONFIG_ERROR, 
                "Redis序号必须大于0: " + parts[3]);
        }
        
        // 解析数据类型
        config.data_type = std::stoi(utils::StringUtils::Trim(parts[4]));
        if (!IsValidDataType(config.data_type)) {
            return Result<ModbusPointConfig>(ErrorCode::CONFIG_ERROR, 
                "无效的数据类型: " + parts[4]);
        }
        
        // 解析参数 (可选)
        if (parts.size() > 5) {
            config.param = std::stoi(utils::StringUtils::Trim(parts[5]));
        }
        
    } catch (const std::exception& e) {
        return Result<ModbusPointConfig>(ErrorCode::CONFIG_ERROR, 
            "解析配置参数失败: " + std::string(e.what()));
    }
    
    return Result<ModbusPointConfig>(config);
}

int ModbusPointParser::ParseHexAddress(const std::string& hex_str) {
    try {
        return std::stoi(hex_str, nullptr, 16);
    } catch (const std::exception&) {
        return -1;
    }
}

bool ModbusPointParser::IsValidFunctionCode(int func_code) {
    return func_code == 1 || func_code == 2 || func_code == 3 || func_code == 4 ||
           func_code == 5 || func_code == 6 || func_code == 15 || func_code == 16;
}

bool ModbusPointParser::IsValidDataType(int data_type) {
    return data_type >= 0 && data_type <= 11;
}

FunctionCode ModbusPointParser::GetFunctionCode(int modbus_func_code) {
    switch (modbus_func_code) {
        case 1: return FunctionCode::READ_COILS;
        case 2: return FunctionCode::READ_DISCRETE_INPUTS;
        case 3: return FunctionCode::READ_HOLDING_REGISTERS;
        case 4: return FunctionCode::READ_INPUT_REGISTERS;
        case 5: return FunctionCode::WRITE_SINGLE_COIL;
        case 6: return FunctionCode::WRITE_SINGLE_REGISTER;
        case 15: return FunctionCode::WRITE_MULTIPLE_COILS;
        case 16: return FunctionCode::WRITE_MULTIPLE_REGISTERS;
        default: return FunctionCode::READ_HOLDING_REGISTERS;
    }
}

int ModbusPointParser::GetRegisterCount(int data_type) {
    switch (data_type) {
        case 0:  // 无符号16位整数
        case 1:  // 有符号16位整数
        case 10: // 非补码有符号16位整数
            return 1;
        case 2:  // 单精度浮点数
        case 3:  // 低字前高字后
        case 4:  // 高字前低字后
        case 5:  // 有、无符号混合16位整数
        case 6:  // 有、无符号混合32位整数
        case 8:  // BCD
        case 11: // 非补码有、无符号混合16位整数
            return 2;
        case 7:  // 双精度浮点数
            return 4;
        case 9:  // 无符号64位整数
            return 4;
        default:
            return 1;
    }
}

Result<std::vector<DataPointEx>> ModbusPointParser::GenerateDataPoints(
    const DevicePointConfig& config,
    int device_id,
    int slave_id) {

    std::vector<DataPointEx> all_points;

    // 生成遥测点
    for (const auto& yc_config : config.yc_configs) {
        auto points_result = GeneratePointsFromConfig(yc_config, DataType::YC, device_id, slave_id);
        if (points_result.IsSuccess()) {
            auto& points = points_result.data;
            all_points.insert(all_points.end(), points.begin(), points.end());
        }
    }

    // 生成遥信点
    for (const auto& yx_config : config.yx_configs) {
        auto points_result = GeneratePointsFromConfig(yx_config, DataType::YX, device_id, slave_id);
        if (points_result.IsSuccess()) {
            auto& points = points_result.data;
            all_points.insert(all_points.end(), points.begin(), points.end());
        }
    }

    // 生成遥脉点
    for (const auto& yt_config : config.yt_configs) {
        auto points_result = GeneratePointsFromConfig(yt_config, DataType::YT, device_id, slave_id);
        if (points_result.IsSuccess()) {
            auto& points = points_result.data;
            all_points.insert(all_points.end(), points.begin(), points.end());
        }
    }

    // 生成遥控点
    for (const auto& yk_config : config.yk_configs) {
        auto points_result = GeneratePointsFromConfig(yk_config, DataType::YK, device_id, slave_id);
        if (points_result.IsSuccess()) {
            auto& points = points_result.data;
            all_points.insert(all_points.end(), points.begin(), points.end());
        }
    }

    // 生成遥设点 (使用YT类型，因为DataType枚举中没有YS)
    for (const auto& ys_config : config.ys_configs) {
        auto points_result = GeneratePointsFromConfig(ys_config, DataType::YT, device_id, slave_id);
        if (points_result.IsSuccess()) {
            auto& points = points_result.data;
            all_points.insert(all_points.end(), points.begin(), points.end());
        }
    }

    return Result<std::vector<DataPointEx>>(all_points);
}

Result<std::vector<DataPointEx>> ModbusPointParser::GeneratePointsFromConfig(
    const ModbusPointConfig& config,
    DataType data_type,
    int device_id,
    int slave_id) {

    std::vector<DataPointEx> points;

    // 根据数据类型确定每个点占用的寄存器数量
    int reg_count = GetRegisterCount(config.data_type);

    // 生成每个数据点
    for (int i = 0; i < config.count; i++) {
        DataPointEx point;

        // 设置类型索引
        point.type_idx.data_type = data_type;
        point.type_idx.point_id = config.redis_start_index + i;

        // 设置基本参数
        point.func_code = GetFunctionCode(config.function_code);
        point.slave_id = slave_id;
        point.start_addr = config.start_addr + (i * reg_count);
        point.count = reg_count;
        point.data_type = config.data_type;
        point.param = config.param;
        point.scale = 1.0;
        point.offset = 0.0;

        // 设置描述
        point.description = utils::StringUtils::Format("%s_%d_%d",
            data_type == DataType::YC ? "遥测" :
            data_type == DataType::YX ? "遥信" :
            data_type == DataType::YT ? "遥脉" :
            data_type == DataType::YK ? "遥控" : "遥设",
            device_id, point.type_idx.point_id);

        points.push_back(point);
    }

    return Result<std::vector<DataPointEx>>(points);
}

} // namespace modbus
