#ifndef CONFIG_MANAGER_H
#define CONFIG_MANAGER_H

#include "../types/modbus_types.h"
#include "../utils/thread_pool.h"
#include <map>
#include <memory>
#include <functional>
#include <string>

namespace modbus {

// 配置变化回调函数类型
using ConfigChangeCallback = std::function<void(const std::string& section, const std::string& key, const std::string& value)>;

// 装置模板参数类型枚举
enum class TemplateParamType : int {
    YC = 0,     // 遥测
    YX = 1,     // 遥信
    YM = 2,     // 遥脉
    YK = 3,     // 遥控
    YS = 4,     // 遥设
    TIME_SYNC = 5  // 校时
};

// 数据类型枚举（V1.12版本）
enum class ModbusDataType : int {
    UINT16 = 0,         // 无符号16位整数
    INT16 = 1,          // 有符号16位整数
    FLOAT32 = 2,        // 单精度浮点数
    UINT32 = 3,         // 无符号32位整数
    INT32 = 4,          // 有符号32位整数
    MIXED_INT16 = 5,    // 有、无符号混合16位整数
    MIXED_INT32 = 6,    // 有、无符号混合32位整数
    DOUBLE64 = 7,       // 双精度浮点数
    BCD = 8,            // BCD码
    UINT64 = 9          // 无符号64位整数
};

// 字节序类型
enum class ByteOrder : int {
    NORMAL = 0,         // 正常字节序
    REVERSE = 1,        // 相反字节序
    ABNORMAL = 2,       // 异常字节序
    LOW_FIRST = 3       // 先低字后高字
};

// 装置模板参数结构
struct TemplateParam {
    TemplateParamType type;     // 参数类型
    uint16_t func_code;         // 功能码
    uint16_t start_addr;        // 起始地址
    uint16_t count;             // 数量
    uint16_t template_fun_inf;  // 对应模板FUN/INF
    ModbusDataType data_type;   // 数据类型
    uint32_t param;             // 参数
    uint32_t sign_param;        // 符号参数
    uint32_t split_param;       // 分解参数
    std::string time_format;    // 时间位置字符串（仅校时使用）

    TemplateParam()
        : type(TemplateParamType::YC)
        , func_code(0x03)
        , start_addr(0)
        , count(1)
        , template_fun_inf(0)
        , data_type(ModbusDataType::UINT16)
        , param(0)
        , sign_param(0)
        , split_param(0) {}
};

// 设备模板配置
struct DeviceTemplate {
    int device_id;
    std::string device_name;
    std::vector<TemplateParam> yc_params;    // 遥测参数
    std::vector<TemplateParam> yx_params;    // 遥信参数
    std::vector<TemplateParam> ym_params;    // 遥脉参数
    std::vector<TemplateParam> yk_params;    // 遥控参数
    std::vector<TemplateParam> ys_params;    // 遥设参数
    std::vector<TemplateParam> time_params;  // 校时参数

    DeviceTemplate() : device_id(0) {}
};

// INI 配置解析器
class IniConfigParser {
public:
    IniConfigParser() = default;
    ~IniConfigParser() = default;
    
    // 加载配置文件
    Result<bool> LoadFile(const std::string& filename);
    
    // 保存配置文件
    Result<bool> SaveFile(const std::string& filename) const;
    
    // 获取配置值
    std::string GetString(const std::string& section, const std::string& key, const std::string& default_value = "") const;
    int GetInt(const std::string& section, const std::string& key, int default_value = 0) const;
    double GetDouble(const std::string& section, const std::string& key, double default_value = 0.0) const;
    bool GetBool(const std::string& section, const std::string& key, bool default_value = false) const;
    
    // 设置配置值
    void SetString(const std::string& section, const std::string& key, const std::string& value);
    void SetInt(const std::string& section, const std::string& key, int value);
    void SetDouble(const std::string& section, const std::string& key, double value);
    void SetBool(const std::string& section, const std::string& key, bool value);
    
    // 检查配置项是否存在
    bool HasSection(const std::string& section) const;
    bool HasKey(const std::string& section, const std::string& key) const;
    
    // 获取所有节名
    std::vector<std::string> GetSections() const;
    
    // 获取节中所有键名
    std::vector<std::string> GetKeys(const std::string& section) const;
    
    // 删除配置项
    bool RemoveSection(const std::string& section);
    bool RemoveKey(const std::string& section, const std::string& key);
    
    // 清空所有配置
    void Clear();
    
private:
    // 解析配置行
    bool ParseLine(const std::string& line, std::string& current_section);
    
    // 去除注释和空白
    std::string TrimLine(const std::string& line) const;
    
    // 解析节名
    bool ParseSection(const std::string& line, std::string& section) const;
    
    // 解析键值对
    bool ParseKeyValue(const std::string& line, std::string& key, std::string& value) const;
    
private:
    std::map<std::string, std::map<std::string, std::string>> config_data_;
    mutable MutexLock mutex_;
};

// 配置管理器
class ConfigManager {
public:
    ConfigManager() = default;
    ~ConfigManager() = default;
    
    // 禁止拷贝和赋值
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;
    
    // 加载配置文件 (自动检测格式)
    Result<bool> LoadConfig(const std::string& config_file);

    // 加载 XML 配置文件
    Result<bool> LoadXmlConfig(const std::string& config_file);

    // 重新加载配置
    Result<bool> ReloadConfig();

    // 保存配置
    Result<bool> SaveConfig(const std::string& config_file = "") const;
    
    // 获取服务参数
    Result<ServiceParam> GetServiceParam() const;
    
    // 获取设备参数
    Result<DeviceParam> GetDeviceParam(int device_id) const;

    // 获取所有设备参数
    Result<std::map<int, DeviceParam>> GetAllDeviceParams() const;

    // 获取设备模板配置
    Result<DeviceTemplate> GetDeviceTemplate(int device_id) const;

    // 获取所有设备模板配置
    Result<std::map<int, DeviceTemplate>> GetAllDeviceTemplates() const;
    
    // 设置配置变化回调
    void SetConfigChangeCallback(ConfigChangeCallback callback);
    
    // 启用配置文件监控
    void EnableConfigMonitoring(bool enable = true);
    
    // 获取配置文件路径
    std::string GetConfigFile() const { return config_file_; }
    
    // 验证配置
    Result<bool> ValidateConfig() const;
    
    // 获取原始配置值
    std::string GetString(const std::string& section, const std::string& key, const std::string& default_value = "") const;
    int GetInt(const std::string& section, const std::string& key, int default_value = 0) const;
    double GetDouble(const std::string& section, const std::string& key, double default_value = 0.0) const;
    bool GetBool(const std::string& section, const std::string& key, bool default_value = false) const;
    
private:
    // 解析服务配置
    Result<ServiceParam> ParseServiceConfig() const;
    
    // 解析设备配置
    Result<DeviceParam> ParseDeviceConfig(const std::string& device_section) const;

    // 解析设备数据点配置
    Result<DevicePointConfig> ParseDevicePointConfig(int device_id) const;

    // 解析数据点配置段
    Result<std::vector<ModbusPointConfig>> ParsePointSection(const std::string& section) const;

    // 解析单行数据点配置
    Result<ModbusPointConfig> ParsePointConfigLine(const std::string& line) const;

    // 解析 RTU 参数
    Result<RtuCommParam> ParseRtuParam(const std::string& device_section) const;

    // 解析 TCP 参数
    Result<TcpCommParam> ParseTcpParam(const std::string& device_section) const;

    // 解析设备模板配置
    Result<DeviceTemplate> ParseDeviceTemplate(const std::string& device_section) const;

    // 解析装置模板参数
    Result<std::vector<TemplateParam>> ParseTemplateParams(const std::string& section,
                                                          const std::string& key_prefix,
                                                          TemplateParamType param_type) const;

    // 解析单个模板参数
    Result<TemplateParam> ParseSingleTemplateParam(const std::string& param_str,
                                                   TemplateParamType param_type) const;

    // 解析16进制字符串
    uint32_t ParseHexString(const std::string& hex_str) const;

    // 验证服务参数
    Result<bool> ValidateServiceParam(const ServiceParam& param) const;
    
    // 验证设备参数
    Result<bool> ValidateDeviceParam(const DeviceParam& param) const;
    
    // 查找设备配置节
    std::vector<std::string> FindDeviceSections() const;
    
    // 配置文件监控
    void StartConfigMonitoring();
    void StopConfigMonitoring();
    void ConfigMonitorThread();
    
    // 检查文件是否修改
    bool IsConfigFileModified() const;
    
    // 获取文件修改时间
    uint64_t GetFileModificationTime(const std::string& filename) const;
    
private:
    std::unique_ptr<IniConfigParser> parser_;
    std::string config_file_;
    uint64_t last_modification_time_;

    // XML 配置支持
    ServiceParam service_param_;  // 存储解析后的服务参数
    
    // 配置监控
    std::unique_ptr<std::thread> monitor_thread_;
    std::atomic<bool> monitoring_enabled_;
    std::atomic<bool> monitor_running_;
    Event stop_monitor_event_;
    
    // 回调函数
    ConfigChangeCallback change_callback_;
    
    // 线程安全
    mutable MutexLock config_mutex_;
};

// 单例配置管理器
class GlobalConfigManager {
public:
    static ConfigManager& GetInstance();
    
private:
    GlobalConfigManager() = default;
    ~GlobalConfigManager() = default;
    
    GlobalConfigManager(const GlobalConfigManager&) = delete;
    GlobalConfigManager& operator=(const GlobalConfigManager&) = delete;
};

// 便捷宏定义
#define CONFIG_MANAGER() modbus::GlobalConfigManager::GetInstance()

// 配置常量定义
namespace ConfigKeys {
    // 服务配置
    namespace Service {
        const std::string SECTION = "Service";
        const std::string REDIS_IP = "RedisIP";
        const std::string REDIS_PORT = "RedisPort";
        const std::string REPORT_TIME = "ReportTime";
        const std::string LOG_LEVEL = "LogLevel";
        const std::string LOG_FILE = "LogFile";
    }
    
    // 设备配置
    namespace Device {
        const std::string DEVICE_ID = "DeviceID";
        const std::string DEVICE_NAME = "DeviceName";
        const std::string COMM_TYPE = "CommType";
        const std::string POINT_FILE = "PointFile";
        
        // RTU 配置
        const std::string DEVICE = "Device";
        const std::string BAUD = "Baud";
        const std::string PARITY = "Parity";
        const std::string DATA_BIT = "DataBit";
        const std::string STOP_BIT = "StopBit";
        const std::string MODE = "Mode";
        const std::string TIMEOUT = "Timeout";
        
        // TCP 配置
        const std::string IP = "IP";
        const std::string PORT = "Port";

        // 装置模板配置
        const std::string YC_PARAM = "遥测";
        const std::string YX_PARAM = "遥信";
        const std::string YM_PARAM = "遥脉";
        const std::string YK_PARAM = "遥控";
        const std::string YS_PARAM = "遥设";
        const std::string TIME_SYNC_PARAM = "校时";
    }
    
    // 高级配置
    namespace Advanced {
        const std::string SECTION = "Advanced";
        const std::string ENABLE_AUTO_RECONNECT = "EnableAutoReconnect";
        const std::string RECONNECT_INTERVAL = "ReconnectInterval";
        const std::string MAX_RECONNECT_ATTEMPTS = "MaxReconnectAttempts";
        const std::string CONNECTION_CHECK_INTERVAL = "ConnectionCheckInterval";
        const std::string THREAD_POOL_SIZE = "ThreadPoolSize";
    }
}

} // namespace modbus

#endif // CONFIG_MANAGER_H
