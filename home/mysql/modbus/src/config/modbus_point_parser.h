#ifndef MODBUS_POINT_PARSER_H
#define MODBUS_POINT_PARSER_H

#include "../types/modbus_types.h"
#include "../utils/thread_pool.h"
#include <string>
#include <vector>

// 前向声明
namespace modbus {
    struct DataPointEx;
}

namespace modbus {

// Modbus 点配置解析器
class ModbusPointParser {
public:
    ModbusPointParser() = default;
    ~ModbusPointParser() = default;

    // 解析点表配置文件
    Result<DevicePointConfig> ParsePointFile(const std::string& file_path);
    
    // 解析单行配置
    Result<ModbusPointConfig> ParseConfigLine(const std::string& line);
    
    // 生成数据点列表
    Result<std::vector<DataPointEx>> GenerateDataPoints(
        const DevicePointConfig& config, 
        int device_id, 
        int slave_id);

private:
    // 解析配置段
    Result<std::vector<ModbusPointConfig>> ParseSection(
        const std::vector<std::string>& lines, 
        const std::string& section_name);
    
    // 解析16进制地址
    int ParseHexAddress(const std::string& hex_str);
    
    // 验证功能码
    bool IsValidFunctionCode(int func_code);
    
    // 验证数据类型
    bool IsValidDataType(int data_type);
    
    // 根据配置生成单个数据点
    Result<std::vector<DataPointEx>> GeneratePointsFromConfig(
        const ModbusPointConfig& config,
        DataType data_type,
        int device_id,
        int slave_id);
    
    // 获取数据类型对应的功能码
    FunctionCode GetFunctionCode(int modbus_func_code);
    
    // 计算寄存器数量 (根据数据类型)
    int GetRegisterCount(int data_type);
};

} // namespace modbus

#endif // MODBUS_POINT_PARSER_H
