#include "point_table_loader.h"
#include "../utils/logger.h"
#include "../utils/utils.h"
#include <fstream>
#include <sstream>
#include <filesystem>

namespace modbus {

// PointTableLoader 实现
Result<DataPointList> PointTableLoader::LoadPointTable(const std::string& point_file) {
    std::lock_guard<MutexLock> lock(point_table_mutex_);
    
    std::ifstream file(point_file);
    if (!file.is_open()) {
        return Result<DataPointList>(ErrorCode::CONFIG_ERROR, "Cannot open point table file: " + point_file);
    }
    
    DataPointList points;
    std::string line;
    int line_number = 0;
    bool in_point_list_section = false;
    
    while (std::getline(file, line)) {
        line_number++;
        
        // 去除空白和注释
        line = utils::StringUtils::Trim(line);
        if (line.empty() || line[0] == '#' || line[0] == ';') {
            continue;
        }
        
        // 检查是否是节标题
        if (line[0] == '[' && line.back() == ']') {
            std::string section = line.substr(1, line.length() - 2);
            in_point_list_section = (section == "PointList");
            continue;
        }
        
        // 只处理 PointList 节中的内容
        if (!in_point_list_section) {
            continue;
        }
        
        // 解析点表行
        auto point_result = ParsePointLine(line);
        if (point_result.IsSuccess()) {
            points.push_back(point_result.data);
        } else {
            WRITE_WARN_LOG("点表文件第 %d 行格式错误: %s - %s",
                          line_number, line.c_str(), point_result.error_message.c_str());
        }
    }
    
    file.close();
    
    // 验证点表
    auto validate_result = ValidatePointTable(points);
    if (!validate_result.IsSuccess()) {
        WRITE_WARN_LOG("Point table validation failed: %s", validate_result.error_message.c_str());
    }
    
    point_file_ = point_file;
    last_modification_time_ = GetFileModificationTime(point_file);
    
    WRITE_INFO_LOG("Point table loaded successfully from: %s (%zu points)", 
                   point_file.c_str(), points.size());
    
    return Result<DataPointList>(points);
}

Result<DataPointList> PointTableLoader::ReloadPointTable() {
    if (point_file_.empty()) {
        return Result<DataPointList>(ErrorCode::CONFIG_ERROR, "No point table file loaded");
    }
    
    return LoadPointTable(point_file_);
}

DataPointList PointTableLoader::FilterByDataType(const DataPointList& points, DataType data_type) const {
    DataPointList filtered_points;
    
    for (const auto& point : points) {
        if (point.type_idx.data_type == data_type) {
            filtered_points.push_back(point);
        }
    }
    
    return filtered_points;
}

DataPointList PointTableLoader::FilterBySlaveId(const DataPointList& points, int slave_id) const {
    DataPointList filtered_points;
    
    for (const auto& point : points) {
        if (point.slave_id == slave_id) {
            filtered_points.push_back(point);
        }
    }
    
    return filtered_points;
}

Result<bool> PointTableLoader::ValidatePointTable(const DataPointList& points) const {
    if (points.empty()) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Empty point table");
    }
    
    // 检查点号重复
    std::map<std::pair<DataType, int>, int> point_map;
    
    for (size_t i = 0; i < points.size(); ++i) {
        const auto& point = points[i];
        
        // 验证单个数据点
        auto validate_result = ValidateDataPoint(point);
        if (!validate_result.IsSuccess()) {
            return Result<bool>(ErrorCode::CONFIG_ERROR, 
                              "Point " + std::to_string(i) + ": " + validate_result.error_message);
        }
        
        // 检查点号重复
        auto key = std::make_pair(point.type_idx.data_type, point.type_idx.point_id);
        if (point_map.find(key) != point_map.end()) {
            return Result<bool>(ErrorCode::CONFIG_ERROR, 
                              "Duplicate point: " + std::to_string(static_cast<int>(point.type_idx.data_type)) + 
                              ":" + std::to_string(point.type_idx.point_id));
        }
        point_map[key] = i;
    }
    
    return Result<bool>(true);
}

PointTableLoader::PointTableStats PointTableLoader::GetPointTableStats(const DataPointList& points) const {
    PointTableStats stats;
    
    stats.total_points = points.size();
    
    for (const auto& point : points) {
        switch (point.type_idx.data_type) {
            case DataType::YC:
                stats.yc_points++;
                break;
            case DataType::YX:
                stats.yx_points++;
                break;
            case DataType::YT:
                stats.yt_points++;
                break;
            case DataType::YK:
                stats.yk_points++;
                break;
        }
        
        stats.slave_point_count[point.slave_id]++;
    }
    
    return stats;
}

// 私有方法实现
Result<DataPoint> PointTableLoader::ParsePointLine(const std::string& line) const {
    // 格式: 数据类型:点号 = 功能码,从站,地址,数量[,比例,偏移,描述]
    size_t eq_pos = line.find('=');
    if (eq_pos == std::string::npos) {
        return Result<DataPoint>(ErrorCode::CONFIG_ERROR, "Missing '=' in point line");
    }
    
    std::string type_str = utils::StringUtils::Trim(line.substr(0, eq_pos));
    std::string params_str = utils::StringUtils::Trim(line.substr(eq_pos + 1));
    
    // 解析类型索引
    auto type_result = ParseTypeIndex(type_str);
    if (!type_result.IsSuccess()) {
        return Result<DataPoint>(type_result.error_code, type_result.error_message);
    }
    
    DataPoint point;
    point.type_idx = type_result.data;
    
    // 解析参数
    auto params_result = ParsePointParams(params_str, point);
    if (!params_result.IsSuccess()) {
        return Result<DataPoint>(params_result.error_code, params_result.error_message);
    }
    
    return Result<DataPoint>(point);
}

Result<TypeIndex> PointTableLoader::ParseTypeIndex(const std::string& type_str) const {
    size_t colon_pos = type_str.find(':');
    if (colon_pos == std::string::npos) {
        return Result<TypeIndex>(ErrorCode::CONFIG_ERROR, "Missing ':' in type index");
    }
    
    std::string data_type_str = utils::StringUtils::Trim(type_str.substr(0, colon_pos));
    std::string point_id_str = utils::StringUtils::Trim(type_str.substr(colon_pos + 1));
    
    int data_type_int = utils::StringUtils::ToInt(data_type_str, -1);
    if (data_type_int < 1 || data_type_int > 4) {
        return Result<TypeIndex>(ErrorCode::CONFIG_ERROR, "Invalid data type: " + data_type_str);
    }
    
    int point_id = utils::StringUtils::ToInt(point_id_str, -1);
    if (point_id < 0) {
        return Result<TypeIndex>(ErrorCode::CONFIG_ERROR, "Invalid point ID: " + point_id_str);
    }
    
    TypeIndex type_idx;
    type_idx.data_type = static_cast<DataType>(data_type_int);
    type_idx.point_id = point_id;
    
    return Result<TypeIndex>(type_idx);
}

Result<bool> PointTableLoader::ParsePointParams(const std::string& params_str, DataPoint& point) const {
    auto params = utils::StringUtils::Split(params_str, ",");
    if (params.size() < 4) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Insufficient parameters (need at least 4)");
    }
    
    // 解析必需参数
    int func_code = utils::StringUtils::ToInt(utils::StringUtils::Trim(params[0]), -1);
    auto func_result = ParseFunctionCode(func_code);
    if (!func_result.IsSuccess()) {
        return Result<bool>(func_result.error_code, func_result.error_message);
    }
    point.func_code = func_result.data;
    
    point.slave_id = utils::StringUtils::ToInt(utils::StringUtils::Trim(params[1]), 1);
    point.start_addr = utils::StringUtils::ToInt(utils::StringUtils::Trim(params[2]), 0);
    point.count = utils::StringUtils::ToInt(utils::StringUtils::Trim(params[3]), 1);
    
    // 解析可选参数
    if (params.size() > 4) {
        point.scale = utils::StringUtils::ToDouble(utils::StringUtils::Trim(params[4]), 1.0);
    }
    
    if (params.size() > 5) {
        point.offset = utils::StringUtils::ToDouble(utils::StringUtils::Trim(params[5]), 0.0);
    }
    
    if (params.size() > 6) {
        point.description = utils::StringUtils::Trim(params[6]);
    }
    
    // 验证功能码和数据类型的匹配性
    if (!IsValidFunctionCodeForDataType(point.func_code, point.type_idx.data_type)) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, 
                          "Function code " + std::to_string(func_code) + 
                          " not valid for data type " + std::to_string(static_cast<int>(point.type_idx.data_type)));
    }
    
    return Result<bool>(true);
}

Result<FunctionCode> PointTableLoader::ParseFunctionCode(int code) const {
    switch (code) {
        case 1: return Result<FunctionCode>(FunctionCode::READ_COILS);
        case 2: return Result<FunctionCode>(FunctionCode::READ_DISCRETE_INPUTS);
        case 3: return Result<FunctionCode>(FunctionCode::READ_HOLDING_REGISTERS);
        case 4: return Result<FunctionCode>(FunctionCode::READ_INPUT_REGISTERS);
        case 5: return Result<FunctionCode>(FunctionCode::WRITE_SINGLE_COIL);
        case 6: return Result<FunctionCode>(FunctionCode::WRITE_SINGLE_REGISTER);
        case 15: return Result<FunctionCode>(FunctionCode::WRITE_MULTIPLE_COILS);
        case 16: return Result<FunctionCode>(FunctionCode::WRITE_MULTIPLE_REGISTERS);
        default:
            return Result<FunctionCode>(ErrorCode::CONFIG_ERROR, "Invalid function code: " + std::to_string(code));
    }
}

Result<bool> PointTableLoader::ValidateDataPoint(const DataPoint& point) const {
    // 验证从站地址
    if (point.slave_id < 1 || point.slave_id > 247) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Invalid slave ID: " + std::to_string(point.slave_id));
    }
    
    // 验证地址范围
    if (point.start_addr < 0 || point.start_addr > 65535) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Invalid start address: " + std::to_string(point.start_addr));
    }
    
    // 验证数量
    if (point.count <= 0) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Invalid count: " + std::to_string(point.count));
    }
    
    // 验证比例因子
    if (point.scale == 0.0) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Scale factor cannot be zero");
    }
    
    return Result<bool>(true);
}

bool PointTableLoader::IsValidFunctionCodeForDataType(FunctionCode func_code, DataType data_type) const {
    switch (data_type) {
        case DataType::YC:  // 遥测 - 读取操作
            return func_code == FunctionCode::READ_HOLDING_REGISTERS || 
                   func_code == FunctionCode::READ_INPUT_REGISTERS;
                   
        case DataType::YX:  // 遥信 - 读取操作
            return func_code == FunctionCode::READ_COILS || 
                   func_code == FunctionCode::READ_DISCRETE_INPUTS ||
                   func_code == FunctionCode::READ_HOLDING_REGISTERS;
                   
        case DataType::YT:  // 遥调 - 写入操作
            return func_code == FunctionCode::WRITE_SINGLE_REGISTER || 
                   func_code == FunctionCode::WRITE_MULTIPLE_REGISTERS;
                   
        case DataType::YK:  // 遥控 - 写入操作
            return func_code == FunctionCode::WRITE_SINGLE_COIL || 
                   func_code == FunctionCode::WRITE_MULTIPLE_COILS;
                   
        default:
            return false;
    }
}

uint64_t PointTableLoader::GetFileModificationTime(const std::string& filename) const {
    try {
        auto ftime = std::filesystem::last_write_time(filename);
        auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
            ftime - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now());
        return std::chrono::duration_cast<std::chrono::milliseconds>(sctp.time_since_epoch()).count();
    } catch (const std::exception&) {
        return 0;
    }
}

// GlobalPointTableManager 实现
PointTableManager& GlobalPointTableManager::GetInstance() {
    static PointTableManager instance;
    return instance;
}

} // namespace modbus
