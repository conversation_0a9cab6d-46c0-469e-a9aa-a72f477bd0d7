#include "config_manager.h"
#include "xml_config_parser.h"
#include "../utils/logger.h"
#include "../utils/utils.h"
#include <fstream>
#include <sstream>
#include <filesystem>
#include <algorithm>
#include <iostream>

namespace modbus {

// IniConfigParser 实现
Result<bool> IniConfigParser::LoadFile(const std::string& filename) {
    std::lock_guard<MutexLock> lock(mutex_);
    
    std::ifstream file(filename);
    if (!file.is_open()) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Cannot open config file: " + filename);
    }
    
    config_data_.clear();
    std::string line;
    std::string current_section;
    int line_number = 0;
    
    while (std::getline(file, line)) {
        line_number++;
        
        if (!ParseLine(line, current_section)) {
            WRITE_WARN_LOG("Invalid config line %d: %s", line_number, line.c_str());
        }
    }
    
    file.close();
    return Result<bool>(true);
}

Result<bool> IniConfigParser::SaveFile(const std::string& filename) const {
    std::lock_guard<MutexLock> lock(mutex_);
    
    std::ofstream file(filename);
    if (!file.is_open()) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Cannot create config file: " + filename);
    }
    
    for (const auto& section_pair : config_data_) {
        file << "[" << section_pair.first << "]" << std::endl;
        
        for (const auto& key_pair : section_pair.second) {
            file << key_pair.first << " = " << key_pair.second << std::endl;
        }
        
        file << std::endl;
    }
    
    file.close();
    return Result<bool>(true);
}

std::string IniConfigParser::GetString(const std::string& section, const std::string& key, const std::string& default_value) const {
    std::lock_guard<MutexLock> lock(mutex_);
    
    auto section_it = config_data_.find(section);
    if (section_it == config_data_.end()) {
        return default_value;
    }
    
    auto key_it = section_it->second.find(key);
    if (key_it == section_it->second.end()) {
        return default_value;
    }
    
    return key_it->second;
}

int IniConfigParser::GetInt(const std::string& section, const std::string& key, int default_value) const {
    std::string value = GetString(section, key);
    return value.empty() ? default_value : utils::StringUtils::ToInt(value, default_value);
}

double IniConfigParser::GetDouble(const std::string& section, const std::string& key, double default_value) const {
    std::string value = GetString(section, key);
    return value.empty() ? default_value : utils::StringUtils::ToDouble(value, default_value);
}

bool IniConfigParser::GetBool(const std::string& section, const std::string& key, bool default_value) const {
    std::string value = GetString(section, key);
    return value.empty() ? default_value : utils::StringUtils::ToBool(value, default_value);
}

void IniConfigParser::SetString(const std::string& section, const std::string& key, const std::string& value) {
    std::lock_guard<MutexLock> lock(mutex_);
    config_data_[section][key] = value;
}

void IniConfigParser::SetInt(const std::string& section, const std::string& key, int value) {
    SetString(section, key, utils::StringUtils::ToString(value));
}

void IniConfigParser::SetDouble(const std::string& section, const std::string& key, double value) {
    SetString(section, key, utils::StringUtils::ToString(value));
}

void IniConfigParser::SetBool(const std::string& section, const std::string& key, bool value) {
    SetString(section, key, utils::StringUtils::ToString(value));
}

bool IniConfigParser::HasSection(const std::string& section) const {
    std::lock_guard<MutexLock> lock(mutex_);
    return config_data_.find(section) != config_data_.end();
}

bool IniConfigParser::HasKey(const std::string& section, const std::string& key) const {
    std::lock_guard<MutexLock> lock(mutex_);
    
    auto section_it = config_data_.find(section);
    if (section_it == config_data_.end()) {
        return false;
    }
    
    return section_it->second.find(key) != section_it->second.end();
}

std::vector<std::string> IniConfigParser::GetSections() const {
    std::lock_guard<MutexLock> lock(mutex_);
    
    std::vector<std::string> sections;
    for (const auto& pair : config_data_) {
        sections.push_back(pair.first);
    }
    
    return sections;
}

std::vector<std::string> IniConfigParser::GetKeys(const std::string& section) const {
    std::lock_guard<MutexLock> lock(mutex_);
    
    std::vector<std::string> keys;
    auto section_it = config_data_.find(section);
    if (section_it != config_data_.end()) {
        for (const auto& pair : section_it->second) {
            keys.push_back(pair.first);
        }
    }
    
    return keys;
}

bool IniConfigParser::RemoveSection(const std::string& section) {
    std::lock_guard<MutexLock> lock(mutex_);
    return config_data_.erase(section) > 0;
}

bool IniConfigParser::RemoveKey(const std::string& section, const std::string& key) {
    std::lock_guard<MutexLock> lock(mutex_);
    
    auto section_it = config_data_.find(section);
    if (section_it == config_data_.end()) {
        return false;
    }
    
    return section_it->second.erase(key) > 0;
}

void IniConfigParser::Clear() {
    std::lock_guard<MutexLock> lock(mutex_);
    config_data_.clear();
}

bool IniConfigParser::ParseLine(const std::string& line, std::string& current_section) {
    std::string trimmed = TrimLine(line);
    
    // 跳过空行和注释
    if (trimmed.empty() || trimmed[0] == '#' || trimmed[0] == ';') {
        return true;
    }
    
    // 解析节名
    if (trimmed[0] == '[') {
        return ParseSection(trimmed, current_section);
    }
    
    // 解析键值对
    if (!current_section.empty()) {
        std::string key, value;
        if (ParseKeyValue(trimmed, key, value)) {
            config_data_[current_section][key] = value;
            return true;
        }

        // 对于数据点配置段，处理没有等号的配置行
        if (current_section == "YC" || current_section == "YX" || current_section == "YT" ||
            current_section == "YK" || current_section == "YS") {
            // 检查是否包含逗号（数据点配置行的特征）
            if (trimmed.find(',') != std::string::npos) {
                // 使用行号作为键，整行作为值
                static int line_counter = 0;
                std::string line_key = "line_" + std::to_string(++line_counter);
                config_data_[current_section][line_key] = trimmed;
                return true;
            }
        }
    }

    return false;
}

std::string IniConfigParser::TrimLine(const std::string& line) const {
    return utils::StringUtils::Trim(line);
}

bool IniConfigParser::ParseSection(const std::string& line, std::string& section) const {
    size_t end_pos = line.find(']');
    if (end_pos == std::string::npos || end_pos <= 1) {
        return false;
    }
    
    section = line.substr(1, end_pos - 1);
    section = utils::StringUtils::Trim(section);
    
    return !section.empty();
}

bool IniConfigParser::ParseKeyValue(const std::string& line, std::string& key, std::string& value) const {
    size_t eq_pos = line.find('=');
    if (eq_pos == std::string::npos) {
        return false;
    }
    
    key = utils::StringUtils::Trim(line.substr(0, eq_pos));
    value = utils::StringUtils::Trim(line.substr(eq_pos + 1));
    
    // 移除值中的注释
    size_t comment_pos = value.find('#');
    if (comment_pos != std::string::npos) {
        value = utils::StringUtils::Trim(value.substr(0, comment_pos));
    }
    
    return !key.empty();
}

// ConfigManager 实现
Result<bool> ConfigManager::LoadConfig(const std::string& config_file) {
    // 自动检测配置文件格式
    std::string extension = config_file.substr(config_file.find_last_of('.') + 1);
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

    if (extension == "xml") {
        return LoadXmlConfig(config_file);
    } else {
        // 默认使用 INI 格式
        std::lock_guard<MutexLock> lock(config_mutex_);

        if (!parser_) {
            parser_ = std::make_unique<IniConfigParser>();
        }

        auto result = parser_->LoadFile(config_file);
        if (!result.IsSuccess()) {
            return result;
        }

        config_file_ = config_file;
        last_modification_time_ = GetFileModificationTime(config_file);

        // 验证配置
        auto validate_result = ValidateConfig();
        if (!validate_result.IsSuccess()) {
            WRITE_WARN_LOG("配置验证失败: %s", validate_result.error_message.c_str());
        }

        WRITE_INFO_LOG("配置文件加载成功: %s", config_file.c_str());

        return Result<bool>(true);
    }
}

Result<bool> ConfigManager::LoadXmlConfig(const std::string& config_file) {
    std::lock_guard<MutexLock> lock(config_mutex_);

    XmlConfigParser xml_parser;
    auto service_result = xml_parser.ParseConfigFile(config_file);

    if (!service_result.IsSuccess()) {
        return Result<bool>(ErrorCode::CONFIG_ERROR,
            "XML 配置解析失败: " + service_result.error_message);
    }

    // 存储解析后的服务参数
    service_param_ = service_result.data;
    config_file_ = config_file;
    last_modification_time_ = GetFileModificationTime(config_file);

    WRITE_INFO_LOG("XML 配置文件加载成功: %s", config_file.c_str());

    return Result<bool>(true);
}

Result<bool> ConfigManager::ReloadConfig() {
    if (config_file_.empty()) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "No config file loaded");
    }
    
    return LoadConfig(config_file_);
}

Result<ServiceParam> ConfigManager::GetServiceParam() const {
    std::lock_guard<MutexLock> lock(config_mutex_);

    // 如果有 XML 配置数据，直接返回
    if (!service_param_.service_name.empty()) {
        return Result<ServiceParam>(service_param_);
    }

    // 否则使用 INI 解析
    return ParseServiceConfig();
}

Result<DeviceParam> ConfigManager::GetDeviceParam(int device_id) const {
    std::lock_guard<MutexLock> lock(config_mutex_);

    // 如果有 XML 配置数据，从中查找设备
    if (!service_param_.service_name.empty()) {
        auto it = service_param_.devices.find(device_id);
        if (it != service_param_.devices.end()) {
            return Result<DeviceParam>(it->second);
        }
        return Result<DeviceParam>(ErrorCode::CONFIG_ERROR,
            "设备 " + std::to_string(device_id) + " 未找到");
    }

    // 否则使用 INI 解析
    auto device_sections = FindDeviceSections();
    for (const auto& section : device_sections) {
        int id = parser_->GetInt(section, ConfigKeys::Device::DEVICE_ID, 0);
        if (id == device_id) {
            return ParseDeviceConfig(section);
        }
    }
    
    return Result<DeviceParam>(ErrorCode::DEVICE_NOT_FOUND,
                              "Device " + std::to_string(device_id) + " not found");
}

Result<std::map<int, DeviceParam>> ConfigManager::GetAllDeviceParams() const {
    std::lock_guard<MutexLock> lock(config_mutex_);

    // 如果有 XML 配置数据，直接返回
    if (!service_param_.service_name.empty()) {
        return Result<std::map<int, DeviceParam>>(service_param_.devices);
    }

    // 否则使用 INI 解析
    std::map<int, DeviceParam> devices;
    auto device_sections = FindDeviceSections();

    for (const auto& section : device_sections) {
        auto device_result = ParseDeviceConfig(section);
        if (device_result.IsSuccess()) {
            devices[device_result.data.device_id] = device_result.data;
        }
    }

    return Result<std::map<int, DeviceParam>>(devices);
}

std::string ConfigManager::GetString(const std::string& section, const std::string& key, const std::string& default_value) const {
    std::lock_guard<MutexLock> lock(config_mutex_);
    return parser_ ? parser_->GetString(section, key, default_value) : default_value;
}

int ConfigManager::GetInt(const std::string& section, const std::string& key, int default_value) const {
    std::lock_guard<MutexLock> lock(config_mutex_);
    return parser_ ? parser_->GetInt(section, key, default_value) : default_value;
}

double ConfigManager::GetDouble(const std::string& section, const std::string& key, double default_value) const {
    std::lock_guard<MutexLock> lock(config_mutex_);
    return parser_ ? parser_->GetDouble(section, key, default_value) : default_value;
}

bool ConfigManager::GetBool(const std::string& section, const std::string& key, bool default_value) const {
    std::lock_guard<MutexLock> lock(config_mutex_);
    return parser_ ? parser_->GetBool(section, key, default_value) : default_value;
}

Result<DeviceTemplate> ConfigManager::GetDeviceTemplate(int device_id) const {
    std::lock_guard<MutexLock> lock(config_mutex_);

    auto device_sections = FindDeviceSections();
    for (const auto& section : device_sections) {
        int id = parser_->GetInt(section, ConfigKeys::Device::DEVICE_ID, 0);
        if (id == device_id) {
            return ParseDeviceTemplate(section);
        }
    }

    return Result<DeviceTemplate>(ErrorCode::DEVICE_NOT_FOUND,
                                 "Device template " + std::to_string(device_id) + " not found");
}

Result<std::map<int, DeviceTemplate>> ConfigManager::GetAllDeviceTemplates() const {
    std::lock_guard<MutexLock> lock(config_mutex_);

    std::map<int, DeviceTemplate> templates;
    auto device_sections = FindDeviceSections();

    for (const auto& section : device_sections) {
        auto template_result = ParseDeviceTemplate(section);
        if (template_result.IsSuccess()) {
            templates[template_result.data.device_id] = template_result.data;
        }
    }

    return Result<std::map<int, DeviceTemplate>>(templates);
}

// GlobalConfigManager 实现
ConfigManager& GlobalConfigManager::GetInstance() {
    static ConfigManager instance;
    return instance;
}

// 私有方法实现
Result<ServiceParam> ConfigManager::ParseServiceConfig() const {
    ServiceParam param;

    param.redis_ip = parser_->GetString(ConfigKeys::Service::SECTION, ConfigKeys::Service::REDIS_IP, "127.0.0.1");
    param.redis_port = parser_->GetInt(ConfigKeys::Service::SECTION, ConfigKeys::Service::REDIS_PORT, 6379);
    param.report_interval_ms = parser_->GetInt(ConfigKeys::Service::SECTION, ConfigKeys::Service::REPORT_TIME, 200);

    // 解析所有设备
    auto device_sections = FindDeviceSections();
    for (const auto& section : device_sections) {
        auto device_result = ParseDeviceConfig(section);
        if (device_result.IsSuccess()) {
            param.devices[device_result.data.device_id] = device_result.data;
        }
    }

    return Result<ServiceParam>(param);
}

Result<DeviceParam> ConfigManager::ParseDeviceConfig(const std::string& device_section) const {
    DeviceParam param;

    param.device_id = parser_->GetInt(device_section, ConfigKeys::Device::DEVICE_ID, 0);
    param.device_name = parser_->GetString(device_section, ConfigKeys::Device::DEVICE_NAME, "Unknown");
    param.slave_id = parser_->GetInt(device_section, "SlaveID", 1);

    // 检查是否有外部点表文件配置
    param.point_file = parser_->GetString(device_section, ConfigKeys::Device::POINT_FILE, "");

    int comm_type = parser_->GetInt(device_section, ConfigKeys::Device::COMM_TYPE, 0);
    param.comm_type = static_cast<CommType>(comm_type);

    // 解析通信参数
    if (param.comm_type == CommType::RTU) {
        auto rtu_result = ParseRtuParam(device_section);
        if (rtu_result.IsSuccess()) {
            param.rtu_param = rtu_result.data;
        }
    } else if (param.comm_type == CommType::TCP) {
        auto tcp_result = ParseTcpParam(device_section);
        if (tcp_result.IsSuccess()) {
            param.tcp_param = tcp_result.data;
        }
    }

    // 解析数据采集配置
    param.scan_interval_ms = parser_->GetInt(device_section, "ScanInterval", 1000);
    param.enable_auto_scan = parser_->GetBool(device_section, "EnableAutoScan", true);

    // 解析内嵌的数据点配置
    auto point_config_result = ParseDevicePointConfig(param.device_id);
    if (point_config_result.IsSuccess()) {
        param.point_config = point_config_result.data;
    }

    return Result<DeviceParam>(param);
}

Result<RtuCommParam> ConfigManager::ParseRtuParam(const std::string& device_section) const {
    RtuCommParam param;

    param.device = parser_->GetString(device_section, ConfigKeys::Device::DEVICE, "/dev/ttyS1");
    param.baud = parser_->GetInt(device_section, ConfigKeys::Device::BAUD, 9600);

    std::string parity_str = parser_->GetString(device_section, ConfigKeys::Device::PARITY, "N");
    param.parity = parity_str.empty() ? 'N' : parity_str[0];

    param.data_bit = parser_->GetInt(device_section, ConfigKeys::Device::DATA_BIT, 8);
    param.stop_bit = parser_->GetInt(device_section, ConfigKeys::Device::STOP_BIT, 1);
    param.mode = parser_->GetInt(device_section, ConfigKeys::Device::MODE, 1);
    param.timeout_ms = parser_->GetInt(device_section, ConfigKeys::Device::TIMEOUT, 1000);

    return Result<RtuCommParam>(param);
}

Result<TcpCommParam> ConfigManager::ParseTcpParam(const std::string& device_section) const {
    TcpCommParam param;

    param.ip = parser_->GetString(device_section, ConfigKeys::Device::IP, "127.0.0.1");
    param.port = parser_->GetInt(device_section, ConfigKeys::Device::PORT, 502);
    param.timeout_ms = parser_->GetInt(device_section, ConfigKeys::Device::TIMEOUT, 3000);

    return Result<TcpCommParam>(param);
}

Result<DeviceTemplate> ConfigManager::ParseDeviceTemplate(const std::string& device_section) const {
    DeviceTemplate template_config;

    template_config.device_id = parser_->GetInt(device_section, ConfigKeys::Device::DEVICE_ID, 0);
    template_config.device_name = parser_->GetString(device_section, ConfigKeys::Device::DEVICE_NAME, "Unknown");

    // 解析各类型参数
    auto yc_result = ParseTemplateParams(device_section, ConfigKeys::Device::YC_PARAM, TemplateParamType::YC);
    if (yc_result.IsSuccess()) {
        template_config.yc_params = yc_result.data;
    }

    auto yx_result = ParseTemplateParams(device_section, ConfigKeys::Device::YX_PARAM, TemplateParamType::YX);
    if (yx_result.IsSuccess()) {
        template_config.yx_params = yx_result.data;
    }

    auto ym_result = ParseTemplateParams(device_section, ConfigKeys::Device::YM_PARAM, TemplateParamType::YM);
    if (ym_result.IsSuccess()) {
        template_config.ym_params = ym_result.data;
    }

    auto yk_result = ParseTemplateParams(device_section, ConfigKeys::Device::YK_PARAM, TemplateParamType::YK);
    if (yk_result.IsSuccess()) {
        template_config.yk_params = yk_result.data;
    }

    auto ys_result = ParseTemplateParams(device_section, ConfigKeys::Device::YS_PARAM, TemplateParamType::YS);
    if (ys_result.IsSuccess()) {
        template_config.ys_params = ys_result.data;
    }

    auto time_result = ParseTemplateParams(device_section, ConfigKeys::Device::TIME_SYNC_PARAM, TemplateParamType::TIME_SYNC);
    if (time_result.IsSuccess()) {
        template_config.time_params = time_result.data;
    }

    return Result<DeviceTemplate>(template_config);
}

std::vector<std::string> ConfigManager::FindDeviceSections() const {
    std::vector<std::string> device_sections;
    auto sections = parser_->GetSections();

    for (const auto& section : sections) {
        // 查找包含 DeviceID 的节
        if (parser_->HasKey(section, ConfigKeys::Device::DEVICE_ID)) {
            device_sections.push_back(section);
        }
    }

    return device_sections;
}

Result<bool> ConfigManager::ValidateConfig() const {
    // 验证服务参数
    auto service_result = ParseServiceConfig();
    if (!service_result.IsSuccess()) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Invalid service config");
    }

    auto validate_service_result = ValidateServiceParam(service_result.data);
    if (!validate_service_result.IsSuccess()) {
        return validate_service_result;
    }

    // 验证设备参数
    for (const auto& device_pair : service_result.data.devices) {
        auto validate_device_result = ValidateDeviceParam(device_pair.second);
        if (!validate_device_result.IsSuccess()) {
            return validate_device_result;
        }
    }

    return Result<bool>(true);
}

Result<bool> ConfigManager::ValidateServiceParam(const ServiceParam& param) const {
    if (param.redis_port <= 0 || param.redis_port > 65535) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Invalid Redis port");
    }

    if (param.report_interval_ms < 100 || param.report_interval_ms > 60000) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Invalid report interval");
    }

    return Result<bool>(true);
}

Result<bool> ConfigManager::ValidateDeviceParam(const DeviceParam& param) const {
    if (param.device_id <= 0) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Invalid device ID");
    }

    if (param.device_name.empty()) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Empty device name");
    }

    if (param.comm_type == CommType::RTU) {
        if (param.rtu_param.device.empty()) {
            return Result<bool>(ErrorCode::CONFIG_ERROR, "Empty RTU device path");
        }

        if (param.rtu_param.baud <= 0) {
            return Result<bool>(ErrorCode::CONFIG_ERROR, "Invalid baud rate");
        }
    } else if (param.comm_type == CommType::TCP) {
        if (param.tcp_param.ip.empty()) {
            return Result<bool>(ErrorCode::CONFIG_ERROR, "Empty TCP IP address");
        }

        if (param.tcp_param.port <= 0 || param.tcp_param.port > 65535) {
            return Result<bool>(ErrorCode::CONFIG_ERROR, "Invalid TCP port");
        }
    }

    return Result<bool>(true);
}

Result<std::vector<TemplateParam>> ConfigManager::ParseTemplateParams(const std::string& section,
                                                                      const std::string& key_prefix,
                                                                      TemplateParamType param_type) const {
    std::vector<TemplateParam> params;

    // 查找所有匹配的键（支持多行配置）
    auto keys = parser_->GetKeys(section);
    for (const auto& key : keys) {
        if (key.find(key_prefix) == 0) {
            std::string param_str = parser_->GetString(section, key, "");
            if (!param_str.empty()) {
                auto param_result = ParseSingleTemplateParam(param_str, param_type);
                if (param_result.IsSuccess()) {
                    params.push_back(param_result.data);
                } else {
                    WRITE_WARN_LOG("Failed to parse template param: %s = %s, error: %s",
                                  key.c_str(), param_str.c_str(), param_result.error_message.c_str());
                }
            }
        }
    }

    return Result<std::vector<TemplateParam>>(params);
}

Result<TemplateParam> ConfigManager::ParseSingleTemplateParam(const std::string& param_str,
                                                              TemplateParamType param_type) const {
    TemplateParam param;
    param.type = param_type;

    // 分割参数字符串
    std::vector<std::string> parts;
    std::stringstream ss(param_str);
    std::string item;

    while (std::getline(ss, item, ',')) {
        parts.push_back(utils::StringUtils::Trim(item));
    }

    try {
        if (param_type == TemplateParamType::TIME_SYNC) {
            // 校时格式：起始地址，数量，时间位置字符串
            if (parts.size() < 3) {
                return Result<TemplateParam>(ErrorCode::CONFIG_ERROR, "Invalid time sync param format");
            }

            param.start_addr = static_cast<uint16_t>(ParseHexString(parts[0]));
            param.count = static_cast<uint16_t>(ParseHexString(parts[1]));
            param.time_format = parts[2];

        } else {
            // 其他类型格式：功能码，起始地址，数量，对应模板FUN/INF，类型，参数，符号参数，分解参数
            if (parts.size() < 4) {
                return Result<TemplateParam>(ErrorCode::CONFIG_ERROR, "Invalid template param format");
            }

            param.func_code = static_cast<uint16_t>(ParseHexString(parts[0]));
            param.start_addr = static_cast<uint16_t>(ParseHexString(parts[1]));
            param.count = static_cast<uint16_t>(ParseHexString(parts[2]));
            param.template_fun_inf = static_cast<uint16_t>(ParseHexString(parts[3]));

            // 可选参数，默认为0
            param.data_type = parts.size() > 4 ? static_cast<ModbusDataType>(ParseHexString(parts[4])) : ModbusDataType::UINT16;
            param.param = parts.size() > 5 ? ParseHexString(parts[5]) : 0;
            param.sign_param = parts.size() > 6 ? ParseHexString(parts[6]) : 0;
            param.split_param = parts.size() > 7 ? ParseHexString(parts[7]) : 0;
        }

    } catch (const std::exception& e) {
        return Result<TemplateParam>(ErrorCode::CONFIG_ERROR,
                                    "Failed to parse template param: " + std::string(e.what()));
    }

    return Result<TemplateParam>(param);
}

uint32_t ConfigManager::ParseHexString(const std::string& hex_str) const {
    if (hex_str.empty()) {
        return 0;
    }

    try {
        // 支持带0x前缀和不带前缀的16进制字符串
        if (hex_str.length() >= 2 && hex_str.substr(0, 2) == "0x") {
            return static_cast<uint32_t>(std::stoul(hex_str, nullptr, 16));
        } else {
            return static_cast<uint32_t>(std::stoul(hex_str, nullptr, 16));
        }
    } catch (const std::exception&) {
        // 如果解析失败，尝试作为十进制数解析
        try {
            return static_cast<uint32_t>(std::stoul(hex_str, nullptr, 10));
        } catch (const std::exception&) {
            return 0;
        }
    }
}

Result<DevicePointConfig> ConfigManager::ParseDevicePointConfig(int device_id) const {
    DevicePointConfig config;

    // 查找设备对应的数据点配置段
    auto sections = parser_->GetSections();

    // 找到设备段的位置
    std::string device_section = "Device" + std::to_string(device_id);
    auto device_it = std::find(sections.begin(), sections.end(), device_section);

    std::cout << "查找设备段: " << device_section << std::endl;
    std::cout << "所有段: ";
    for (const auto& s : sections) {
        std::cout << "[" << s << "] ";
    }
    std::cout << std::endl;

    // 检查是否有数据点配置段
    bool has_yc = std::find(sections.begin(), sections.end(), "YC") != sections.end();
    bool has_yx = std::find(sections.begin(), sections.end(), "YX") != sections.end();
    std::cout << "是否有YC段: " << (has_yc ? "是" : "否") << std::endl;
    std::cout << "是否有YX段: " << (has_yx ? "是" : "否") << std::endl;

    if (device_it != sections.end()) {
        std::cout << "找到设备段: " << device_section << std::endl;
        // 从设备段后面开始查找数据点配置段
        for (auto it = device_it + 1; it != sections.end(); ++it) {
            const std::string& section = *it;

            // 如果遇到下一个设备段或其他非数据点段，停止查找
            if (section.find("Device") == 0 || section == "Service" || section == "Advanced") {
                break;
            }

            // 检查是否是数据点配置段
            if (section == "YC" || section == "YX" || section == "YT" ||
                section == "YK" || section == "YS") {

                auto point_configs = ParsePointSection(section);
                if (point_configs.IsSuccess()) {
                    if (section == "YC") {
                        config.yc_configs = point_configs.data;
                    } else if (section == "YX") {
                        config.yx_configs = point_configs.data;
                    } else if (section == "YT") {
                        config.yt_configs = point_configs.data;
                    } else if (section == "YK") {
                        config.yk_configs = point_configs.data;
                    } else if (section == "YS") {
                        config.ys_configs = point_configs.data;
                    }
                }
            }
        }
    }

    return Result<DevicePointConfig>(config);
}

Result<std::vector<ModbusPointConfig>> ConfigManager::ParsePointSection(const std::string& section) const {
    std::vector<ModbusPointConfig> configs;

    // 使用 INI 解析器的数据
    auto keys = parser_->GetKeys(section);
    for (const auto& key : keys) {
        std::string value = parser_->GetString(section, key, "");
        if (value.empty()) {
            continue;
        }

        // 检查是否包含逗号（数据点配置行的特征）
        if (value.find(',') != std::string::npos) {
            auto config_result = ParsePointConfigLine(value);
            if (config_result.IsSuccess()) {
                configs.push_back(config_result.data);
            } else {
                std::cerr << "解析数据点配置失败 [" << section << "]: " << value
                          << " - " << config_result.error_message << std::endl;
            }
        }
    }

    return Result<std::vector<ModbusPointConfig>>(configs);
}

Result<ModbusPointConfig> ConfigManager::ParsePointConfigLine(const std::string& line) const {
    // 格式: 功能码,起始地址,数量,装置内序号起始,类型,参数
    // 示例: 03,0000,5,1,0

    std::vector<std::string> parts = utils::StringUtils::Split(line, ",");
    if (parts.size() < 5) {
        return Result<ModbusPointConfig>(ErrorCode::CONFIG_ERROR,
            "配置行格式错误，至少需要5个参数");
    }

    ModbusPointConfig config;

    try {
        // 解析功能码
        config.function_code = std::stoi(utils::StringUtils::Trim(parts[0]));

        // 解析起始地址 (16进制)
        std::string addr_str = utils::StringUtils::Trim(parts[1]);
        config.start_addr = std::stoi(addr_str, nullptr, 16);

        // 解析数量
        config.count = std::stoi(utils::StringUtils::Trim(parts[2]));

        // 解析Redis起始序号
        config.redis_start_index = std::stoi(utils::StringUtils::Trim(parts[3]));

        // 解析数据类型
        config.data_type = std::stoi(utils::StringUtils::Trim(parts[4]));

        // 解析参数 (可选)
        if (parts.size() > 5) {
            config.param = std::stoi(utils::StringUtils::Trim(parts[5]));
        }

    } catch (const std::exception& e) {
        return Result<ModbusPointConfig>(ErrorCode::CONFIG_ERROR,
            "解析配置参数失败: " + std::string(e.what()));
    }

    return Result<ModbusPointConfig>(config);
}

uint64_t ConfigManager::GetFileModificationTime(const std::string& filename) const {
    try {
        auto ftime = std::filesystem::last_write_time(filename);
        auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
            ftime - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now());
        return std::chrono::duration_cast<std::chrono::milliseconds>(sctp.time_since_epoch()).count();
    } catch (const std::exception&) {
        return 0;
    }
}

} // namespace modbus
