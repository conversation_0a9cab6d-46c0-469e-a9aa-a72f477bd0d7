#ifndef XML_CONFIG_PARSER_H
#define XML_CONFIG_PARSER_H

#include "../types/modbus_types.h"
#include "../utils/thread_pool.h"
#include <string>
#include <vector>
#include <map>
#include <memory>

namespace modbus {

// 简单的 XML 节点
class XmlNode {
public:
    std::string name;
    std::string text;
    std::map<std::string, std::string> attributes;
    std::vector<std::shared_ptr<XmlNode>> children;
    std::shared_ptr<XmlNode> parent;

    XmlNode(const std::string& node_name = "") : name(node_name) {}

    // 查找子节点
    std::shared_ptr<XmlNode> FindChild(const std::string& child_name) const;
    
    // 查找所有同名子节点
    std::vector<std::shared_ptr<XmlNode>> FindChildren(const std::string& child_name) const;
    
    // 获取属性值
    std::string GetAttribute(const std::string& attr_name, const std::string& default_value = "") const;
    
    // 获取整数属性值
    int GetIntAttribute(const std::string& attr_name, int default_value = 0) const;
    
    // 获取布尔属性值
    bool GetBoolAttribute(const std::string& attr_name, bool default_value = false) const;
    
    // 获取子节点文本内容
    std::string GetChildText(const std::string& child_name, const std::string& default_value = "") const;
    
    // 获取子节点整数内容
    int GetChildInt(const std::string& child_name, int default_value = 0) const;
    
    // 获取子节点布尔内容
    bool GetChildBool(const std::string& child_name, bool default_value = false) const;

    // 获取子节点浮点数内容
    double GetChildDouble(const std::string& child_name, double default_value = 0.0) const;

    // 检查是否有子节点
    bool HasChild(const std::string& child_name) const;
};

// XML 配置解析器
class XmlConfigParser {
public:
    XmlConfigParser() = default;
    ~XmlConfigParser() = default;

    // 解析 XML 配置文件
    Result<ServiceParam> ParseConfigFile(const std::string& file_path);
    
    // 解析 XML 字符串
    Result<std::shared_ptr<XmlNode>> ParseXml(const std::string& xml_str);

private:
    // 解析服务配置
    Result<ServiceParam> ParseServiceConfig(std::shared_ptr<XmlNode> root);
    
    // 解析设备配置列表
    Result<std::vector<DeviceParam>> ParseDevicesConfig(std::shared_ptr<XmlNode> devices_node);
    
    // 解析单个设备配置
    Result<DeviceParam> ParseDeviceConfig(std::shared_ptr<XmlNode> device_node);
    
    // 解析通信配置
    Result<bool> ParseCommunicationConfig(std::shared_ptr<XmlNode> comm_node, DeviceParam& device);
    
    // 解析数据点配置
    Result<DevicePointConfig> ParseDataPointsConfig(std::shared_ptr<XmlNode> points_node);
    
    // 解析数据点配置行
    Result<ModbusPointConfig> ParsePointConfigLine(const std::string& config_line);
    
    // XML 解析辅助方法
    std::shared_ptr<XmlNode> ParseElement(const std::string& xml, size_t& pos);
    std::string ParseElementName(const std::string& xml, size_t& pos);
    std::map<std::string, std::string> ParseAttributes(const std::string& xml, size_t& pos);
    std::string ParseElementContent(const std::string& xml, size_t& pos, const std::string& element_name);
    std::string ParseAttributeValue(const std::string& xml, size_t& pos);
    
    // 跳过空白字符和注释
    void SkipWhitespaceAndComments(const std::string& xml, size_t& pos);
    
    // 跳过 XML 注释
    void SkipComment(const std::string& xml, size_t& pos);
    
    // 解码 XML 实体
    std::string DecodeXmlEntities(const std::string& text);
    
    // 查找标签结束位置
    size_t FindTagEnd(const std::string& xml, size_t start_pos, const std::string& tag_name);
    
    // 检查是否是自闭合标签
    bool IsSelfClosingTag(const std::string& xml, size_t start_pos, size_t end_pos);
};

} // namespace modbus

#endif // XML_CONFIG_PARSER_H
