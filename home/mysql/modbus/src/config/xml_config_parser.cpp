#include "xml_config_parser.h"
#include "../utils/utils.h"
#include "../utils/logger.h"
#include <fstream>
#include <sstream>
#include <cctype>
#include <algorithm>
#include <iostream>

namespace modbus {

// XmlNode 实现
std::shared_ptr<XmlNode> XmlNode::FindChild(const std::string& child_name) const {
    for (const auto& child : children) {
        if (child->name == child_name) {
            return child;
        }
    }
    return nullptr;
}

std::vector<std::shared_ptr<XmlNode>> XmlNode::FindChildren(const std::string& child_name) const {
    std::vector<std::shared_ptr<XmlNode>> result;
    for (const auto& child : children) {
        if (child->name == child_name) {
            result.push_back(child);
        }
    }
    return result;
}

std::string XmlNode::GetAttribute(const std::string& attr_name, const std::string& default_value) const {
    auto it = attributes.find(attr_name);
    if (it != attributes.end()) {
        return it->second;
    }
    return default_value;
}

int XmlNode::GetIntAttribute(const std::string& attr_name, int default_value) const {
    std::string value = GetAttribute(attr_name);
    if (!value.empty()) {
        try {
            return std::stoi(value);
        } catch (const std::exception&) {
            // 忽略转换错误
        }
    }
    return default_value;
}

bool XmlNode::GetBoolAttribute(const std::string& attr_name, bool default_value) const {
    std::string value = GetAttribute(attr_name);
    if (!value.empty()) {
        std::transform(value.begin(), value.end(), value.begin(), ::tolower);
        return value == "true" || value == "1" || value == "yes";
    }
    return default_value;
}

std::string XmlNode::GetChildText(const std::string& child_name, const std::string& default_value) const {
    auto child = FindChild(child_name);
    if (child) {
        return utils::StringUtils::Trim(child->text);
    }
    return default_value;
}

int XmlNode::GetChildInt(const std::string& child_name, int default_value) const {
    std::string value = GetChildText(child_name);
    if (!value.empty()) {
        try {
            return std::stoi(value);
        } catch (const std::exception&) {
            // 忽略转换错误
        }
    }
    return default_value;
}

bool XmlNode::GetChildBool(const std::string& child_name, bool default_value) const {
    std::string value = GetChildText(child_name);
    if (!value.empty()) {
        std::transform(value.begin(), value.end(), value.begin(), ::tolower);
        return value == "true" || value == "1" || value == "yes";
    }
    return default_value;
}

double XmlNode::GetChildDouble(const std::string& child_name, double default_value) const {
    std::string value = GetChildText(child_name);
    if (!value.empty()) {
        try {
            return std::stod(value);
        } catch (const std::exception&) {
            // 忽略转换错误
        }
    }
    return default_value;
}

bool XmlNode::HasChild(const std::string& child_name) const {
    return FindChild(child_name) != nullptr;
}

// XmlConfigParser 实现
Result<ServiceParam> XmlConfigParser::ParseConfigFile(const std::string& file_path) {
    std::ifstream file(file_path);
    if (!file.is_open()) {
        return Result<ServiceParam>(ErrorCode::CONFIG_ERROR, 
            "无法打开配置文件: " + file_path);
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    std::string xml_content = buffer.str();
    file.close();

    auto xml_result = ParseXml(xml_content);
    if (!xml_result.IsSuccess()) {
        return Result<ServiceParam>(ErrorCode::CONFIG_ERROR, 
            "XML 解析失败: " + xml_result.error_message);
    }

    return ParseServiceConfig(xml_result.data);
}

Result<std::shared_ptr<XmlNode>> XmlConfigParser::ParseXml(const std::string& xml_str) {
    size_t pos = 0;
    SkipWhitespaceAndComments(xml_str, pos);
    
    // 跳过 XML 声明
    if (xml_str.substr(pos, 5) == "<?xml") {
        size_t end_pos = xml_str.find("?>", pos);
        if (end_pos != std::string::npos) {
            pos = end_pos + 2;
            SkipWhitespaceAndComments(xml_str, pos);
        }
    }
    
    if (pos >= xml_str.length()) {
        return Result<std::shared_ptr<XmlNode>>(ErrorCode::CONFIG_ERROR, "空的 XML 内容");
    }
    
    auto root = ParseElement(xml_str, pos);
    if (!root) {
        return Result<std::shared_ptr<XmlNode>>(ErrorCode::CONFIG_ERROR, "XML 解析失败");
    }
    
    return Result<std::shared_ptr<XmlNode>>(root);
}

std::shared_ptr<XmlNode> XmlConfigParser::ParseElement(const std::string& xml, size_t& pos) {
    SkipWhitespaceAndComments(xml, pos);
    
    if (pos >= xml.length() || xml[pos] != '<') {
        return nullptr;
    }
    
    pos++; // 跳过 '<'
    
    // 解析元素名
    std::string element_name = ParseElementName(xml, pos);
    if (element_name.empty()) {
        return nullptr;
    }
    
    auto node = std::make_shared<XmlNode>(element_name);
    
    // 解析属性
    node->attributes = ParseAttributes(xml, pos);
    
    SkipWhitespaceAndComments(xml, pos);
    
    // 检查是否是自闭合标签
    if (pos < xml.length() && xml.substr(pos, 2) == "/>") {
        pos += 2;
        return node;
    }
    
    // 期望 '>'
    if (pos >= xml.length() || xml[pos] != '>') {
        return nullptr;
    }
    pos++;
    
    // 解析内容和子元素
    std::string content = ParseElementContent(xml, pos, element_name);
    
    // 解析内容中的子元素
    size_t content_pos = 0;
    std::string text_content;
    
    while (content_pos < content.length()) {
        SkipWhitespaceAndComments(content, content_pos);
        
        if (content_pos >= content.length()) {
            break;
        }
        
        if (content[content_pos] == '<') {
            // 解析子元素
            auto child = ParseElement(content, content_pos);
            if (child) {
                child->parent = node;
                node->children.push_back(child);
            }
        } else {
            // 收集文本内容
            size_t text_start = content_pos;
            while (content_pos < content.length() && content[content_pos] != '<') {
                content_pos++;
            }
            text_content += content.substr(text_start, content_pos - text_start);
        }
    }
    
    node->text = DecodeXmlEntities(utils::StringUtils::Trim(text_content));


    
    return node;
}

std::string XmlConfigParser::ParseElementName(const std::string& xml, size_t& pos) {
    size_t start = pos;
    
    while (pos < xml.length() && 
           (std::isalnum(xml[pos]) || xml[pos] == '_' || xml[pos] == '-' || xml[pos] == ':')) {
        pos++;
    }
    
    return xml.substr(start, pos - start);
}

std::map<std::string, std::string> XmlConfigParser::ParseAttributes(const std::string& xml, size_t& pos) {
    std::map<std::string, std::string> attributes;
    
    while (pos < xml.length()) {
        SkipWhitespaceAndComments(xml, pos);
        
        if (pos >= xml.length() || xml[pos] == '>' || xml.substr(pos, 2) == "/>") {
            break;
        }
        
        // 解析属性名
        std::string attr_name = ParseElementName(xml, pos);
        if (attr_name.empty()) {
            break;
        }
        
        SkipWhitespaceAndComments(xml, pos);
        
        // 期望 '='
        if (pos >= xml.length() || xml[pos] != '=') {
            break;
        }
        pos++;
        
        SkipWhitespaceAndComments(xml, pos);
        
        // 解析属性值
        std::string attr_value = ParseAttributeValue(xml, pos);
        
        attributes[attr_name] = DecodeXmlEntities(attr_value);
    }
    
    return attributes;
}

std::string XmlConfigParser::ParseAttributeValue(const std::string& xml, size_t& pos) {
    if (pos >= xml.length()) {
        return "";
    }
    
    char quote = xml[pos];
    if (quote != '"' && quote != '\'') {
        return "";
    }
    
    pos++; // 跳过开始引号
    size_t start = pos;
    
    while (pos < xml.length() && xml[pos] != quote) {
        pos++;
    }
    
    std::string value = xml.substr(start, pos - start);
    
    if (pos < xml.length()) {
        pos++; // 跳过结束引号
    }
    
    return value;
}

std::string XmlConfigParser::ParseElementContent(const std::string& xml, size_t& pos, const std::string& element_name) {
    size_t start = pos;
    size_t end_pos = FindTagEnd(xml, pos, element_name);
    
    if (end_pos == std::string::npos) {
        return "";
    }
    
    std::string content = xml.substr(start, end_pos - start);
    pos = end_pos + element_name.length() + 3; // 跳过 "</" + element_name + ">"
    
    return content;
}

size_t XmlConfigParser::FindTagEnd(const std::string& xml, size_t start_pos, const std::string& tag_name) {
    std::string start_tag = "<" + tag_name;
    std::string end_tag = "</" + tag_name + ">";

    size_t pos = start_pos;
    int depth = 1; // 我们已经在一个标签内部

    while (pos < xml.length() && depth > 0) {
        // 查找下一个相关标签
        size_t next_start = xml.find(start_tag, pos);
        size_t next_end = xml.find(end_tag, pos);

        if (next_end == std::string::npos) {
            return std::string::npos; // 没有找到结束标签
        }

        if (next_start != std::string::npos && next_start < next_end) {
            // 找到了嵌套的开始标签
            // 检查这是否真的是一个开始标签（不是属性中的文本）
            size_t check_pos = next_start + start_tag.length();
            if (check_pos < xml.length() && (xml[check_pos] == '>' || xml[check_pos] == ' ' || xml[check_pos] == '\t' || xml[check_pos] == '\n')) {
                depth++;
                pos = next_start + start_tag.length();
            } else {
                pos = next_start + 1;
            }
        } else {
            // 找到了结束标签
            depth--;
            if (depth == 0) {
                return next_end;
            }
            pos = next_end + end_tag.length();
        }
    }

    return std::string::npos;
}

void XmlConfigParser::SkipWhitespaceAndComments(const std::string& xml, size_t& pos) {
    while (pos < xml.length()) {
        if (std::isspace(xml[pos])) {
            pos++;
        } else if (xml.substr(pos, 4) == "<!--") {
            SkipComment(xml, pos);
        } else {
            break;
        }
    }
}

void XmlConfigParser::SkipComment(const std::string& xml, size_t& pos) {
    size_t end_pos = xml.find("-->", pos);
    if (end_pos != std::string::npos) {
        pos = end_pos + 3;
    } else {
        pos = xml.length();
    }
}

std::string XmlConfigParser::DecodeXmlEntities(const std::string& text) {
    std::string result = text;
    
    // 替换常见的 XML 实体
    size_t pos = 0;
    while ((pos = result.find("&lt;", pos)) != std::string::npos) {
        result.replace(pos, 4, "<");
        pos += 1;
    }
    
    pos = 0;
    while ((pos = result.find("&gt;", pos)) != std::string::npos) {
        result.replace(pos, 4, ">");
        pos += 1;
    }
    
    pos = 0;
    while ((pos = result.find("&amp;", pos)) != std::string::npos) {
        result.replace(pos, 5, "&");
        pos += 1;
    }
    
    pos = 0;
    while ((pos = result.find("&quot;", pos)) != std::string::npos) {
        result.replace(pos, 6, "\"");
        pos += 1;
    }
    
    pos = 0;
    while ((pos = result.find("&apos;", pos)) != std::string::npos) {
        result.replace(pos, 6, "'");
        pos += 1;
    }
    
    return result;
}

Result<ServiceParam> XmlConfigParser::ParseServiceConfig(std::shared_ptr<XmlNode> root) {
    ServiceParam service_param;

    // 解析服务配置
    auto service_node = root->FindChild("service");
    if (service_node) {
        service_param.service_name = service_node->GetChildText("name", "Modbus Service");
        service_param.log_level = service_node->GetChildText("log_level", "INFO");
        service_param.log_file = service_node->GetChildText("log_file", "modbus_service.log");
        service_param.report_interval_ms = service_node->GetChildInt("report_interval_ms", 200);
        service_param.max_threads = service_node->GetChildInt("max_threads", 4);

        // 解析 Redis 配置
        auto redis_node = service_node->FindChild("redis");
        if (redis_node) {
            service_param.redis_ip = redis_node->GetChildText("ip", "127.0.0.1");
            service_param.redis_port = redis_node->GetChildInt("port", 6379);
            service_param.redis_password = redis_node->GetChildText("password", "");
            service_param.redis_database = redis_node->GetChildInt("database", 0);
        }
    }

    // 解析设备配置
    auto devices_node = root->FindChild("devices");
    if (devices_node) {
        auto devices_result = ParseDevicesConfig(devices_node);
        if (devices_result.IsSuccess()) {
            for (const auto& device : devices_result.data) {
                service_param.devices[device.device_id] = device;
            }
        } else {
            return Result<ServiceParam>(ErrorCode::CONFIG_ERROR,
                "解析设备配置失败: " + devices_result.error_message);
        }
    }

    return Result<ServiceParam>(service_param);
}

Result<std::vector<DeviceParam>> XmlConfigParser::ParseDevicesConfig(std::shared_ptr<XmlNode> devices_node) {
    std::vector<DeviceParam> devices;

    auto device_nodes = devices_node->FindChildren("device");
    for (const auto& device_node : device_nodes) {
        auto device_result = ParseDeviceConfig(device_node);
        if (device_result.IsSuccess()) {
            devices.push_back(device_result.data);
        } else {
            return Result<std::vector<DeviceParam>>(ErrorCode::CONFIG_ERROR,
                "解析设备配置失败: " + device_result.error_message);
        }
    }

    return Result<std::vector<DeviceParam>>(devices);
}

Result<DeviceParam> XmlConfigParser::ParseDeviceConfig(std::shared_ptr<XmlNode> device_node) {
    DeviceParam device;

    device.device_id = device_node->GetIntAttribute("id", 0);
    device.device_name = device_node->GetAttribute("name", "Unknown Device");
    device.slave_id = device_node->GetChildInt("slave_id", 1);



    // 解析通信配置
    auto comm_node = device_node->FindChild("communication");
    if (comm_node) {
        auto comm_result = ParseCommunicationConfig(comm_node, device);
        if (!comm_result.IsSuccess()) {
            return Result<DeviceParam>(ErrorCode::CONFIG_ERROR,
                "解析通信配置失败: " + comm_result.error_message);
        }
    }

    // 解析扫描配置
    auto scan_node = device_node->FindChild("scan_config");
    if (scan_node) {
        device.scan_interval_ms = scan_node->GetChildInt("scan_interval_ms", 1000);
        device.enable_auto_scan = scan_node->GetChildBool("enable_auto_scan", true);
        device.enable_change_detection = scan_node->GetChildBool("enable_change_detection", false);
        device.change_threshold = scan_node->GetChildDouble("change_threshold", 0.01);
    }

    // 解析数据点配置
    auto points_node = device_node->FindChild("data_points");
    if (points_node) {
        auto points_result = ParseDataPointsConfig(points_node);
        if (points_result.IsSuccess()) {
            device.point_config = points_result.data;
        } else {
            return Result<DeviceParam>(ErrorCode::CONFIG_ERROR,
                "解析数据点配置失败: " + points_result.error_message);
        }
    }

    return Result<DeviceParam>(device);
}

Result<bool> XmlConfigParser::ParseCommunicationConfig(std::shared_ptr<XmlNode> comm_node, DeviceParam& device) {
    std::string comm_type = comm_node->GetAttribute("type", "RTU");

    if (comm_type == "RTU") {
        device.comm_type = CommType::RTU;
        device.rtu_param.device = comm_node->GetChildText("device", "/dev/ttyS0");
        device.rtu_param.baud = comm_node->GetChildInt("baud", 9600);
        device.rtu_param.parity = comm_node->GetChildText("parity", "N")[0];
        device.rtu_param.data_bit = comm_node->GetChildInt("data_bits", 8);
        device.rtu_param.stop_bit = comm_node->GetChildInt("stop_bits", 1);
        device.rtu_param.timeout_ms = comm_node->GetChildInt("timeout", 1000);
    } else if (comm_type == "TCP") {
        device.comm_type = CommType::TCP;
        device.tcp_param.ip = comm_node->GetChildText("ip", "127.0.0.1");
        device.tcp_param.port = comm_node->GetChildInt("port", 502);
        device.tcp_param.timeout_ms = comm_node->GetChildInt("timeout", 3000);
    } else {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "不支持的通信类型: " + comm_type);
    }

    return Result<bool>(true);
}

Result<DevicePointConfig> XmlConfigParser::ParseDataPointsConfig(std::shared_ptr<XmlNode> points_node) {
    DevicePointConfig config;

    // 解析各种数据点类型
    auto yc_node = points_node->FindChild("YC");
    if (yc_node) {
        auto point_nodes = yc_node->FindChildren("point");
        for (const auto& point_node : point_nodes) {
            auto point_result = ParsePointConfigLine(point_node->text);
            if (point_result.IsSuccess()) {
                config.yc_configs.push_back(point_result.data);
            }
        }
    }

    auto yx_node = points_node->FindChild("YX");
    if (yx_node) {
        auto point_nodes = yx_node->FindChildren("point");
        for (const auto& point_node : point_nodes) {
            auto point_result = ParsePointConfigLine(point_node->text);
            if (point_result.IsSuccess()) {
                config.yx_configs.push_back(point_result.data);
            }
        }
    }

    auto yt_node = points_node->FindChild("YT");
    if (yt_node) {
        auto point_nodes = yt_node->FindChildren("point");
        for (const auto& point_node : point_nodes) {
            auto point_result = ParsePointConfigLine(point_node->text);
            if (point_result.IsSuccess()) {
                config.yt_configs.push_back(point_result.data);
            }
        }
    }

    auto yk_node = points_node->FindChild("YK");
    if (yk_node) {
        auto point_nodes = yk_node->FindChildren("point");
        for (const auto& point_node : point_nodes) {
            auto point_result = ParsePointConfigLine(point_node->text);
            if (point_result.IsSuccess()) {
                config.yk_configs.push_back(point_result.data);
            }
        }
    }

    auto ys_node = points_node->FindChild("YS");
    if (ys_node) {
        auto point_nodes = ys_node->FindChildren("point");
        for (const auto& point_node : point_nodes) {
            auto point_result = ParsePointConfigLine(point_node->text);
            if (point_result.IsSuccess()) {
                config.ys_configs.push_back(point_result.data);
            }
        }
    }

    return Result<DevicePointConfig>(config);
}

Result<ModbusPointConfig> XmlConfigParser::ParsePointConfigLine(const std::string& config_line) {
    // 格式: 功能码,起始地址,数量,装置内序号起始,类型,参数
    // 示例: 03,0000,5,1,0

    std::vector<std::string> parts = utils::StringUtils::Split(config_line, ",");
    if (parts.size() < 5) {
        return Result<ModbusPointConfig>(ErrorCode::CONFIG_ERROR,
            "配置行格式错误，至少需要5个参数: " + config_line);
    }

    ModbusPointConfig config;

    try {
        // 解析功能码
        config.function_code = std::stoi(utils::StringUtils::Trim(parts[0]));

        // 解析起始地址 (16进制)
        std::string addr_str = utils::StringUtils::Trim(parts[1]);
        config.start_addr = std::stoi(addr_str, nullptr, 16);

        // 解析数量
        config.count = std::stoi(utils::StringUtils::Trim(parts[2]));

        // 解析Redis起始序号
        config.redis_start_index = std::stoi(utils::StringUtils::Trim(parts[3]));

        // 解析数据类型
        config.data_type = std::stoi(utils::StringUtils::Trim(parts[4]));

        // 解析参数 (可选)
        if (parts.size() > 5) {
            config.param = std::stoi(utils::StringUtils::Trim(parts[5]));
        }

    } catch (const std::exception& e) {
        return Result<ModbusPointConfig>(ErrorCode::CONFIG_ERROR,
            "解析配置参数失败: " + std::string(e.what()) + " - " + config_line);
    }

    return Result<ModbusPointConfig>(config);
}

} // namespace modbus
