#include "../comm/modbus_rtu_comm.h"
#include "../utils/logger.h"
#include <iostream>
#include <iomanip>
#include <thread>
#include <chrono>

using namespace modbus;

void PrintCommStats(ModbusCommInterface& comm) {
    int success_count, error_count, timeout_count;
    comm.GetCommStats(success_count, error_count, timeout_count);
    
    std::cout << "Communication Statistics:" << std::endl;
    std::cout << "  Success: " << success_count << std::endl;
    std::cout << "  Errors: " << error_count << std::endl;
    std::cout << "  Timeouts: " << timeout_count << std::endl;
    
    if (success_count + error_count + timeout_count > 0) {
        double success_rate = static_cast<double>(success_count) / (success_count + error_count + timeout_count) * 100.0;
        std::cout << "  Success Rate: " << std::fixed << std::setprecision(1) << success_rate << "%" << std::endl;
    }
    
    std::string last_error = comm.GetLastError();
    if (!last_error.empty()) {
        std::cout << "  Last Error: " << last_error << std::endl;
    }
    
    std::cout << std::endl;
}

void TestReadOperations(ModbusCommInterface& comm) {
    std::cout << "=== Testing Read Operations ===" << std::endl;
    
    // 设置从站地址
    auto slave_result = comm.SetSlaveId(1);
    if (!slave_result.IsSuccess()) {
        std::cout << "Failed to set slave ID: " << slave_result.error_message << std::endl;
        return;
    }
    
    // 测试读取保持寄存器
    std::cout << "Reading holding registers (address 0, count 10)..." << std::endl;
    auto holding_result = comm.ReadHoldingRegisters(0, 10);
    if (holding_result.IsSuccess()) {
        std::cout << "Success! Values: ";
        for (size_t i = 0; i < holding_result.data.size(); ++i) {
            std::cout << holding_result.data[i];
            if (i < holding_result.data.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;
    } else {
        std::cout << "Failed: " << holding_result.error_message << std::endl;
    }
    
    // 测试读取输入寄存器
    std::cout << "Reading input registers (address 0, count 5)..." << std::endl;
    auto input_result = comm.ReadInputRegisters(0, 5);
    if (input_result.IsSuccess()) {
        std::cout << "Success! Values: ";
        for (size_t i = 0; i < input_result.data.size(); ++i) {
            std::cout << input_result.data[i];
            if (i < input_result.data.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;
    } else {
        std::cout << "Failed: " << input_result.error_message << std::endl;
    }
    
    // 测试读取线圈
    std::cout << "Reading coils (address 0, count 16)..." << std::endl;
    auto coils_result = comm.ReadCoils(0, 16);
    if (coils_result.IsSuccess()) {
        std::cout << "Success! Values: ";
        for (size_t i = 0; i < coils_result.data.size(); ++i) {
            std::cout << (coils_result.data[i] ? "1" : "0");
            if (i < coils_result.data.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;
    } else {
        std::cout << "Failed: " << coils_result.error_message << std::endl;
    }
    
    // 测试读取离散输入
    std::cout << "Reading discrete inputs (address 0, count 8)..." << std::endl;
    auto discrete_result = comm.ReadDiscreteInputs(0, 8);
    if (discrete_result.IsSuccess()) {
        std::cout << "Success! Values: ";
        for (size_t i = 0; i < discrete_result.data.size(); ++i) {
            std::cout << (discrete_result.data[i] ? "1" : "0");
            if (i < discrete_result.data.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;
    } else {
        std::cout << "Failed: " << discrete_result.error_message << std::endl;
    }
    
    std::cout << std::endl;
}

void TestWriteOperations(ModbusCommInterface& comm) {
    std::cout << "=== Testing Write Operations ===" << std::endl;
    
    // 测试写单个寄存器
    std::cout << "Writing single register (address 100, value 1234)..." << std::endl;
    auto write_reg_result = comm.WriteSingleRegister(100, 1234);
    if (write_reg_result.IsSuccess()) {
        std::cout << "Success!" << std::endl;
        
        // 读回验证
        auto read_back = comm.ReadHoldingRegisters(100, 1);
        if (read_back.IsSuccess() && !read_back.data.empty()) {
            std::cout << "Read back value: " << read_back.data[0] << std::endl;
        }
    } else {
        std::cout << "Failed: " << write_reg_result.error_message << std::endl;
    }
    
    // 测试写单个线圈
    std::cout << "Writing single coil (address 100, value true)..." << std::endl;
    auto write_coil_result = comm.WriteSingleCoil(100, true);
    if (write_coil_result.IsSuccess()) {
        std::cout << "Success!" << std::endl;
        
        // 读回验证
        auto read_back = comm.ReadCoils(100, 1);
        if (read_back.IsSuccess() && !read_back.data.empty()) {
            std::cout << "Read back value: " << (read_back.data[0] ? "true" : "false") << std::endl;
        }
    } else {
        std::cout << "Failed: " << write_coil_result.error_message << std::endl;
    }
    
    // 测试写多个寄存器
    std::cout << "Writing multiple registers (address 200, values [100, 200, 300])..." << std::endl;
    std::vector<uint16_t> reg_values = {100, 200, 300};
    auto write_regs_result = comm.WriteMultipleRegisters(200, reg_values);
    if (write_regs_result.IsSuccess()) {
        std::cout << "Success!" << std::endl;
        
        // 读回验证
        auto read_back = comm.ReadHoldingRegisters(200, 3);
        if (read_back.IsSuccess()) {
            std::cout << "Read back values: ";
            for (size_t i = 0; i < read_back.data.size(); ++i) {
                std::cout << read_back.data[i];
                if (i < read_back.data.size() - 1) std::cout << ", ";
            }
            std::cout << std::endl;
        }
    } else {
        std::cout << "Failed: " << write_regs_result.error_message << std::endl;
    }
    
    // 测试写多个线圈
    std::cout << "Writing multiple coils (address 200, values [true, false, true, false])..." << std::endl;
    std::vector<bool> coil_values = {true, false, true, false};
    auto write_coils_result = comm.WriteMultipleCoils(200, coil_values);
    if (write_coils_result.IsSuccess()) {
        std::cout << "Success!" << std::endl;
        
        // 读回验证
        auto read_back = comm.ReadCoils(200, 4);
        if (read_back.IsSuccess()) {
            std::cout << "Read back values: ";
            for (size_t i = 0; i < read_back.data.size(); ++i) {
                std::cout << (read_back.data[i] ? "true" : "false");
                if (i < read_back.data.size() - 1) std::cout << ", ";
            }
            std::cout << std::endl;
        }
    } else {
        std::cout << "Failed: " << write_coils_result.error_message << std::endl;
    }
    
    std::cout << std::endl;
}

int main() {
    std::cout << "Modbus RTU Communication Basic Example" << std::endl;
    std::cout << "=======================================" << std::endl;
    
    // 初始化日志系统
    Logger::GetInstance().Initialize(LogLevel::INFO, LogTarget::CONSOLE);
    
    // 配置 RTU 参数
    RtuCommParam rtu_param;
    rtu_param.device = "/dev/ttyUSB0";  // 根据实际情况修改
    rtu_param.baud = 9600;
    rtu_param.parity = 'N';
    rtu_param.data_bit = 8;
    rtu_param.stop_bit = 1;
    rtu_param.timeout_ms = 1000;
    
    std::cout << "RTU Configuration:" << std::endl;
    std::cout << "  Device: " << rtu_param.device << std::endl;
    std::cout << "  Baud: " << rtu_param.baud << std::endl;
    std::cout << "  Parity: " << rtu_param.parity << std::endl;
    std::cout << "  Data Bits: " << rtu_param.data_bit << std::endl;
    std::cout << "  Stop Bits: " << rtu_param.stop_bit << std::endl;
    std::cout << "  Timeout: " << rtu_param.timeout_ms << " ms" << std::endl;
    std::cout << std::endl;
    
    // 创建 RTU 通信实例
    ModbusRtuComm rtu_comm(rtu_param, 1);
    
    // 设置连接状态回调
    rtu_comm.SetConnectionStatusCallback([](int device_id, DeviceStatus status) {
        const char* status_str = "";
        switch (status) {
            case DeviceStatus::DISCONNECTED: status_str = "DISCONNECTED"; break;
            case DeviceStatus::CONNECTING: status_str = "CONNECTING"; break;
            case DeviceStatus::CONNECTED: status_str = "CONNECTED"; break;
            case DeviceStatus::ERROR: status_str = "ERROR"; break;
        }
        std::cout << "Device " << device_id << " status changed to: " << status_str << std::endl;
    });
    
    // 初始化通信
    std::cout << "Initializing RTU communication..." << std::endl;
    auto init_result = rtu_comm.Initialize();
    if (!init_result.IsSuccess()) {
        std::cerr << "Failed to initialize RTU communication: " << init_result.error_message << std::endl;
        return 1;
    }
    
    // 连接设备
    std::cout << "Connecting to device..." << std::endl;
    auto connect_result = rtu_comm.Connect();
    if (!connect_result.IsSuccess()) {
        std::cerr << "Failed to connect: " << connect_result.error_message << std::endl;
        std::cout << "Note: This is expected if no actual Modbus device is connected." << std::endl;
        std::cout << "The example will continue to demonstrate the API usage." << std::endl;
    } else {
        std::cout << "Connected successfully!" << std::endl;
    }
    
    std::cout << std::endl;
    
    // 测试读取操作
    TestReadOperations(rtu_comm);
    
    // 测试写入操作
    TestWriteOperations(rtu_comm);
    
    // 打印通信统计
    PrintCommStats(rtu_comm);
    
    // 测试重连
    std::cout << "=== Testing Reconnection ===" << std::endl;
    auto reconnect_result = rtu_comm.Reconnect();
    if (reconnect_result.IsSuccess()) {
        std::cout << "Reconnection successful!" << std::endl;
    } else {
        std::cout << "Reconnection failed: " << reconnect_result.error_message << std::endl;
    }
    
    // 关闭连接
    std::cout << "Disconnecting..." << std::endl;
    rtu_comm.Disconnect();
    
    std::cout << "RTU communication example completed!" << std::endl;
    
    Logger::GetInstance().Shutdown();
    return 0;
}
