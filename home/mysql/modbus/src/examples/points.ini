# Modbus 数据点配置文件
# 兼容现有 xj_svg 系统的点表格式
# 格式: 数据类型:点号 = 功能码,从站地址,起始地址,数量[,比例因子,偏移量,描述]

[PointList]
# 遥测数据 (YC - 模拟量)
# 数据类型 1 = 遥测
1:00001 = 3,1,0,10,1.0,0.0,电压测量值
1:00002 = 3,1,10,10,0.1,0.0,电流测量值  
1:00003 = 3,1,20,5,0.01,0.0,功率测量值
1:00004 = 3,2,0,15,1.0,0.0,温度测量值
1:00005 = 3,2,15,8,0.1,0.0,压力测量值

# 遥信数据 (YX - 开关量)  
# 数据类型 2 = 遥信
2:00001 = 1,1,0,16,1.0,0.0,开关状态1
2:00002 = 1,1,16,16,1.0,0.0,开关状态2
2:00003 = 1,2,0,32,1.0,0.0,报警状态
2:00004 = 3,3,0,10,1.0,0.0,设备状态寄存器

# 遥调数据 (YT - 模拟量设定)
# 数据类型 3 = 遥调  
3:00001 = 6,1,100,1,1.0,0.0,电压设定值
3:00002 = 6,1,101,1,0.1,0.0,电流设定值
3:00003 = 6,2,200,1,0.01,0.0,功率设定值
3:00004 = 16,1,102,5,1.0,0.0,参数设定组1
3:00005 = 16,2,201,3,0.1,0.0,参数设定组2

# 遥控数据 (YK - 开关量控制)
# 数据类型 4 = 遥控
4:00001 = 5,1,1000,1,1.0,0.0,主开关控制
4:00002 = 5,1,1001,1,1.0,0.0,备用开关控制  
4:00003 = 5,2,2000,1,1.0,0.0,报警复位
4:00004 = 15,1,1002,8,1.0,0.0,批量开关控制
4:00005 = 5,3,3000,1,1.0,0.0,设备启停控制

# 扩展配置
[PointConfig]
# 数据采集周期 (毫秒)
YC_ScanInterval = 1000
YX_ScanInterval = 500
YT_ScanInterval = 2000
YK_ScanInterval = 100

# 数据有效性检查
EnableDataValidation = true
MinValidValue = -999999
MaxValidValue = 999999

# 数据变化检测
EnableChangeDetection = true
ChangeThreshold = 0.01

# 数据滤波
EnableDataFilter = true
FilterType = AVERAGE    # AVERAGE, MEDIAN, LOW_PASS
FilterWindow = 5

# 报警配置
EnableAlarm = true
AlarmDeadband = 0.05

[AlarmLimits]
# 遥测点报警限值配置
# 格式: 点号 = 下下限,下限,上限,上上限
1:00001 = 200,210,240,250    # 电压报警限值
1:00002 = 0,5,95,100         # 电流报警限值
1:00004 = -10,0,80,90        # 温度报警限值

[ScaleConfig]
# 工程量转换配置
# 格式: 点号 = 原始最小值,原始最大值,工程最小值,工程最大值
1:00001 = 0,65535,0,300      # 电压量程转换
1:00002 = 0,65535,0,100      # 电流量程转换
1:00004 = 0,65535,-20,100    # 温度量程转换

[DeviceMapping]
# 设备地址映射
# 格式: 逻辑地址 = 物理从站地址
Device1 = 1
Device2 = 2  
Device3 = 3
Backup1 = 4
Backup2 = 5
