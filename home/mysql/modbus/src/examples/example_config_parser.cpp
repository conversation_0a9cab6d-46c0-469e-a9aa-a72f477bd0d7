#include "../config/config_manager.h"
#include "../config/point_table_loader.h"
#include "../utils/logger.h"
#include <iostream>

using namespace modbus;

void PrintServiceParam(const ServiceParam& param) {
    std::cout << "=== Service Configuration ===" << std::endl;
    std::cout << "Redis IP: " << param.redis_ip << std::endl;
    std::cout << "Redis Port: " << param.redis_port << std::endl;
    std::cout << "Report Interval: " << param.report_interval_ms << " ms" << std::endl;
    std::cout << "Device Count: " << param.devices.size() << std::endl;
    std::cout << std::endl;
}

void PrintDeviceParam(const DeviceParam& param) {
    std::cout << "=== Device " << param.device_id << " Configuration ===" << std::endl;
    std::cout << "Device Name: " << param.device_name << std::endl;
    std::cout << "Point File: " << param.point_file << std::endl;
    std::cout << "Communication Type: " << (param.comm_type == CommType::RTU ? "RTU" : "TCP") << std::endl;
    
    if (param.comm_type == CommType::RTU) {
        std::cout << "RTU Device: " << param.rtu_param.device << std::endl;
        std::cout << "RTU Baud: " << param.rtu_param.baud << std::endl;
        std::cout << "RTU Parity: " << param.rtu_param.parity << std::endl;
        std::cout << "RTU Data Bits: " << param.rtu_param.data_bit << std::endl;
        std::cout << "RTU Stop Bits: " << param.rtu_param.stop_bit << std::endl;
        std::cout << "RTU Timeout: " << param.rtu_param.timeout_ms << " ms" << std::endl;
    } else {
        std::cout << "TCP IP: " << param.tcp_param.ip << std::endl;
        std::cout << "TCP Port: " << param.tcp_param.port << std::endl;
        std::cout << "TCP Timeout: " << param.tcp_param.timeout_ms << " ms" << std::endl;
    }
    std::cout << std::endl;
}

void PrintDataPoint(const DataPoint& point) {
    std::cout << "Point " << static_cast<int>(point.type_idx.data_type) 
              << ":" << point.type_idx.point_id;
    std::cout << " - Func:" << static_cast<int>(point.func_code);
    std::cout << " Slave:" << point.slave_id;
    std::cout << " Addr:" << point.start_addr;
    std::cout << " Count:" << point.count;
    std::cout << " Scale:" << point.scale;
    std::cout << " Offset:" << point.offset;
    if (!point.description.empty()) {
        std::cout << " Desc:" << point.description;
    }
    std::cout << std::endl;
}

void PrintPointTableStats(const PointTableLoader::PointTableStats& stats) {
    std::cout << "=== Point Table Statistics ===" << std::endl;
    std::cout << "Total Points: " << stats.total_points << std::endl;
    std::cout << "YC Points: " << stats.yc_points << std::endl;
    std::cout << "YX Points: " << stats.yx_points << std::endl;
    std::cout << "YT Points: " << stats.yt_points << std::endl;
    std::cout << "YK Points: " << stats.yk_points << std::endl;
    
    std::cout << "Points per Slave:" << std::endl;
    for (const auto& pair : stats.slave_point_count) {
        std::cout << "  Slave " << pair.first << ": " << pair.second << " points" << std::endl;
    }
    std::cout << std::endl;
}

int main() {
    std::cout << "Modbus Configuration Parser Example" << std::endl;
    std::cout << "====================================" << std::endl;
    
    // 初始化日志系统
    Logger::GetInstance().Initialize(LogLevel::INFO, LogTarget::CONSOLE);
    
    // 测试配置管理器
    ConfigManager config_manager;
    
    // 加载配置文件
    std::cout << "Loading configuration file..." << std::endl;
    auto load_result = config_manager.LoadConfig("src/examples/modbus_config.ini");
    if (!load_result.IsSuccess()) {
        std::cerr << "Failed to load config: " << load_result.error_message << std::endl;
        return 1;
    }
    
    // 获取服务参数
    auto service_result = config_manager.GetServiceParam();
    if (service_result.IsSuccess()) {
        PrintServiceParam(service_result.data);
        
        // 打印所有设备配置
        for (const auto& device_pair : service_result.data.devices) {
            PrintDeviceParam(device_pair.second);
        }
    } else {
        std::cerr << "Failed to get service param: " << service_result.error_message << std::endl;
    }
    
    // 测试点表加载器
    std::cout << "Loading point table..." << std::endl;
    PointTableLoader point_loader;
    
    auto points_result = point_loader.LoadPointTable("src/examples/points.ini");
    if (points_result.IsSuccess()) {
        const auto& points = points_result.data;
        
        std::cout << "=== Point Table ===" << std::endl;
        std::cout << "Loaded " << points.size() << " points:" << std::endl;
        
        // 打印前10个点的详细信息
        int count = 0;
        for (const auto& point : points) {
            if (count++ >= 10) {
                std::cout << "... (showing first 10 points)" << std::endl;
                break;
            }
            PrintDataPoint(point);
        }
        std::cout << std::endl;
        
        // 打印统计信息
        auto stats = point_loader.GetPointTableStats(points);
        PrintPointTableStats(stats);
        
        // 测试按数据类型过滤
        std::cout << "=== Filtering by Data Type ===" << std::endl;
        auto yc_points = point_loader.FilterByDataType(points, DataType::YC);
        std::cout << "YC Points: " << yc_points.size() << std::endl;
        
        auto yx_points = point_loader.FilterByDataType(points, DataType::YX);
        std::cout << "YX Points: " << yx_points.size() << std::endl;
        
        auto yt_points = point_loader.FilterByDataType(points, DataType::YT);
        std::cout << "YT Points: " << yt_points.size() << std::endl;
        
        auto yk_points = point_loader.FilterByDataType(points, DataType::YK);
        std::cout << "YK Points: " << yk_points.size() << std::endl;
        std::cout << std::endl;
        
        // 测试按从站地址过滤
        std::cout << "=== Filtering by Slave ID ===" << std::endl;
        for (int slave_id = 1; slave_id <= 3; ++slave_id) {
            auto slave_points = point_loader.FilterBySlaveId(points, slave_id);
            std::cout << "Slave " << slave_id << " Points: " << slave_points.size() << std::endl;
        }
        std::cout << std::endl;
        
    } else {
        std::cerr << "Failed to load point table: " << points_result.error_message << std::endl;
    }
    
    // 测试配置验证
    std::cout << "=== Configuration Validation ===" << std::endl;
    auto validate_result = config_manager.ValidateConfig();
    if (validate_result.IsSuccess()) {
        std::cout << "Configuration validation: PASSED" << std::endl;
    } else {
        std::cout << "Configuration validation: FAILED - " << validate_result.error_message << std::endl;
    }
    
    // 测试获取特定设备配置
    std::cout << "=== Device-Specific Configuration ===" << std::endl;
    for (int device_id = 1; device_id <= 3; ++device_id) {
        auto device_result = config_manager.GetDeviceParam(device_id);
        if (device_result.IsSuccess()) {
            std::cout << "Found configuration for device " << device_id << std::endl;
        } else {
            std::cout << "Device " << device_id << " not found: " << device_result.error_message << std::endl;
        }
    }
    
    std::cout << std::endl << "Configuration parser example completed successfully!" << std::endl;
    
    Logger::GetInstance().Shutdown();
    return 0;
}
