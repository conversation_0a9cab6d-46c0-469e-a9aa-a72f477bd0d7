#include "../comm/modbus_tcp_comm.h"
#include "../utils/logger.h"
#include <iostream>
#include <iomanip>
#include <thread>
#include <chrono>

using namespace modbus;

void PrintCommStats(ModbusCommInterface& comm) {
    int success_count, error_count, timeout_count;
    comm.GetCommStats(success_count, error_count, timeout_count);
    
    std::cout << "Communication Statistics:" << std::endl;
    std::cout << "  Success: " << success_count << std::endl;
    std::cout << "  Errors: " << error_count << std::endl;
    std::cout << "  Timeouts: " << timeout_count << std::endl;
    
    if (success_count + error_count + timeout_count > 0) {
        double success_rate = static_cast<double>(success_count) / (success_count + error_count + timeout_count) * 100.0;
        std::cout << "  Success Rate: " << std::fixed << std::setprecision(1) << success_rate << "%" << std::endl;
    }
    
    std::string last_error = comm.GetLastError();
    if (!last_error.empty()) {
        std::cout << "  Last Error: " << last_error << std::endl;
    }
    
    std::cout << std::endl;
}

void TestReadOperations(ModbusCommInterface& comm) {
    std::cout << "=== Testing Read Operations ===" << std::endl;
    
    // 设置从站地址
    auto slave_result = comm.SetSlaveId(1);
    if (!slave_result.IsSuccess()) {
        std::cout << "Failed to set slave ID: " << slave_result.error_message << std::endl;
        return;
    }
    
    // 测试读取保持寄存器
    std::cout << "Reading holding registers (address 0, count 10)..." << std::endl;
    auto holding_result = comm.ReadHoldingRegisters(0, 10);
    if (holding_result.IsSuccess()) {
        std::cout << "Success! Values: ";
        for (size_t i = 0; i < holding_result.data.size(); ++i) {
            std::cout << holding_result.data[i];
            if (i < holding_result.data.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;
    } else {
        std::cout << "Failed: " << holding_result.error_message << std::endl;
    }
    
    // 测试读取输入寄存器
    std::cout << "Reading input registers (address 0, count 5)..." << std::endl;
    auto input_result = comm.ReadInputRegisters(0, 5);
    if (input_result.IsSuccess()) {
        std::cout << "Success! Values: ";
        for (size_t i = 0; i < input_result.data.size(); ++i) {
            std::cout << input_result.data[i];
            if (i < input_result.data.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;
    } else {
        std::cout << "Failed: " << input_result.error_message << std::endl;
    }
    
    // 测试读取线圈
    std::cout << "Reading coils (address 0, count 16)..." << std::endl;
    auto coils_result = comm.ReadCoils(0, 16);
    if (coils_result.IsSuccess()) {
        std::cout << "Success! Values: ";
        for (size_t i = 0; i < coils_result.data.size(); ++i) {
            std::cout << (coils_result.data[i] ? "1" : "0");
            if (i < coils_result.data.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;
    } else {
        std::cout << "Failed: " << coils_result.error_message << std::endl;
    }
    
    // 测试读取离散输入
    std::cout << "Reading discrete inputs (address 0, count 8)..." << std::endl;
    auto discrete_result = comm.ReadDiscreteInputs(0, 8);
    if (discrete_result.IsSuccess()) {
        std::cout << "Success! Values: ";
        for (size_t i = 0; i < discrete_result.data.size(); ++i) {
            std::cout << (discrete_result.data[i] ? "1" : "0");
            if (i < discrete_result.data.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;
    } else {
        std::cout << "Failed: " << discrete_result.error_message << std::endl;
    }
    
    std::cout << std::endl;
}

void TestWriteOperations(ModbusCommInterface& comm) {
    std::cout << "=== Testing Write Operations ===" << std::endl;
    
    // 测试写单个寄存器
    std::cout << "Writing single register (address 100, value 1234)..." << std::endl;
    auto write_reg_result = comm.WriteSingleRegister(100, 1234);
    if (write_reg_result.IsSuccess()) {
        std::cout << "Success!" << std::endl;
        
        // 读回验证
        auto read_back = comm.ReadHoldingRegisters(100, 1);
        if (read_back.IsSuccess() && !read_back.data.empty()) {
            std::cout << "Read back value: " << read_back.data[0] << std::endl;
        }
    } else {
        std::cout << "Failed: " << write_reg_result.error_message << std::endl;
    }
    
    // 测试写单个线圈
    std::cout << "Writing single coil (address 100, value true)..." << std::endl;
    auto write_coil_result = comm.WriteSingleCoil(100, true);
    if (write_coil_result.IsSuccess()) {
        std::cout << "Success!" << std::endl;
        
        // 读回验证
        auto read_back = comm.ReadCoils(100, 1);
        if (read_back.IsSuccess() && !read_back.data.empty()) {
            std::cout << "Read back value: " << (read_back.data[0] ? "true" : "false") << std::endl;
        }
    } else {
        std::cout << "Failed: " << write_coil_result.error_message << std::endl;
    }
    
    // 测试写多个寄存器
    std::cout << "Writing multiple registers (address 200, values [100, 200, 300])..." << std::endl;
    std::vector<uint16_t> reg_values = {100, 200, 300};
    auto write_regs_result = comm.WriteMultipleRegisters(200, reg_values);
    if (write_regs_result.IsSuccess()) {
        std::cout << "Success!" << std::endl;
        
        // 读回验证
        auto read_back = comm.ReadHoldingRegisters(200, 3);
        if (read_back.IsSuccess()) {
            std::cout << "Read back values: ";
            for (size_t i = 0; i < read_back.data.size(); ++i) {
                std::cout << read_back.data[i];
                if (i < read_back.data.size() - 1) std::cout << ", ";
            }
            std::cout << std::endl;
        }
    } else {
        std::cout << "Failed: " << write_regs_result.error_message << std::endl;
    }
    
    // 测试写多个线圈
    std::cout << "Writing multiple coils (address 200, values [true, false, true, false])..." << std::endl;
    std::vector<bool> coil_values = {true, false, true, false};
    auto write_coils_result = comm.WriteMultipleCoils(200, coil_values);
    if (write_coils_result.IsSuccess()) {
        std::cout << "Success!" << std::endl;
        
        // 读回验证
        auto read_back = comm.ReadCoils(200, 4);
        if (read_back.IsSuccess()) {
            std::cout << "Read back values: ";
            for (size_t i = 0; i < read_back.data.size(); ++i) {
                std::cout << (read_back.data[i] ? "true" : "false");
                if (i < read_back.data.size() - 1) std::cout << ", ";
            }
            std::cout << std::endl;
        }
    } else {
        std::cout << "Failed: " << write_coils_result.error_message << std::endl;
    }
    
    std::cout << std::endl;
}

void TestMultipleConnections(ModbusTcpComm& tcp_comm) {
    std::cout << "=== Testing Multiple Connections ===" << std::endl;
    
    // 添加额外连接
    std::cout << "Adding additional connection to *************:502..." << std::endl;
    auto add_result = tcp_comm.AddConnection("*************", 502);
    if (add_result.IsSuccess()) {
        std::cout << "Connection added successfully!" << std::endl;
    } else {
        std::cout << "Failed to add connection: " << add_result.error_message << std::endl;
    }
    
    // 添加另一个连接
    std::cout << "Adding additional connection to *************:502..." << std::endl;
    add_result = tcp_comm.AddConnection("*************", 502);
    if (add_result.IsSuccess()) {
        std::cout << "Connection added successfully!" << std::endl;
    } else {
        std::cout << "Failed to add connection: " << add_result.error_message << std::endl;
    }
    
    // 获取活跃连接
    auto active_connections = tcp_comm.GetActiveConnections();
    std::cout << "Active connections: " << active_connections.size() << std::endl;
    for (const auto& conn : active_connections) {
        std::cout << "  " << conn << std::endl;
    }
    
    // 切换连接
    std::cout << "Switching to connection *************:502..." << std::endl;
    auto switch_result = tcp_comm.SwitchConnection("*************", 502);
    if (switch_result.IsSuccess()) {
        std::cout << "Connection switched successfully!" << std::endl;
    } else {
        std::cout << "Failed to switch connection: " << switch_result.error_message << std::endl;
    }
    
    // 移除连接
    std::cout << "Removing connection *************:502..." << std::endl;
    auto remove_result = tcp_comm.RemoveConnection("*************", 502);
    if (remove_result.IsSuccess()) {
        std::cout << "Connection removed successfully!" << std::endl;
    } else {
        std::cout << "Failed to remove connection: " << remove_result.error_message << std::endl;
    }
    
    std::cout << std::endl;
}

int main() {
    std::cout << "Modbus TCP Communication Basic Example" << std::endl;
    std::cout << "=======================================" << std::endl;
    
    // 初始化日志系统
    Logger::GetInstance().Initialize(LogLevel::INFO, LogTarget::CONSOLE);
    
    // 配置 TCP 参数
    TcpCommParam tcp_param;
    tcp_param.ip = "127.0.0.1";  // 根据实际情况修改
    tcp_param.port = 502;
    tcp_param.timeout_ms = 1000;
    
    std::cout << "TCP Configuration:" << std::endl;
    std::cout << "  IP: " << tcp_param.ip << std::endl;
    std::cout << "  Port: " << tcp_param.port << std::endl;
    std::cout << "  Timeout: " << tcp_param.timeout_ms << " ms" << std::endl;
    std::cout << std::endl;
    
    // 创建 TCP 通信实例
    ModbusTcpComm tcp_comm(tcp_param, 1);
    
    // 设置连接状态回调
    tcp_comm.SetConnectionStatusCallback([](int device_id, DeviceStatus status) {
        const char* status_str = "";
        switch (status) {
            case DeviceStatus::DISCONNECTED: status_str = "DISCONNECTED"; break;
            case DeviceStatus::CONNECTING: status_str = "CONNECTING"; break;
            case DeviceStatus::CONNECTED: status_str = "CONNECTED"; break;
            case DeviceStatus::ERROR: status_str = "ERROR"; break;
        }
        std::cout << "Device " << device_id << " status changed to: " << status_str << std::endl;
    });
    
    // 初始化通信
    std::cout << "Initializing TCP communication..." << std::endl;
    auto init_result = tcp_comm.Initialize();
    if (!init_result.IsSuccess()) {
        std::cerr << "Failed to initialize TCP communication: " << init_result.error_message << std::endl;
        return 1;
    }
    
    // 连接设备
    std::cout << "Connecting to device..." << std::endl;
    auto connect_result = tcp_comm.Connect();
    if (!connect_result.IsSuccess()) {
        std::cerr << "Failed to connect: " << connect_result.error_message << std::endl;
        std::cout << "Note: This is expected if no actual Modbus device is connected." << std::endl;
        std::cout << "The example will continue to demonstrate the API usage." << std::endl;
    } else {
        std::cout << "Connected successfully!" << std::endl;
    }
    
    std::cout << std::endl;
    
    // 测试读取操作
    TestReadOperations(tcp_comm);
    
    // 测试写入操作
    TestWriteOperations(tcp_comm);
    
    // 测试多连接功能
    TestMultipleConnections(tcp_comm);
    
    // 打印通信统计
    PrintCommStats(tcp_comm);
    
    // 测试重连
    std::cout << "=== Testing Reconnection ===" << std::endl;
    auto reconnect_result = tcp_comm.Reconnect();
    if (reconnect_result.IsSuccess()) {
        std::cout << "Reconnection successful!" << std::endl;
    } else {
        std::cout << "Reconnection failed: " << reconnect_result.error_message << std::endl;
    }
    
    // 关闭连接
    std::cout << "Disconnecting..." << std::endl;
    tcp_comm.Disconnect();
    
    std::cout << "TCP communication example completed!" << std::endl;
    
    Logger::GetInstance().Shutdown();
    return 0;
}
