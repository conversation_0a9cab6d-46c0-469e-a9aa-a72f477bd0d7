#include "../redis/redis_manager.h"
#include "../utils/logger.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace modbus;

void PrintRedisStats(const RedisManager& manager) {
    auto stats = manager.GetStatistics();
    
    std::cout << "=== Redis Statistics ===" << std::endl;
    std::cout << "Publisher:" << std::endl;
    std::cout << "  Connected: " << (stats.publisher_connected ? "Yes" : "No") << std::endl;
    std::cout << "  Success: " << stats.publish_success_count << std::endl;
    std::cout << "  Errors: " << stats.publish_error_count << std::endl;
    std::cout << "  Pending: " << stats.publish_pending_count << std::endl;
    
    std::cout << "Subscriber:" << std::endl;
    std::cout << "  Connected: " << (stats.subscriber_connected ? "Yes" : "No") << std::endl;
    std::cout << "  Messages: " << stats.message_count << std::endl;
    std::cout << "  Errors: " << stats.subscribe_error_count << std::endl;
    std::cout << "  Channels: " << stats.subscribed_channel_count << std::endl;
    
    std::cout << "Commands:" << std::endl;
    std::cout << "  Processed: " << stats.command_processed_count << std::endl;
    std::cout << "  Success: " << stats.command_success_count << std::endl;
    std::cout << "  Errors: " << stats.command_error_count << std::endl;
    
    std::cout << std::endl;
}

void TestBasicPublishing(RedisManager& manager) {
    std::cout << "=== Testing Basic Publishing ===" << std::endl;
    
    int device_id = 1;
    
    // 发布遥测数据
    TypeIndex yc_point;
    yc_point.data_type = DataType::YC;
    yc_point.point_id = 1;
    
    auto result1 = manager.PublishData(device_id, yc_point, 123.45);
    std::cout << "Publish YC data: " << (result1.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    if (!result1.IsSuccess()) {
        std::cout << "  Error: " << result1.error_message << std::endl;
    }
    
    // 发布遥信数据
    TypeIndex yx_point;
    yx_point.data_type = DataType::YX;
    yx_point.point_id = 1;
    
    auto result2 = manager.PublishData(device_id, yx_point, 1.0);
    std::cout << "Publish YX data: " << (result2.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    if (!result2.IsSuccess()) {
        std::cout << "  Error: " << result2.error_message << std::endl;
    }
    
    // 发布设备状态
    auto result3 = manager.PublishDeviceStatus(device_id, DeviceStatus::CONNECTED, "Device online");
    std::cout << "Publish device status: " << (result3.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    if (!result3.IsSuccess()) {
        std::cout << "  Error: " << result3.error_message << std::endl;
    }
    
    // 发布报警信息
    auto result4 = manager.PublishAlarm(device_id, yc_point, "HIGH_ALARM", 150.0, "Temperature too high");
    std::cout << "Publish alarm: " << (result4.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    if (!result4.IsSuccess()) {
        std::cout << "  Error: " << result4.error_message << std::endl;
    }
    
    std::cout << std::endl;
}

void TestBatchPublishing(RedisManager& manager) {
    std::cout << "=== Testing Batch Publishing ===" << std::endl;
    
    int device_id = 2;
    
    // 准备批量数据
    std::map<TypeIndex, double> batch_data;
    
    for (int i = 1; i <= 5; ++i) {
        TypeIndex point;
        point.data_type = DataType::YC;
        point.point_id = i;
        batch_data[point] = 100.0 + i * 10.0;
    }
    
    for (int i = 1; i <= 3; ++i) {
        TypeIndex point;
        point.data_type = DataType::YX;
        point.point_id = i;
        batch_data[point] = (i % 2 == 0) ? 1.0 : 0.0;
    }
    
    auto result = manager.PublishDataBatch(device_id, batch_data);
    std::cout << "Publish batch data (" << batch_data.size() << " points): " 
              << (result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    if (!result.IsSuccess()) {
        std::cout << "  Error: " << result.error_message << std::endl;
    }
    
    std::cout << std::endl;
}

void TestControlSubscription(RedisManager& manager) {
    std::cout << "=== Testing Control Subscription ===" << std::endl;
    
    // 设置控制指令处理器
    manager.SetControlCommandHandler([](int device_id, const ControlCommand& command) {
        std::cout << "Received control command for device " << device_id << ":" << std::endl;
        std::cout << "  Type: " << static_cast<int>(command.type_idx.data_type) << std::endl;
        std::cout << "  Point: " << command.type_idx.point_id << std::endl;
        std::cout << "  Value: " << command.value << std::endl;
        std::cout << "  Command ID: " << command.command_id << std::endl;
        std::cout << "  Timestamp: " << command.timestamp << std::endl;
        
        // 模拟处理控制指令
        return Result<bool>(true);
    });
    
    // 订阅控制通道
    for (int device_id = 1; device_id <= 3; ++device_id) {
        auto result = manager.SubscribeControlChannel(device_id);
        std::cout << "Subscribe control channel for device " << device_id << ": " 
                  << (result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
        if (!result.IsSuccess()) {
            std::cout << "  Error: " << result.error_message << std::endl;
        }
    }
    
    // 也可以订阅所有控制通道
    auto result = manager.SubscribeAllControlChannels();
    std::cout << "Subscribe all control channels: " << (result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    if (!result.IsSuccess()) {
        std::cout << "  Error: " << result.error_message << std::endl;
    }
    
    std::cout << std::endl;
}

void TestDeviceRedisManager() {
    std::cout << "=== Testing Device Redis Manager ===" << std::endl;
    
    // 创建全局 Redis 管理器
    RedisManagerConfig config("127.0.0.1", 6379);
    auto redis_manager = std::make_shared<RedisManager>(config);
    
    auto init_result = redis_manager->Initialize();
    if (!init_result.IsSuccess()) {
        std::cout << "Failed to initialize Redis manager: " << init_result.error_message << std::endl;
        return;
    }
    
    auto start_result = redis_manager->Start();
    if (!start_result.IsSuccess()) {
        std::cout << "Failed to start Redis manager: " << start_result.error_message << std::endl;
        return;
    }
    
    // 创建设备 Redis 管理器
    DeviceRedisManager device_manager(100, redis_manager);
    device_manager.SetDeviceName("Test Device 100");
    
    auto device_init_result = device_manager.Initialize();
    std::cout << "Device Redis manager initialization: " 
              << (device_init_result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    
    // 设置控制指令处理器
    device_manager.SetControlHandler([](const ControlCommand& command) {
        std::cout << "Device received control command:" << std::endl;
        std::cout << "  Point: " << static_cast<int>(command.type_idx.data_type) 
                  << ":" << command.type_idx.point_id << std::endl;
        std::cout << "  Value: " << command.value << std::endl;
        return Result<bool>(true);
    });
    
    // 发布一些数据
    TypeIndex test_point;
    test_point.data_type = DataType::YC;
    test_point.point_id = 1;
    
    for (int i = 0; i < 5; ++i) {
        double value = 100.0 + i * 5.0;
        auto result = device_manager.PublishData(test_point, value);
        std::cout << "Device publish data " << i + 1 << ": " 
                  << (result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
        
        utils::TimeUtils::SleepMs(100);
    }
    
    // 发布状态
    auto status_result = device_manager.PublishStatus(DeviceStatus::CONNECTED, "Device is running normally");
    std::cout << "Device publish status: " << (status_result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    
    // 获取统计信息
    auto device_stats = device_manager.GetStatistics();
    std::cout << "Device statistics:" << std::endl;
    std::cout << "  Data published: " << device_stats.data_published_count << std::endl;
    std::cout << "  Commands received: " << device_stats.commands_received_count << std::endl;
    std::cout << "  Commands processed: " << device_stats.commands_processed_count << std::endl;
    
    device_manager.Shutdown();
    redis_manager->Stop();
    
    std::cout << std::endl;
}

void TestDataReporter() {
    std::cout << "=== Testing Data Reporter ===" << std::endl;
    
    RedisConnectionParam param("127.0.0.1", 6379);
    auto publisher = RedisPublisherFactory::Create(param);
    
    auto connect_result = publisher->Connect();
    std::cout << "Publisher connection: " << (connect_result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    
    if (connect_result.IsSuccess()) {
        RedisDataReporter reporter(publisher);
        
        // 测试不同格式的数据上报
        TypeIndex point;
        point.data_type = DataType::YC;
        point.point_id = 1;
        
        // JSON 格式
        reporter.SetReportFormat(RedisDataReporter::ReportFormat::JSON);
        auto result1 = reporter.ReportData(1, point, 123.45);
        std::cout << "Report JSON format: " << (result1.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
        
        // 兼容格式
        reporter.SetReportFormat(RedisDataReporter::ReportFormat::LEGACY);
        auto result2 = reporter.ReportData(1, point, 123.45);
        std::cout << "Report legacy format: " << (result2.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
        
        // 自定义格式
        reporter.SetReportFormat(RedisDataReporter::ReportFormat::CUSTOM);
        reporter.SetCustomFormatter([](int device_id, const TypeIndex& type_idx, double value, uint64_t timestamp) {
            return utils::StringUtils::Format("CUSTOM|%d|%d:%d|%.2f|%llu", 
                                            device_id, static_cast<int>(type_idx.data_type), 
                                            type_idx.point_id, value, timestamp);
        });
        auto result3 = reporter.ReportData(1, point, 123.45);
        std::cout << "Report custom format: " << (result3.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
        
        publisher->Disconnect();
    }
    
    std::cout << std::endl;
}

int main() {
    std::cout << "Redis Publisher Example" << std::endl;
    std::cout << "=======================" << std::endl;
    
    // 初始化日志系统
    Logger::GetInstance().Initialize(LogLevel::INFO, LogTarget::CONSOLE);
    
    // 检查 Redis 支持
    if (!RedisManager::IsRedisSupported()) {
        std::cout << "Redis is not supported (hiredis not available)" << std::endl;
        std::cout << "This example will demonstrate the API usage without actual Redis operations." << std::endl;
    }
    
    // 配置 Redis 连接
    RedisManagerConfig config;
    config.connection_param.host = "127.0.0.1";
    config.connection_param.port = 6379;
    config.enable_publisher = true;
    config.enable_subscriber = true;
    config.enable_auto_reconnect = true;
    
    std::cout << "Redis Configuration:" << std::endl;
    std::cout << "  Host: " << config.connection_param.host << std::endl;
    std::cout << "  Port: " << config.connection_param.port << std::endl;
    std::cout << "  Publisher: " << (config.enable_publisher ? "Enabled" : "Disabled") << std::endl;
    std::cout << "  Subscriber: " << (config.enable_subscriber ? "Enabled" : "Disabled") << std::endl;
    std::cout << std::endl;
    
    // 创建 Redis 管理器
    RedisManager manager(config);
    
    // 设置状态回调
    manager.SetStatusCallback([](const std::string& component, bool connected, const std::string& error) {
        std::cout << "Redis " << component << " status: " 
                  << (connected ? "CONNECTED" : "DISCONNECTED");
        if (!error.empty()) {
            std::cout << " (" << error << ")";
        }
        std::cout << std::endl;
    });
    
    // 初始化和启动
    std::cout << "Initializing Redis manager..." << std::endl;
    auto init_result = manager.Initialize();
    if (!init_result.IsSuccess()) {
        std::cerr << "Failed to initialize Redis manager: " << init_result.error_message << std::endl;
        return 1;
    }
    
    std::cout << "Starting Redis manager..." << std::endl;
    auto start_result = manager.Start();
    if (!start_result.IsSuccess()) {
        std::cerr << "Failed to start Redis manager: " << start_result.error_message << std::endl;
        return 1;
    }
    
    // 等待连接建立
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // 运行测试
    TestBasicPublishing(manager);
    TestBatchPublishing(manager);
    TestControlSubscription(manager);
    
    // 打印统计信息
    PrintRedisStats(manager);
    
    // 运行一段时间以接收可能的控制指令
    std::cout << "Running for 10 seconds to receive control commands..." << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(10));
    
    // 最终统计
    PrintRedisStats(manager);
    
    // 测试其他组件
    TestDeviceRedisManager();
    TestDataReporter();
    
    // 停止管理器
    std::cout << "Stopping Redis manager..." << std::endl;
    manager.Stop();
    
    std::cout << "Redis publisher example completed!" << std::endl;
    
    Logger::GetInstance().Shutdown();
    return 0;
}
