#include "../service/modbus_service.h"
#include "../utils/logger.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace modbus;

void PrintServiceStatus(const ModbusService& service) {
    auto stats = service.GetStatistics();
    
    std::cout << "=== Service Status ===" << std::endl;
    std::cout << "Status: " << service.GetStatusString() << std::endl;
    std::cout << "Uptime: " << stats.uptime_seconds << " seconds" << std::endl;
    std::cout << "Devices: " << stats.total_devices << " total, " 
              << stats.running_devices << " running, " 
              << stats.connected_devices << " connected" << std::endl;
    std::cout << "Data Points: " << stats.total_data_points << std::endl;
    std::cout << "Scan Success Rate: " << stats.overall_success_rate << "%" << std::endl;
    
    if (stats.redis_connected) {
        std::cout << "Redis: Connected (Published: " << stats.redis_publish_count 
                  << ", Subscribed: " << stats.redis_subscribe_count << ")" << std::endl;
    } else {
        std::cout << "Redis: Disconnected" << std::endl;
    }
    
    std::cout << std::endl;
}

void PrintDeviceStatus(const ModbusService& service) {
    auto device_status_list = service.GetAllDeviceStatus();
    
    std::cout << "=== Device Status ===" << std::endl;
    
    if (device_status_list.empty()) {
        std::cout << "No devices configured" << std::endl;
    } else {
        for (const auto& status : device_status_list) {
            std::cout << "Device " << status.device_id << ":" << std::endl;
            std::cout << "  Status: " << static_cast<int>(status.status) << std::endl;
            std::cout << "  Message: " << status.status_message << std::endl;
            std::cout << "  Scans: " << status.scan_count 
                      << " (Success: " << status.success_count 
                      << ", Rate: " << status.success_rate << "%)" << std::endl;
            std::cout << "  Errors: " << status.error_count << std::endl;
        }
    }
    
    std::cout << std::endl;
}

void TestBasicOperations(ModbusService& service) {
    std::cout << "=== Testing Basic Operations ===" << std::endl;
    
    // 创建测试设备配置
    DeviceConfig device_config(1, "Test Device 1");
    device_config.device_type = "RTU Test Device";
    device_config.comm_type = CommType::RTU;
    device_config.comm_config = R"({"device":"/dev/ttyS1","baud":9600,"parity":"N"})";
    device_config.point_table_file = "test_points.json";
    device_config.enable_redis = true;
    device_config.auto_start_devices = false;  // 手动启动
    
    // 添加设备
    auto add_result = service.AddDevice(device_config);
    std::cout << "Add device: " << (add_result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    if (!add_result.IsSuccess()) {
        std::cout << "  Error: " << add_result.error_message << std::endl;
    }
    
    // 启动设备
    auto start_result = service.StartDevice(1);
    std::cout << "Start device: " << (start_result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    if (!start_result.IsSuccess()) {
        std::cout << "  Error: " << start_result.error_message << std::endl;
    }
    
    // 等待一段时间让设备初始化
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // 测试数据读取
    TypeIndex test_point;
    test_point.data_type = DataType::YC;
    test_point.point_id = 1;
    
    auto read_result = service.ReadDataPoint(1, test_point);
    std::cout << "Read data point: " << (read_result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    if (read_result.IsSuccess()) {
        std::cout << "  Value: " << read_result.data.scaled_value << std::endl;
        std::cout << "  Timestamp: " << read_result.data.timestamp << std::endl;
        std::cout << "  Valid: " << (read_result.data.is_valid ? "Yes" : "No") << std::endl;
    } else {
        std::cout << "  Error: " << read_result.error_message << std::endl;
    }
    
    // 测试数据写入
    TypeIndex control_point;
    control_point.data_type = DataType::YK;
    control_point.point_id = 1;
    
    auto write_result = service.WriteDataPoint(1, control_point, 1.0);
    std::cout << "Write data point: " << (write_result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    if (!write_result.IsSuccess()) {
        std::cout << "  Error: " << write_result.error_message << std::endl;
    }
    
    // 测试 Redis 发布
    auto publish_result = service.PublishData(1, test_point, 123.45);
    std::cout << "Publish to Redis: " << (publish_result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    if (!publish_result.IsSuccess()) {
        std::cout << "  Error: " << publish_result.error_message << std::endl;
    }
    
    std::cout << std::endl;
}

void TestCallbacks(ModbusService& service) {
    std::cout << "=== Testing Callbacks ===" << std::endl;
    
    // 设置状态变化回调
    service.SetStatusChangeCallback([](ServiceStatus old_status, ServiceStatus new_status) {
        std::cout << "Service status changed from " << static_cast<int>(old_status) 
                  << " to " << static_cast<int>(new_status) << std::endl;
    });
    
    // 设置设备事件回调
    service.SetDeviceEventCallback([](int device_id, const std::string& event, const std::string& message) {
        std::cout << "Device " << device_id << " event: " << event << " - " << message << std::endl;
    });
    
    // 设置数据变化回调
    service.SetDataEventCallback([](int device_id, const DataPointValue& value) {
        std::cout << "Device " << device_id << " data change: " 
                  << static_cast<int>(value.type_idx.data_type) << ":" << value.type_idx.point_id 
                  << " = " << value.scaled_value << std::endl;
    });
    
    // 设置错误回调
    service.SetErrorCallback([](const std::string& error_message) {
        std::cout << "Service error: " << error_message << std::endl;
    });
    
    std::cout << "Callbacks configured" << std::endl;
    std::cout << std::endl;
}

void TestConfiguration(ModbusService& service) {
    std::cout << "=== Testing Configuration ===" << std::endl;
    
    // 获取当前配置
    auto config = service.GetConfig();
    std::cout << "Service Name: " << config.service_name << std::endl;
    std::cout << "Version: " << config.version << std::endl;
    std::cout << "Log Level: " << static_cast<int>(config.log_level) << std::endl;
    std::cout << "Redis Enabled: " << (config.enable_redis ? "Yes" : "No") << std::endl;
    std::cout << "Auto Start Devices: " << (config.auto_start_devices ? "Yes" : "No") << std::endl;
    std::cout << "Monitoring Enabled: " << (config.enable_monitoring ? "Yes" : "No") << std::endl;
    
    // 测试配置保存
    auto save_result = service.SaveConfigToFile("test_config.json");
    std::cout << "Save config: " << (save_result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    
    std::cout << std::endl;
}

void RunServiceDemo() {
    std::cout << "Modbus Protocol Service Demo" << std::endl;
    std::cout << "============================" << std::endl;
    
    // 创建开发环境配置
    auto config = ModbusServiceFactory::GetDevelopmentConfig();
    config.enable_redis = false;  // 禁用 Redis 以简化演示
    config.auto_start_devices = false;
    
    // 创建服务
    auto service = ModbusServiceFactory::CreateService(config);
    
    // 初始化服务
    std::cout << "Initializing service..." << std::endl;
    auto init_result = service->Initialize();
    if (!init_result.IsSuccess()) {
        std::cerr << "Failed to initialize service: " << init_result.error_message << std::endl;
        return;
    }
    
    // 设置回调
    TestCallbacks(*service);
    
    // 启动服务
    std::cout << "Starting service..." << std::endl;
    auto start_result = service->Start();
    if (!start_result.IsSuccess()) {
        std::cerr << "Failed to start service: " << start_result.error_message << std::endl;
        return;
    }
    
    // 打印初始状态
    PrintServiceStatus(*service);
    PrintDeviceStatus(*service);
    
    // 测试基本操作
    TestBasicOperations(*service);
    
    // 测试配置
    TestConfiguration(*service);
    
    // 运行一段时间并监控状态
    std::cout << "Running service for 30 seconds..." << std::endl;
    for (int i = 0; i < 6; ++i) {
        std::this_thread::sleep_for(std::chrono::seconds(5));
        
        std::cout << "--- Status Update " << (i + 1) << " ---" << std::endl;
        PrintServiceStatus(*service);
        PrintDeviceStatus(*service);
    }
    
    // 停止服务
    std::cout << "Stopping service..." << std::endl;
    service->Stop();
    
    // 最终状态
    PrintServiceStatus(*service);
    
    std::cout << "Service demo completed!" << std::endl;
}

int main() {
    try {
        // 初始化日志系统
        Logger::GetInstance().Initialize(LogLevel::INFO, LogTarget::CONSOLE);
        
        // 运行演示
        RunServiceDemo();
        
        // 关闭日志系统
        Logger::GetInstance().Shutdown();
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown exception occurred" << std::endl;
        return 1;
    }
}
