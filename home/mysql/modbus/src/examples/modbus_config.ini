[Service]
# Redis 服务器配置
RedisIP = **************
RedisPort = 6379

# 日志配置
LogLevel = INFO
LogFile = modbus_service.log

# 数据上报间隔 (毫秒)
ReportTime = 200

# ========================================
# 设备1配置 - RTU 设备
# ========================================
[Device1]
DeviceID = 1
DeviceName = XJ-SVG-1
CommType = 0        # 0=RTU, 1=TCP
SlaveID = 1         # Modbus 从站地址

# RTU 通信参数
Device = /dev/ttyS1
Baud = 9600
Parity = N
DataBit = 8
StopBit = 1
Mode = 1
Timeout = 1000

# 数据采集配置
ScanInterval = 1000     # 扫描间隔 (毫秒)
EnableAutoScan = true   # 启用自动扫描

# ========================================
# 设备1数据点配置
# 格式: 功能码,起始地址,数量,Redis序号起始,数据类型,参数
# 功能码: 01=读线圈, 02=读输入状态, 03=读保持寄存器, 04=读输入寄存器
#         05=写单线圈, 06=写单寄存器, 15=写多线圈, 16=写多寄存器
# 数据类型: 0=uint16, 1=int16, 2=uint32, 3=int32, 4=float, 5=double
# ========================================

[Device1.YC]  # 遥测 (模拟量)
# 电压电流功率测量 - 保持寄存器 40001-40010
03,0000,10,1,0
# 温度压力测量 - 输入寄存器 30001-30005
04,0000,5,11,0
# 扩展测量值 - 保持寄存器 40021-40025 (32位数据)
03,0014,5,16,2

[Device1.YX]  # 遥信 (开关量)
# 主要开关状态 - 线圈 00001-00032
01,0000,32,1,0
# 辅助状态信号 - 输入状态 10001-10016
02,0000,16,33,0

[Device1.YT]  # 遥脉 (累计量)
# 电能累计值 - 保持寄存器 40101-40110 (32位数据)
03,0064,10,1,2

[Device1.YK]  # 遥控 (开关量控制)
# 开关控制 - 线圈 00001-00010
05,0000,10,1,0

[Device1.YS]  # 遥设 (模拟量设置)
# 设定值寄存器 - 保持寄存器 40201-40210
06,00C8,10,1,0

# ========================================
# 设备2配置 - RTU 设备 (简化示例)
# ========================================
[Device2]
DeviceID = 2
DeviceName = XJ-SVG-2
CommType = 0        # 0=RTU, 1=TCP
SlaveID = 2         # Modbus 从站地址

# RTU 通信参数
Device = /dev/ttyS2
Baud = 9600
Parity = N
DataBit = 8
StopBit = 1
Mode = 1
Timeout = 1000

# 数据采集配置
ScanInterval = 1000     # 扫描间隔 (毫秒)
EnableAutoScan = true   # 启用自动扫描

[Device2.YC]  # 遥测 (模拟量)
# 基本测量值 - 保持寄存器 40001-40008
03,0000,8,1,0

[Device2.YX]  # 遥信 (开关量)
# 状态信号 - 线圈 00001-00016
01,0000,16,1,0

[Device2.YT]  # 遥脉 (累计量)
# 累计值 - 保持寄存器 40101-40104 (32位数据)
03,0064,4,1,2

# ========================================
# 设备3配置 - TCP 设备示例
# ========================================
[Device3]
DeviceID = 3
DeviceName = TCP-Device-1
CommType = 1        # 0=RTU, 1=TCP
SlaveID = 1         # Modbus 从站地址

# TCP 通信参数
IP = *************
Port = 502
Timeout = 3000

# 数据采集配置
ScanInterval = 1000     # 扫描间隔 (毫秒)
EnableAutoScan = true   # 启用自动扫描

[Device3.YC]  # 遥测 (模拟量)
# TCP设备测量值 - 保持寄存器 40001-40010
03,0000,10,1,0
# 输入寄存器测量值 - 输入寄存器 30001-30005
04,0000,5,11,0
# 扩展测量值 - 保持寄存器 40021-40025 (32位数据)
03,0014,5,16,2

[Device3.YX]  # 遥信 (开关量)
# 主要状态 - 线圈 00001-00032
01,0000,32,1,0
# 辅助状态 - 输入状态 10001-10016
02,0000,16,33,0

[Device3.YT]  # 遥脉 (累计量)
# 累计数据 - 保持寄存器 40101-40110 (32位数据)
03,0064,10,1,2

[Device3.YK]  # 遥控 (开关量控制)
# 控制输出 - 线圈 00001-00010
05,0000,10,1,0

[Device3.YS]  # 遥设 (模拟量设置)
# 设定参数 - 保持寄存器 40201-40210
06,00C8,10,1,0

# 高级配置
[Advanced]
# 自动重连配置
EnableAutoReconnect = true
ReconnectInterval = 5000    # 重连间隔 (毫秒)
MaxReconnectAttempts = 10   # 最大重连次数

# 连接监控配置
ConnectionCheckInterval = 30000  # 连接检查间隔 (毫秒)

# 线程池配置
ThreadPoolSize = 4          # 线程池大小

# 数据缓存配置
EnableDataCache = true
CacheSize = 1000           # 缓存大小

# 性能监控
EnablePerformanceMonitor = true
StatisticsInterval = 60000  # 统计间隔 (毫秒)