# 示例程序 CMakeLists.txt

# 基础示例：RTU 通信
if(LIBMODBUS_FOUND)
    add_executable(example_rtu_basic example_rtu_basic.cpp)
    target_link_libraries(example_rtu_basic modbus_protocol_static)

    # TCP 通信示例
    add_executable(example_tcp_basic example_tcp_basic.cpp)
    target_link_libraries(example_tcp_basic modbus_protocol_static)
endif()

# 配置文件示例
add_executable(example_config_parser example_config_parser.cpp)
target_link_libraries(example_config_parser modbus_protocol_static)

# Redis 示例
if(HIREDIS_FOUND)
    add_executable(example_redis_publisher example_redis_publisher.cpp)
    target_link_libraries(example_redis_publisher modbus_protocol_static)
endif()

# 完整服务示例
add_executable(example_complete_service example_complete_service.cpp)
target_link_libraries(example_complete_service modbus_protocol_static)
