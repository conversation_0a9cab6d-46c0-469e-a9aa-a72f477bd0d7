#include "modbus_device.h"
#include "../utils/utils.h"

namespace modbus {

// ModbusDevice 实现
ModbusDevice::ModbusDevice(const DeviceConfig& config)
    : config_(config)
    , status_(DeviceStatus::DISCONNECTED)
    , running_(false)
    , scan_running_(false)
    , last_scan_time_(0)
    , scan_interval_ms_(config.scan_interval_ms > 0 ? config.scan_interval_ms : 1000) {
    
    WRITE_INFO_LOG("Creating Modbus device: %s (ID: %d)", 
                   config_.device_name.c_str(), config_.device_id);
}

ModbusDevice::~ModbusDevice() {
    Shutdown();
    WRITE_INFO_LOG("Modbus device destroyed: %s (ID: %d)", 
                   config_.device_name.c_str(), config_.device_id);
}

Result<bool> ModbusDevice::Initialize() {
    std::lock_guard<MutexLock> lock(status_mutex_);
    
    if (status_ != DeviceStatus::DISCONNECTED) {
        return Result<bool>(ErrorCode::INVALID_STATE, "Device already initialized");
    }
    
    // 创建数据点管理器
    data_manager_ = std::make_unique<DataPointManager>();
    auto init_result = data_manager_->Initialize();
    if (!init_result.IsSuccess()) {
        return Result<bool>(ErrorCode::INIT_FAILED, "Failed to initialize data manager: " + init_result.error_message);
    }
    
    // 加载点表配置
    if (!config_.point_table_file.empty()) {
        auto load_result = data_manager_->LoadPointTable(config_.point_table_file);
        if (!load_result.IsSuccess()) {
            WRITE_WARN_LOG("Failed to load point table: %s", load_result.error_message.c_str());
        }
    }
    
    // 调用子类初始化
    auto do_init_result = DoInitialize();
    if (!do_init_result.IsSuccess()) {
        return do_init_result;
    }
    
    SetStatus(DeviceStatus::INITIALIZED);
    
    WRITE_INFO_LOG("Device initialized successfully: %s", config_.device_name.c_str());
    return Result<bool>(true);
}

Result<bool> ModbusDevice::Start() {
    std::lock_guard<MutexLock> lock(status_mutex_);
    
    if (status_ == DeviceStatus::CONNECTED) {
        return Result<bool>(true); // Already started
    }
    
    if (status_ != DeviceStatus::INITIALIZED && status_ != DeviceStatus::DISCONNECTED) {
        return Result<bool>(ErrorCode::INVALID_STATE, "Device not in valid state to start");
    }
    
    // 调用子类启动
    auto start_result = DoStart();
    if (!start_result.IsSuccess()) {
        return start_result;
    }
    
    // 连接设备
    auto connect_result = DoConnect();
    if (!connect_result.IsSuccess()) {
        SetStatus(DeviceStatus::ERROR);
        return connect_result;
    }
    
    running_ = true;
    SetStatus(DeviceStatus::CONNECTED);
    
    // 启动扫描线程
    if (config_.auto_scan) {
        StartScanThread();
    }
    
    WRITE_INFO_LOG("Device started successfully: %s", config_.device_name.c_str());
    return Result<bool>(true);
}

Result<bool> ModbusDevice::Stop() {
    std::lock_guard<MutexLock> lock(status_mutex_);
    
    if (status_ == DeviceStatus::DISCONNECTED) {
        return Result<bool>(true); // Already stopped
    }
    
    running_ = false;
    
    // 停止扫描线程
    StopScanThread();
    
    // 断开连接
    auto disconnect_result = DoDisconnect();
    
    // 调用子类停止
    auto stop_result = DoStop();
    
    SetStatus(DeviceStatus::DISCONNECTED);
    
    WRITE_INFO_LOG("Device stopped: %s", config_.device_name.c_str());
    return Result<bool>(true);
}

Result<bool> ModbusDevice::Restart() {
    auto stop_result = Stop();
    if (!stop_result.IsSuccess()) {
        return stop_result;
    }
    
    utils::TimeUtils::SleepMs(1000); // Wait 1 second
    
    return Start();
}

void ModbusDevice::Shutdown() {
    Stop();
    
    std::lock_guard<MutexLock> lock(status_mutex_);
    data_manager_.reset();
    comm_.reset();
}

Result<DataPointValue> ModbusDevice::ReadDataPoint(const TypeIndex& type_idx) {
    if (!IsRunning()) {
        return Result<DataPointValue>(ErrorCode::DEVICE_NOT_READY, "Device not running");
    }
    
    if (!data_manager_) {
        return Result<DataPointValue>(ErrorCode::INVALID_STATE, "Data manager not initialized");
    }
    
    return data_manager_->ReadDataPoint(type_idx);
}

Result<bool> ModbusDevice::WriteDataPoint(const TypeIndex& type_idx, double value) {
    if (!IsRunning()) {
        return Result<bool>(ErrorCode::DEVICE_NOT_READY, "Device not running");
    }
    
    if (!data_manager_) {
        return Result<bool>(ErrorCode::INVALID_STATE, "Data manager not initialized");
    }
    
    auto result = data_manager_->WriteDataPoint(type_idx, value);
    
    if (result.IsSuccess()) {
        // 通知命令执行事件
        NotifyEvent(DeviceEventType::COMMAND_EXECUTED, 
                   utils::StringUtils::Format("Write %d:%d = %.2f", 
                                             static_cast<int>(type_idx.data_type), 
                                             type_idx.point_id, value));
    }
    
    return result;
}

Result<std::vector<DataPointValue>> ModbusDevice::ReadAllDataPoints() {
    if (!IsRunning()) {
        return Result<std::vector<DataPointValue>>(ErrorCode::DEVICE_NOT_READY, "Device not running");
    }
    
    if (!data_manager_) {
        return Result<std::vector<DataPointValue>>(ErrorCode::INVALID_STATE, "Data manager not initialized");
    }
    
    return data_manager_->ReadAllDataPoints();
}

DeviceStatistics ModbusDevice::GetStatistics() const {
    std::lock_guard<MutexLock> lock(stats_mutex_);
    
    // 更新成功率
    if (statistics_.scan_count > 0) {
        statistics_.success_rate = (double)statistics_.success_count / statistics_.scan_count * 100.0;
    }
    
    // 更新数据点统计
    if (data_manager_) {
        auto data_stats = data_manager_->GetStatistics();
        statistics_.data_point_count = data_stats.total_points;
        statistics_.active_point_count = data_stats.active_points;
    }
    
    return statistics_;
}

void ModbusDevice::ResetStatistics() {
    std::lock_guard<MutexLock> lock(stats_mutex_);
    statistics_ = DeviceStatistics();
}

void ModbusDevice::SetStatus(DeviceStatus new_status) {
    DeviceStatus old_status = status_;
    status_ = new_status;
    
    if (old_status != new_status) {
        WRITE_INFO_LOG("设备 %s 状态变更: %d -> %d",
                       config_.device_name.c_str(),
                       static_cast<int>(old_status),
                       static_cast<int>(new_status));
        
        if (status_callback_) {
            std::lock_guard<MutexLock> lock(callback_mutex_);
            try {
                status_callback_(config_.device_id, old_status, new_status);
            } catch (const std::exception& e) {
                WRITE_WARN_LOG("状态回调函数异常: %s", e.what());
            }
        }
    }
}

void ModbusDevice::UpdateStatistics(bool success) {
    std::lock_guard<MutexLock> lock(stats_mutex_);
    
    statistics_.scan_count++;
    statistics_.last_scan_time = utils::TimeUtils::GetCurrentTimestamp();
    
    if (success) {
        statistics_.success_count++;
        statistics_.last_success_time = statistics_.last_scan_time;
    } else {
        statistics_.error_count++;
    }
}

void ModbusDevice::NotifyDataUpdate(const DataPointValue& value) {
    if (data_callback_) {
        std::lock_guard<MutexLock> lock(callback_mutex_);
        try {
            data_callback_(config_.device_id, value);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("数据回调函数异常: %s", e.what());
        }
    }
}

void ModbusDevice::NotifyEvent(DeviceEventType type, const std::string& message) {
    if (event_callback_) {
        std::lock_guard<MutexLock> lock(callback_mutex_);
        try {
            DeviceEvent event(type, config_.device_id, message);
            event_callback_(config_.device_id, event);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("事件回调函数异常: %s", e.what());
        }
    }
}

void ModbusDevice::StartScanThread() {
    if (scan_running_) {
        return;
    }
    
    scan_running_ = true;
    scan_thread_ = std::make_unique<std::thread>(&ModbusDevice::ScanThreadFunction, this);
}

void ModbusDevice::StopScanThread() {
    if (scan_running_) {
        scan_running_ = false;
        if (scan_thread_ && scan_thread_->joinable()) {
            scan_thread_->join();
        }
        scan_thread_.reset();
    }
}

void ModbusDevice::ScanThreadFunction() {
    WRITE_INFO_LOG("Scan thread started for device: %s", config_.device_name.c_str());
    
    while (scan_running_ && running_) {
        uint64_t start_time = utils::TimeUtils::GetCurrentTimestamp();
        
        // 执行扫描周期
        auto result = ProcessScanCycle();
        UpdateStatistics(result.IsSuccess());
        
        if (!result.IsSuccess()) {
            WRITE_WARN_LOG("Scan cycle failed for device %s: %s", 
                          config_.device_name.c_str(), result.error_message.c_str());
        }
        
        // 计算睡眠时间
        uint64_t elapsed = utils::TimeUtils::GetCurrentTimestamp() - start_time;
        if (elapsed < scan_interval_ms_) {
            utils::TimeUtils::SleepMs(scan_interval_ms_ - elapsed);
        }
    }
    
    WRITE_INFO_LOG("Scan thread stopped for device: %s", config_.device_name.c_str());
}

Result<bool> ModbusDevice::ProcessScanCycle() {
    if (!IsRunning() || !data_manager_) {
        return Result<bool>(ErrorCode::DEVICE_NOT_READY, "Device not ready");
    }
    
    return ScanDataPoints();
}

Result<bool> ModbusDevice::ScanDataPoints() {
    // 读取所有数据点
    auto read_result = data_manager_->ReadAllDataPoints();
    if (!read_result.IsSuccess()) {
        return Result<bool>(ErrorCode::READ_failed, read_result.error_message);
    }
    
    // 通知数据更新
    for (const auto& value : read_result.data) {
        NotifyDataUpdate(value);
    }
    
    return Result<bool>(true);
}

// ModbusRtuDevice 实现
ModbusRtuDevice::ModbusRtuDevice(const DeviceConfig& config) : ModbusDevice(config) {
    if (config_.comm_type != CommType::RTU) {
        WRITE_WARN_LOG("Device config comm_type is not RTU, forcing to RTU");
        config_.comm_type = CommType::RTU;
    }
}

Result<bool> ModbusRtuDevice::DoInitialize() {
    // RTU 设备特定的初始化
    WRITE_INFO_LOG("Initializing RTU device: %s", config_.device_name.c_str());
    return Result<bool>(true);
}

Result<bool> ModbusRtuDevice::DoStart() {
    WRITE_INFO_LOG("Starting RTU device: %s", config_.device_name.c_str());
    return Result<bool>(true);
}

Result<bool> ModbusRtuDevice::DoStop() {
    WRITE_INFO_LOG("Stopping RTU device: %s", config_.device_name.c_str());
    return Result<bool>(true);
}

Result<bool> ModbusRtuDevice::DoConnect() {
    WRITE_INFO_LOG("Connecting RTU device: %s", config_.device_name.c_str());
    // 这里应该创建和连接 RTU 通信接口
    return Result<bool>(true);
}

Result<bool> ModbusRtuDevice::DoDisconnect() {
    WRITE_INFO_LOG("Disconnecting RTU device: %s", config_.device_name.c_str());
    return Result<bool>(true);
}

// ModbusTcpDevice 实现
ModbusTcpDevice::ModbusTcpDevice(const DeviceConfig& config) : ModbusDevice(config) {
    if (config_.comm_type != CommType::TCP) {
        WRITE_WARN_LOG("Device config comm_type is not TCP, forcing to TCP");
        config_.comm_type = CommType::TCP;
    }
}

Result<bool> ModbusTcpDevice::DoInitialize() {
    WRITE_INFO_LOG("Initializing TCP device: %s", config_.device_name.c_str());
    return Result<bool>(true);
}

Result<bool> ModbusTcpDevice::DoStart() {
    WRITE_INFO_LOG("Starting TCP device: %s", config_.device_name.c_str());
    return Result<bool>(true);
}

Result<bool> ModbusTcpDevice::DoStop() {
    WRITE_INFO_LOG("Stopping TCP device: %s", config_.device_name.c_str());
    return Result<bool>(true);
}

Result<bool> ModbusTcpDevice::DoConnect() {
    WRITE_INFO_LOG("Connecting TCP device: %s", config_.device_name.c_str());
    return Result<bool>(true);
}

Result<bool> ModbusTcpDevice::DoDisconnect() {
    WRITE_INFO_LOG("Disconnecting TCP device: %s", config_.device_name.c_str());
    return Result<bool>(true);
}

// ModbusDeviceFactory 实现
std::unique_ptr<ModbusDevice> ModbusDeviceFactory::CreateDevice(const DeviceConfig& config) {
    auto validate_result = ValidateConfig(config);
    if (!validate_result.IsSuccess()) {
        WRITE_ERROR_LOG("Invalid device config: %s", validate_result.error_message.c_str());
        return nullptr;
    }
    
    switch (config.comm_type) {
        case CommType::RTU:
            return CreateRtuDevice(config);
        case CommType::TCP:
            return CreateTcpDevice(config);
        default:
            WRITE_ERROR_LOG("Unsupported communication type: %d", static_cast<int>(config.comm_type));
            return nullptr;
    }
}

std::unique_ptr<ModbusRtuDevice> ModbusDeviceFactory::CreateRtuDevice(const DeviceConfig& config) {
    return std::make_unique<ModbusRtuDevice>(config);
}

std::unique_ptr<ModbusTcpDevice> ModbusDeviceFactory::CreateTcpDevice(const DeviceConfig& config) {
    return std::make_unique<ModbusTcpDevice>(config);
}

Result<bool> ModbusDeviceFactory::ValidateConfig(const DeviceConfig& config) {
    if (config.device_id <= 0) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid device ID");
    }
    
    if (config.device_name.empty()) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Device name cannot be empty");
    }
    
    if (config.comm_type != CommType::RTU && config.comm_type != CommType::TCP) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Invalid communication type");
    }
    
    return Result<bool>(true);
}

} // namespace modbus
