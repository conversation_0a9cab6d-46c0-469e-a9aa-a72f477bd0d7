#ifndef MODBUS_DEVICE_H
#define MODBUS_DEVICE_H

#include "../types/modbus_types.h"
#include "../comm/modbus_comm_interface.h"
#include "../data/data_point_manager.h"
#include "../utils/thread_pool.h"
#include "../utils/logger.h"
#include "../utils/utils.h"
#include <memory>
#include <atomic>
#include <functional>

namespace modbus {

// 设备事件类型
enum class DeviceEventType {
    STATUS_CHANGED,     // 状态变化
    DATA_UPDATED,       // 数据更新
    ERROR_OCCURRED,     // 错误发生
    COMMAND_EXECUTED    // 命令执行
};

// 设备事件结构
struct DeviceEvent {
    DeviceEventType type;
    int device_id;
    std::string message;
    uint64_t timestamp;
    
    DeviceEvent(DeviceEventType t, int id, const std::string& msg)
        : type(t), device_id(id), message(msg), timestamp(utils::TimeUtils::GetCurrentTimestamp()) {}
};

// 设备统计信息
struct DeviceStatistics {
    uint64_t scan_count = 0;
    uint64_t success_count = 0;
    uint64_t error_count = 0;
    uint64_t last_scan_time = 0;
    uint64_t last_success_time = 0;
    double success_rate = 0.0;
    uint32_t data_point_count = 0;
    uint32_t active_point_count = 0;
};

// 设备回调函数类型
using DeviceStatusCallback = std::function<void(int device_id, DeviceStatus old_status, DeviceStatus new_status)>;
using DeviceDataCallback = std::function<void(int device_id, const DataPointValue& value)>;
using DeviceEventCallback = std::function<void(int device_id, const DeviceEvent& event)>;

// Modbus 设备抽象类
class ModbusDevice {
public:
    explicit ModbusDevice(const DeviceConfig& config);
    virtual ~ModbusDevice();
    
    // 基本操作
    virtual Result<bool> Initialize();
    virtual Result<bool> Start();
    virtual Result<bool> Stop();
    virtual Result<bool> Restart();
    virtual void Shutdown();
    
    // 状态查询
    DeviceStatus GetStatus() const { return status_; }
    bool IsRunning() const { return status_ == DeviceStatus::CONNECTED && running_; }
    bool IsConnected() const { return status_ == DeviceStatus::CONNECTED; }
    
    // 配置管理
    const DeviceConfig& GetConfig() const { return config_; }
    Result<bool> UpdateConfig(const DeviceConfig& new_config);
    
    // 数据操作
    virtual Result<DataPointValue> ReadDataPoint(const TypeIndex& type_idx);
    virtual Result<bool> WriteDataPoint(const TypeIndex& type_idx, double value);
    virtual Result<std::vector<DataPointValue>> ReadAllDataPoints();
    
    // 批量操作
    virtual Result<std::vector<DataPointValue>> ReadDataPoints(const std::vector<TypeIndex>& type_indices);
    virtual Result<bool> WriteDataPoints(const std::vector<std::pair<TypeIndex, double>>& values);
    
    // 统计信息
    DeviceStatistics GetStatistics() const;
    void ResetStatistics();
    
    // 回调设置
    void SetStatusCallback(DeviceStatusCallback callback) { status_callback_ = callback; }
    void SetDataCallback(DeviceDataCallback callback) { data_callback_ = callback; }
    void SetEventCallback(DeviceEventCallback callback) { event_callback_ = callback; }
    
    // 设备信息
    int GetDeviceId() const { return config_.device_id; }
    std::string GetDeviceName() const { return config_.device_name; }
    std::string GetDeviceType() const { return config_.device_type; }
    
protected:
    // 子类需要实现的虚函数
    virtual Result<bool> DoInitialize() = 0;
    virtual Result<bool> DoStart() = 0;
    virtual Result<bool> DoStop() = 0;
    virtual Result<bool> DoConnect() = 0;
    virtual Result<bool> DoDisconnect() = 0;
    
    // 内部方法
    void SetStatus(DeviceStatus new_status);
    void UpdateStatistics(bool success);
    void NotifyDataUpdate(const DataPointValue& value);
    void NotifyEvent(DeviceEventType type, const std::string& message);
    
    // 扫描线程
    void StartScanThread();
    void StopScanThread();
    void ScanThreadFunction();
    
    // 数据处理
    virtual Result<bool> ProcessScanCycle();
    virtual Result<bool> ScanDataPoints();
    
protected:
    DeviceConfig config_;
    std::atomic<DeviceStatus> status_;
    std::atomic<bool> running_;
    std::atomic<bool> scan_running_;
    
    // 通信和数据管理
    std::unique_ptr<ModbusCommInterface> comm_;
    std::unique_ptr<DataPointManager> data_manager_;
    
    // 线程管理
    std::unique_ptr<std::thread> scan_thread_;
    mutable MutexLock status_mutex_;
    
    // 统计信息
    mutable DeviceStatistics statistics_;
    mutable MutexLock stats_mutex_;
    
    // 回调函数
    DeviceStatusCallback status_callback_;
    DeviceDataCallback data_callback_;
    DeviceEventCallback event_callback_;
    mutable MutexLock callback_mutex_;
    
    // 时间管理
    uint64_t last_scan_time_;
    uint64_t scan_interval_ms_;
};

// Modbus RTU 设备实现
class ModbusRtuDevice : public ModbusDevice {
public:
    explicit ModbusRtuDevice(const DeviceConfig& config);
    virtual ~ModbusRtuDevice() = default;
    
protected:
    virtual Result<bool> DoInitialize() override;
    virtual Result<bool> DoStart() override;
    virtual Result<bool> DoStop() override;
    virtual Result<bool> DoConnect() override;
    virtual Result<bool> DoDisconnect() override;
};

// Modbus TCP 设备实现
class ModbusTcpDevice : public ModbusDevice {
public:
    explicit ModbusTcpDevice(const DeviceConfig& config);
    virtual ~ModbusTcpDevice() = default;
    
protected:
    virtual Result<bool> DoInitialize() override;
    virtual Result<bool> DoStart() override;
    virtual Result<bool> DoStop() override;
    virtual Result<bool> DoConnect() override;
    virtual Result<bool> DoDisconnect() override;
};

// 设备工厂类
class ModbusDeviceFactory {
public:
    static std::unique_ptr<ModbusDevice> CreateDevice(const DeviceConfig& config);
    static std::unique_ptr<ModbusRtuDevice> CreateRtuDevice(const DeviceConfig& config);
    static std::unique_ptr<ModbusTcpDevice> CreateTcpDevice(const DeviceConfig& config);
    
    // 验证配置
    static Result<bool> ValidateConfig(const DeviceConfig& config);
};

} // namespace modbus

#endif // MODBUS_DEVICE_H
