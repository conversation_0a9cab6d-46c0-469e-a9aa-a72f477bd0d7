#ifndef DEVICE_MANAGER_H
#define DEVICE_MANAGER_H

#include "../types/modbus_types.h"
#include "../comm/modbus_comm_interface.h"
#include "../data/data_point_manager.h"
#include "../redis/redis_manager.h"
#include "../utils/thread_pool.h"
#include "../utils/logger.h"
#include <map>
#include <memory>
#include <functional>
#include <atomic>
#include <shared_mutex>

namespace modbus {

// 设备状态信息
struct DeviceStatusInfo {
    int device_id;
    DeviceStatus status;
    std::string status_message;
    uint64_t last_update_time;
    uint64_t last_scan_time;
    uint64_t last_error_time;
    int error_count;
    int scan_count;
    int success_count;
    double success_rate;
    
    DeviceStatusInfo() : device_id(0), status(DeviceStatus::DISCONNECTED), 
                        last_update_time(0), last_scan_time(0), last_error_time(0),
                        error_count(0), scan_count(0), success_count(0), success_rate(0.0) {}
};

// 设备实例 - 管理单个设备的所有功能
class DeviceInstance {
public:
    explicit DeviceInstance(const DeviceConfig& config);
    virtual ~DeviceInstance();
    
    // 禁止拷贝和赋值
    DeviceInstance(const DeviceInstance&) = delete;
    DeviceInstance& operator=(const DeviceInstance&) = delete;
    
    // 初始化和控制
    Result<bool> Initialize();
    void Shutdown();
    Result<bool> Start();
    void Stop();
    bool IsRunning() const { return is_running_; }
    
    // 配置管理
    void SetConfig(const DeviceConfig& config);
    DeviceConfig GetConfig() const { return config_; }
    
    // 状态管理
    DeviceStatusInfo GetStatusInfo() const;
    DeviceStatus GetStatus() const { return status_; }
    void SetStatus(DeviceStatus status, const std::string& message = "");
    
    // 组件访问
    std::shared_ptr<ModbusCommInterface> GetCommInterface() const { return comm_; }
    std::shared_ptr<DataPointManager> GetDataPointManager() const;
    std::shared_ptr<DeviceRedisManager> GetRedisManager() const { return redis_manager_; }
    
    // 数据操作
    Result<DataPointValue> ReadDataPoint(const TypeIndex& type_idx);
    Result<std::vector<DataPointValue>> ReadAllDataPoints();
    Result<bool> WriteDataPoint(const TypeIndex& type_idx, double value);
    
    // 控制指令处理
    using ControlCommandHandler = std::function<Result<bool>(const ControlCommand& command)>;
    void SetControlCommandHandler(ControlCommandHandler handler);
    
    // 回调函数设置
    using StatusChangeCallback = std::function<void(int device_id, DeviceStatus old_status, DeviceStatus new_status)>;
    using DataChangeCallback = std::function<void(int device_id, const DataPointValue& value)>;
    using AlarmCallback = std::function<void(int device_id, const std::string& alarm_message)>;
    
    void SetStatusChangeCallback(StatusChangeCallback callback);
    void SetDataChangeCallback(DataChangeCallback callback);
    void SetAlarmCallback(AlarmCallback callback);
    
    // 设备信息
    int GetDeviceId() const { return config_.device_id; }
    std::string GetDeviceName() const { return config_.device_name; }
    std::string GetDeviceType() const { return config_.device_type; }
    
private:
    // 内部方法
    Result<bool> CreateCommInterface();
    Result<bool> InitializeDataPointManager();
    Result<bool> InitializeRedisManager();
    void SetupCallbacks();
    
    // 状态更新
    void UpdateStatus(DeviceStatus new_status, const std::string& message = "");
    void UpdateStatistics(bool success);
    
    // 回调处理
    void HandleDataChange(const DataPointValue& value);
    void HandleAlarm(const DataPointEx& point, const DataPointValue& value, const std::string& alarm_type);
    void HandleControlCommand(const ControlCommand& command);
    
private:
    DeviceConfig config_;
    std::atomic<DeviceStatus> status_{DeviceStatus::DISCONNECTED};
    std::atomic<bool> is_running_{false};
    
    // 组件
    std::shared_ptr<ModbusCommInterface> comm_;
    std::shared_ptr<DeviceDataPointManager> data_manager_;
    std::shared_ptr<DeviceRedisManager> redis_manager_;
    
    // 状态信息
    mutable DeviceStatusInfo status_info_;
    mutable std::mutex status_mutex_;
    
    // 回调函数
    ControlCommandHandler control_handler_;
    StatusChangeCallback status_callback_;
    DataChangeCallback data_callback_;
    AlarmCallback alarm_callback_;
    std::mutex callback_mutex_;
};

// 设备管理器 - 管理多个设备实例
class DeviceManager {
public:
    explicit DeviceManager();
    virtual ~DeviceManager();
    
    // 禁止拷贝和赋值
    DeviceManager(const DeviceManager&) = delete;
    DeviceManager& operator=(const DeviceManager&) = delete;
    
    // 初始化和控制
    Result<bool> Initialize();
    void Shutdown();
    Result<bool> Start();
    void Stop();
    bool IsRunning() const { return is_running_; }
    
    // 设备管理
    Result<bool> AddDevice(const DeviceConfig& config);
    Result<bool> RemoveDevice(int device_id);
    Result<bool> UpdateDevice(const DeviceConfig& config);
    Result<std::shared_ptr<DeviceInstance>> GetDevice(int device_id) const;
    std::vector<std::shared_ptr<DeviceInstance>> GetAllDevices() const;
    
    // 批量操作
    Result<bool> StartAllDevices();
    void StopAllDevices();
    Result<bool> AddDevicesFromConfig(const std::string& config_file);
    
    // 设备状态查询
    std::vector<DeviceStatusInfo> GetAllDeviceStatus() const;
    DeviceStatusInfo GetDeviceStatus(int device_id) const;
    int GetDeviceCount() const;
    int GetRunningDeviceCount() const;
    
    // 数据操作
    Result<std::map<int, DataPointValue>> ReadDataPointFromAllDevices(const TypeIndex& type_idx);
    Result<bool> WriteDataPointToDevice(int device_id, const TypeIndex& type_idx, double value);
    
    // 全局回调设置
    using GlobalStatusChangeCallback = std::function<void(int device_id, DeviceStatus old_status, DeviceStatus new_status)>;
    using GlobalDataChangeCallback = std::function<void(int device_id, const DataPointValue& value)>;
    using GlobalAlarmCallback = std::function<void(int device_id, const std::string& alarm_message)>;
    
    void SetGlobalStatusChangeCallback(GlobalStatusChangeCallback callback);
    void SetGlobalDataChangeCallback(GlobalDataChangeCallback callback);
    void SetGlobalAlarmCallback(GlobalAlarmCallback callback);
    
    // 统计信息
    struct ManagerStatistics {
        int total_devices = 0;
        int running_devices = 0;
        int connected_devices = 0;
        int error_devices = 0;
        int total_data_points = 0;
        int total_scan_count = 0;
        int total_success_count = 0;
        int total_error_count = 0;
        double overall_success_rate = 0.0;
        uint64_t last_update_time = 0;
    };
    
    ManagerStatistics GetStatistics() const;
    void ResetStatistics();
    
    // 配置管理
    Result<bool> LoadConfiguration(const std::string& config_file);
    Result<bool> SaveConfiguration(const std::string& config_file) const;
    
    // 设备发现和自动配置
    std::vector<DeviceConfig> DiscoverDevices(CommType comm_type, const std::string& comm_config);
    Result<bool> AutoConfigureDevice(int device_id, CommType comm_type, const std::string& comm_config);
    
private:
    // 内部方法
    void SetupDeviceCallbacks(std::shared_ptr<DeviceInstance> device);
    void HandleDeviceStatusChange(int device_id, DeviceStatus old_status, DeviceStatus new_status);
    void HandleDeviceDataChange(int device_id, const DataPointValue& value);
    void HandleDeviceAlarm(int device_id, const std::string& alarm_message);
    
    // 配置解析
    Result<std::vector<DeviceConfig>> ParseConfigFile(const std::string& config_file);
    std::string SerializeConfigs(const std::vector<DeviceConfig>& configs) const;
    
private:
    std::atomic<bool> is_running_{false};
    
    // 设备存储
    std::map<int, std::shared_ptr<DeviceInstance>> devices_;
    mutable std::shared_mutex devices_mutex_;
    
    // 全局回调
    GlobalStatusChangeCallback global_status_callback_;
    GlobalDataChangeCallback global_data_callback_;
    GlobalAlarmCallback global_alarm_callback_;
    std::mutex global_callback_mutex_;
    
    // 统计信息
    mutable ManagerStatistics stats_;
    mutable std::mutex stats_mutex_;
};

// 全局设备管理器
class GlobalDeviceManager {
public:
    static DeviceManager& GetInstance();
    static bool Initialize();
    static void Shutdown();
    
private:
    GlobalDeviceManager() = default;
    ~GlobalDeviceManager() = default;
    
    GlobalDeviceManager(const GlobalDeviceManager&) = delete;
    GlobalDeviceManager& operator=(const GlobalDeviceManager&) = delete;
    
private:
    static std::unique_ptr<DeviceManager> instance_;
    static std::mutex instance_mutex_;
};

// 便捷宏定义
#define DEVICE_MANAGER() modbus::GlobalDeviceManager::GetInstance()

} // namespace modbus

#endif // DEVICE_MANAGER_H
