#include "device_manager.h"
#include "../utils/utils.h"
#include "../comm/modbus_comm_interface.h"
#include <sstream>

namespace modbus {

// 静态成员初始化
std::unique_ptr<DeviceManager> GlobalDeviceManager::instance_;
std::mutex GlobalDeviceManager::instance_mutex_;

// DeviceInstance 实现
DeviceInstance::DeviceInstance(const DeviceConfig& config)
    : config_(config) {
}

DeviceInstance::~DeviceInstance() {
    Shutdown();
}

Result<bool> DeviceInstance::Initialize() {
    // 创建通信接口
    auto comm_result = CreateCommInterface();
    if (!comm_result.IsSuccess()) {
        return comm_result;
    }
    
    // 初始化数据点管理器
    auto data_result = InitializeDataPointManager();
    if (!data_result.IsSuccess()) {
        return data_result;
    }
    
    // 初始化 Redis 管理器
    if (config_.enable_redis) {
        auto redis_result = InitializeRedisManager();
        if (!redis_result.IsSuccess()) {
            WRITE_WARN_LOG("设备 %d Redis 管理器初始化失败: %s",
                          config_.device_id, redis_result.error_message.c_str());
        }
    }
    
    // 设置回调
    SetupCallbacks();
    
    UpdateStatus(DeviceStatus::INITIALIZED, "Device initialized successfully");
    
    WRITE_INFO_LOG("设备 %d (%s) 初始化完成", config_.device_id, config_.device_name.c_str());
    return Result<bool>(true);
}

void DeviceInstance::Shutdown() {
    Stop();
    
    if (data_manager_) {
        data_manager_->Shutdown();
    }
    
    if (redis_manager_) {
        redis_manager_->Shutdown();
    }
    
    if (comm_) {
        comm_->Disconnect();
    }
    
    UpdateStatus(DeviceStatus::DISCONNECTED, "Device shutdown");
    
    WRITE_INFO_LOG("设备 %d (%s) 已关闭", config_.device_id, config_.device_name.c_str());
}

Result<bool> DeviceInstance::Start() {
    if (is_running_) {
        return Result<bool>(true);
    }
    
    // 连接通信接口
    if (comm_) {
        auto connect_result = comm_->Connect();
        if (!connect_result.IsSuccess()) {
            UpdateStatus(DeviceStatus::ERROR, "Failed to connect: " + connect_result.error_message);
            return connect_result;
        }
    }
    
    // 启动数据点管理器
    if (data_manager_ && data_manager_->GetDataPointManager()) {
        auto start_result = data_manager_->GetDataPointManager()->Start();
        if (!start_result.IsSuccess()) {
            UpdateStatus(DeviceStatus::ERROR, "Failed to start data manager: " + start_result.error_message);
            return start_result;
        }
    }

    // 启动Redis管理器（订阅控制通道）
    if (redis_manager_) {
        auto redis_start_result = redis_manager_->Start();
        if (!redis_start_result.IsSuccess()) {
            WRITE_WARN_LOG("设备 %d Redis 管理器启动失败: %s",
                          config_.device_id, redis_start_result.error_message.c_str());
            // 不返回错误，因为Redis启动失败不应该阻止设备启动
        }
    }

    is_running_ = true;
    UpdateStatus(DeviceStatus::CONNECTED, "Device started successfully");
    
    WRITE_INFO_LOG("设备 %d (%s) 已启动", config_.device_id, config_.device_name.c_str());
    return Result<bool>(true);
}

void DeviceInstance::Stop() {
    if (!is_running_) {
        return;
    }
    
    is_running_ = false;
    
    // 停止数据点管理器
    if (data_manager_ && data_manager_->GetDataPointManager()) {
        data_manager_->GetDataPointManager()->Stop();
    }
    
    // 断开通信连接
    if (comm_) {
        comm_->Disconnect();
    }
    
    UpdateStatus(DeviceStatus::DISCONNECTED, "Device stopped");
    
    WRITE_INFO_LOG("设备 %d (%s) 已停止", config_.device_id, config_.device_name.c_str());
}

void DeviceInstance::SetConfig(const DeviceConfig& config) {
    bool was_running = is_running_;
    
    if (was_running) {
        Stop();
    }
    
    config_ = config;
    
    if (was_running) {
        Initialize();
        Start();
    }
}

DeviceStatusInfo DeviceInstance::GetStatusInfo() const {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    DeviceStatusInfo info = status_info_;
    info.device_id = config_.device_id;
    info.status = status_;
    info.last_update_time = utils::TimeUtils::GetCurrentTimestamp();
    
    // 从数据点管理器获取统计信息
    if (data_manager_ && data_manager_->GetDataPointManager()) {
        auto stats = data_manager_->GetDataPointManager()->GetStatistics();
        info.scan_count = stats.scan_count;
        info.success_count = stats.success_count;
        info.success_rate = stats.success_rate;
        info.last_scan_time = stats.last_scan_time;
    }
    
    return info;
}

void DeviceInstance::SetStatus(DeviceStatus status, const std::string& message) {
    UpdateStatus(status, message);
}

std::shared_ptr<DataPointManager> DeviceInstance::GetDataPointManager() const {
    return data_manager_ ? data_manager_->GetDataPointManager() : nullptr;
}

Result<DataPointValue> DeviceInstance::ReadDataPoint(const TypeIndex& type_idx) {
    if (!data_manager_ || !data_manager_->GetDataPointManager()) {
        return Result<DataPointValue>(ErrorCode::NOT_INITIALIZED, "Data manager not initialized");
    }
    
    return data_manager_->GetDataPointManager()->ReadDataPoint(type_idx);
}

Result<std::vector<DataPointValue>> DeviceInstance::ReadAllDataPoints() {
    if (!data_manager_ || !data_manager_->GetDataPointManager()) {
        return Result<std::vector<DataPointValue>>(ErrorCode::NOT_INITIALIZED, "Data manager not initialized");
    }
    
    return data_manager_->GetDataPointManager()->ReadAllDataPoints();
}

Result<bool> DeviceInstance::WriteDataPoint(const TypeIndex& type_idx, double value) {
    if (!data_manager_ || !data_manager_->GetDataPointManager()) {
        return Result<bool>(ErrorCode::NOT_INITIALIZED, "Data manager not initialized");
    }
    
    return data_manager_->GetDataPointManager()->WriteDataPoint(type_idx, value);
}

void DeviceInstance::SetControlCommandHandler(ControlCommandHandler handler) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    control_handler_ = handler;
}

void DeviceInstance::SetStatusChangeCallback(StatusChangeCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    status_callback_ = callback;
}

void DeviceInstance::SetDataChangeCallback(DataChangeCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    data_callback_ = callback;
}

void DeviceInstance::SetAlarmCallback(AlarmCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    alarm_callback_ = callback;
}

// 内部方法实现
Result<bool> DeviceInstance::CreateCommInterface() {
    WRITE_INFO_LOG("正在为设备 %d 创建通信接口 (类型: %d)",
                   config_.device_id, static_cast<int>(config_.comm_type));

    // 解析通信配置字符串
    if (config_.comm_config.empty()) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Communication config is empty");
    }

    if (config_.comm_type == CommType::RTU) {
        // 解析RTU配置: "device,baud,parity"
        std::vector<std::string> parts;
        std::stringstream ss(config_.comm_config);
        std::string item;
        while (std::getline(ss, item, ',')) {
            parts.push_back(item);
        }

        if (parts.size() < 3) {
            return Result<bool>(ErrorCode::CONFIG_ERROR, "Invalid RTU config format");
        }

        RtuCommParam rtu_param;
        rtu_param.device = parts[0];
        rtu_param.baud = std::stoi(parts[1]);
        rtu_param.parity = parts[2][0];
        rtu_param.data_bit = 8;
        rtu_param.stop_bit = 1;
        rtu_param.mode = 0;  // MODBUS_RTU_RS232
        rtu_param.timeout_ms = 1000;

        comm_ = ModbusCommFactory::CreateRtuComm(rtu_param);
        if (!comm_) {
            return Result<bool>(ErrorCode::CONFIG_ERROR, "Failed to create RTU communication interface");
        }

        auto init_result = comm_->Initialize();
        if (!init_result.IsSuccess()) {
            comm_.reset();
            return Result<bool>(ErrorCode::CONFIG_ERROR, "Failed to initialize RTU communication: " + init_result.error_message);
        }

        WRITE_INFO_LOG("RTU通信接口创建成功: %s", rtu_param.device.c_str());
    } else {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Unsupported communication type");
    }

    return Result<bool>(true);
}

Result<bool> DeviceInstance::InitializeDataPointManager() {
    data_manager_ = std::make_shared<DeviceDataPointManager>(config_.device_id);
    data_manager_->SetDeviceName(config_.device_name);
    
    auto result = data_manager_->Initialize(comm_, config_.point_table_file, config_.embedded_point_config);
    if (!result.IsSuccess()) {
        return result;
    }
    
    // 配置数据上报
    data_manager_->EnableDataReporting(config_.enable_redis);
    data_manager_->SetReportInterval(config_.redis_report_interval_ms);



    // 配置扫描参数
    if (data_manager_->GetDataPointManager()) {
        data_manager_->GetDataPointManager()->EnableAutoScan(config_.enable_auto_scan);
        data_manager_->GetDataPointManager()->SetScanInterval(config_.scan_interval_ms);

        // 配置变化检测参数
        data_manager_->GetDataPointManager()->SetGlobalChangeDetection(
            config_.enable_change_detection, config_.change_threshold);
    }
    
    return Result<bool>(true);
}



Result<bool> DeviceInstance::InitializeRedisManager() {
    // 获取全局 Redis 管理器
    auto& global_redis = REDIS_MANAGER();
    auto redis_shared = std::shared_ptr<RedisManager>(&global_redis, [](RedisManager*){});
    
    redis_manager_ = std::make_shared<DeviceRedisManager>(config_.device_id, redis_shared);
    redis_manager_->SetDeviceName(config_.device_name);
    
    return redis_manager_->Initialize();
}

void DeviceInstance::SetupCallbacks() {
    // 设置数据点管理器回调
    if (data_manager_ && data_manager_->GetDataPointManager()) {
        data_manager_->GetDataPointManager()->SetDataChangeCallback([this](const DataPointValue& value) {
            HandleDataChange(value);
        });
        
        data_manager_->GetDataPointManager()->SetAlarmCallback([this](const DataPointEx& point, const DataPointValue& value, const std::string& alarm_type) {
            HandleAlarm(point, value, alarm_type);
        });
    }
    
    // 设置 Redis 管理器回调
    if (redis_manager_) {
        redis_manager_->SetControlHandler([this](const ControlCommand& command) {
            HandleControlCommand(command);
            return Result<bool>(true);
        });
    }
}

void DeviceInstance::UpdateStatus(DeviceStatus new_status, const std::string& message) {
    DeviceStatus old_status = status_;
    status_ = new_status;
    
    {
        std::lock_guard<std::mutex> lock(status_mutex_);
        status_info_.status = new_status;
        status_info_.status_message = message;
        status_info_.last_update_time = utils::TimeUtils::GetCurrentTimestamp();
        
        if (new_status == DeviceStatus::ERROR) {
            status_info_.error_count++;
            status_info_.last_error_time = status_info_.last_update_time;
        }
    }
    
    // 触发状态变化回调
    if (old_status != new_status && status_callback_) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        try {
            status_callback_(config_.device_id, old_status, new_status);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("状态回调函数异常: %s", e.what());
        }
    }
}

void DeviceInstance::UpdateStatistics(bool success) {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    if (success) {
        status_info_.success_count++;
    } else {
        status_info_.error_count++;
    }
    
    status_info_.scan_count++;
    
    if (status_info_.scan_count > 0) {
        status_info_.success_rate = (double)status_info_.success_count / status_info_.scan_count * 100.0;
    }
}

void DeviceInstance::HandleDataChange(const DataPointValue& value) {
    // 上报到 Redis
    if (redis_manager_) {
        redis_manager_->PublishData(value.type_idx, value.scaled_value, value.timestamp);
    }
    
    // 触发数据变化回调
    if (data_callback_) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        try {
            data_callback_(config_.device_id, value);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("数据回调函数异常: %s", e.what());
        }
    }
}

void DeviceInstance::HandleAlarm(const DataPointEx& point, const DataPointValue& value, const std::string& alarm_type) {
    std::string alarm_message = utils::StringUtils::Format("Device %d Point %d:%d %s alarm: %.6f", 
                                                          config_.device_id, 
                                                          static_cast<int>(point.type_idx.data_type),
                                                          point.type_idx.point_id,
                                                          alarm_type.c_str(),
                                                          value.scaled_value);
    
    // 上报到 Redis
    if (redis_manager_) {
        redis_manager_->PublishAlarm(point.type_idx, alarm_type, value.scaled_value, alarm_message);
    }
    
    // 触发报警回调
    if (alarm_callback_) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        try {
            alarm_callback_(config_.device_id, alarm_message);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("Alarm callback exception: %s", e.what());
        }
    }
}

void DeviceInstance::HandleControlCommand(const ControlCommand& command) {
    if (control_handler_) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        try {
            auto result = control_handler_(command);
            if (!result.IsSuccess()) {
                WRITE_WARN_LOG("控制命令执行失败: %s", result.error_message.c_str());
            }
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("控制处理器异常: %s", e.what());
        }
    }
}

// DeviceManager 实现
DeviceManager::DeviceManager() {
}

DeviceManager::~DeviceManager() {
    Shutdown();
}

Result<bool> DeviceManager::Initialize() {
    WRITE_INFO_LOG("设备管理器初始化完成");
    return Result<bool>(true);
}

void DeviceManager::Shutdown() {
    StopAllDevices();

    std::unique_lock<std::shared_mutex> lock(devices_mutex_);
    devices_.clear();

    WRITE_INFO_LOG("设备管理器已关闭");
}

Result<bool> DeviceManager::Start() {
    if (is_running_) {
        return Result<bool>(true);
    }

    is_running_ = true;

    WRITE_INFO_LOG("设备管理器已启动");
    return Result<bool>(true);
}

void DeviceManager::Stop() {
    if (!is_running_) {
        return;
    }

    is_running_ = false;
    StopAllDevices();

    WRITE_INFO_LOG("设备管理器已停止");
}

Result<bool> DeviceManager::AddDevice(const DeviceConfig& config) {
    std::unique_lock<std::shared_mutex> lock(devices_mutex_);

    if (devices_.find(config.device_id) != devices_.end()) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Device already exists");
    }

    auto device = std::make_shared<DeviceInstance>(config);

    auto init_result = device->Initialize();
    if (!init_result.IsSuccess()) {
        return init_result;
    }

    SetupDeviceCallbacks(device);
    devices_[config.device_id] = device;

    WRITE_INFO_LOG("Added device %d (%s)", config.device_id, config.device_name.c_str());
    return Result<bool>(true);
}

Result<bool> DeviceManager::RemoveDevice(int device_id) {
    std::unique_lock<std::shared_mutex> lock(devices_mutex_);

    auto it = devices_.find(device_id);
    if (it == devices_.end()) {
        return Result<bool>(ErrorCode::DEVICE_NOT_FOUND, "Device not found");
    }

    it->second->Shutdown();
    devices_.erase(it);

    WRITE_INFO_LOG("已移除设备 %d", device_id);
    return Result<bool>(true);
}

Result<bool> DeviceManager::UpdateDevice(const DeviceConfig& config) {
    std::shared_lock<std::shared_mutex> lock(devices_mutex_);

    auto it = devices_.find(config.device_id);
    if (it == devices_.end()) {
        return Result<bool>(ErrorCode::DEVICE_NOT_FOUND, "Device not found");
    }

    it->second->SetConfig(config);

    WRITE_INFO_LOG("Updated device %d (%s)", config.device_id, config.device_name.c_str());
    return Result<bool>(true);
}

Result<std::shared_ptr<DeviceInstance>> DeviceManager::GetDevice(int device_id) const {
    std::shared_lock<std::shared_mutex> lock(devices_mutex_);

    auto it = devices_.find(device_id);
    if (it == devices_.end()) {
        return Result<std::shared_ptr<DeviceInstance>>(ErrorCode::DEVICE_NOT_FOUND, "Device not found");
    }

    return Result<std::shared_ptr<DeviceInstance>>(it->second);
}

std::vector<std::shared_ptr<DeviceInstance>> DeviceManager::GetAllDevices() const {
    std::shared_lock<std::shared_mutex> lock(devices_mutex_);

    std::vector<std::shared_ptr<DeviceInstance>> devices;
    devices.reserve(devices_.size());

    for (const auto& pair : devices_) {
        devices.push_back(pair.second);
    }

    return devices;
}

Result<bool> DeviceManager::StartAllDevices() {
    std::shared_lock<std::shared_mutex> lock(devices_mutex_);

    bool all_success = true;
    std::string error_messages;

    for (const auto& pair : devices_) {
        auto result = pair.second->Start();
        if (!result.IsSuccess()) {
            all_success = false;
            if (!error_messages.empty()) {
                error_messages += "; ";
            }
            error_messages += "Device " + std::to_string(pair.first) + ": " + result.error_message;
        }
    }

    if (all_success) {
        return Result<bool>(true);
    } else {
        return Result<bool>(ErrorCode::COMM_ERROR, error_messages);
    }
}

void DeviceManager::StopAllDevices() {
    std::shared_lock<std::shared_mutex> lock(devices_mutex_);

    for (const auto& pair : devices_) {
        pair.second->Stop();
    }
}

Result<bool> DeviceManager::AddDevicesFromConfig(const std::string& config_file) {
    auto parse_result = ParseConfigFile(config_file);
    if (!parse_result.IsSuccess()) {
        return Result<bool>(parse_result.error_code, parse_result.error_message);
    }

    bool all_success = true;
    std::string error_messages;

    for (const auto& config : parse_result.data) {
        auto result = AddDevice(config);
        if (!result.IsSuccess()) {
            all_success = false;
            if (!error_messages.empty()) {
                error_messages += "; ";
            }
            error_messages += "Device " + std::to_string(config.device_id) + ": " + result.error_message;
        }
    }

    if (all_success) {
        return Result<bool>(true);
    } else {
        return Result<bool>(ErrorCode::CONFIG_ERROR, error_messages);
    }
}

std::vector<DeviceStatusInfo> DeviceManager::GetAllDeviceStatus() const {
    std::shared_lock<std::shared_mutex> lock(devices_mutex_);

    std::vector<DeviceStatusInfo> status_list;
    status_list.reserve(devices_.size());

    for (const auto& pair : devices_) {
        status_list.push_back(pair.second->GetStatusInfo());
    }

    return status_list;
}

DeviceStatusInfo DeviceManager::GetDeviceStatus(int device_id) const {
    std::shared_lock<std::shared_mutex> lock(devices_mutex_);

    auto it = devices_.find(device_id);
    if (it != devices_.end()) {
        return it->second->GetStatusInfo();
    }

    return DeviceStatusInfo();  // 返回默认状态
}

int DeviceManager::GetDeviceCount() const {
    std::shared_lock<std::shared_mutex> lock(devices_mutex_);
    return devices_.size();
}

int DeviceManager::GetRunningDeviceCount() const {
    std::shared_lock<std::shared_mutex> lock(devices_mutex_);

    int count = 0;
    for (const auto& pair : devices_) {
        if (pair.second->IsRunning()) {
            count++;
        }
    }

    return count;
}

Result<std::map<int, DataPointValue>> DeviceManager::ReadDataPointFromAllDevices(const TypeIndex& type_idx) {
    std::shared_lock<std::shared_mutex> lock(devices_mutex_);

    std::map<int, DataPointValue> results;

    for (const auto& pair : devices_) {
        auto result = pair.second->ReadDataPoint(type_idx);
        if (result.IsSuccess()) {
            results[pair.first] = result.data;
        }
    }

    return Result<std::map<int, DataPointValue>>(results);
}

Result<bool> DeviceManager::WriteDataPointToDevice(int device_id, const TypeIndex& type_idx, double value) {
    auto device_result = GetDevice(device_id);
    if (!device_result.IsSuccess()) {
        return Result<bool>(device_result.error_code, device_result.error_message);
    }

    return device_result.data->WriteDataPoint(type_idx, value);
}

void DeviceManager::SetGlobalStatusChangeCallback(GlobalStatusChangeCallback callback) {
    std::lock_guard<std::mutex> lock(global_callback_mutex_);
    global_status_callback_ = callback;
}

void DeviceManager::SetGlobalDataChangeCallback(GlobalDataChangeCallback callback) {
    std::lock_guard<std::mutex> lock(global_callback_mutex_);
    global_data_callback_ = callback;
}

void DeviceManager::SetGlobalAlarmCallback(GlobalAlarmCallback callback) {
    std::lock_guard<std::mutex> lock(global_callback_mutex_);
    global_alarm_callback_ = callback;
}

DeviceManager::ManagerStatistics DeviceManager::GetStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    ManagerStatistics stats = stats_;

    // 更新实时统计
    std::shared_lock<std::shared_mutex> devices_lock(devices_mutex_);

    stats.total_devices = devices_.size();
    stats.running_devices = 0;
    stats.connected_devices = 0;
    stats.error_devices = 0;
    stats.total_data_points = 0;
    stats.total_scan_count = 0;
    stats.total_success_count = 0;
    stats.total_error_count = 0;

    for (const auto& pair : devices_) {
        auto device_status = pair.second->GetStatusInfo();

        if (pair.second->IsRunning()) {
            stats.running_devices++;
        }

        if (device_status.status == DeviceStatus::CONNECTED) {
            stats.connected_devices++;
        } else if (device_status.status == DeviceStatus::ERROR) {
            stats.error_devices++;
        }

        stats.total_scan_count += device_status.scan_count;
        stats.total_success_count += device_status.success_count;
        stats.total_error_count += device_status.error_count;

        // 统计数据点数量
        if (pair.second->GetDataPointManager()) {
            auto point_stats = pair.second->GetDataPointManager()->GetStatistics();
            stats.total_data_points += point_stats.total_points;
        }
    }

    if (stats.total_scan_count > 0) {
        stats.overall_success_rate = (double)stats.total_success_count / stats.total_scan_count * 100.0;
    }

    stats.last_update_time = utils::TimeUtils::GetCurrentTimestamp();

    return stats;
}

void DeviceManager::ResetStatistics() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_ = ManagerStatistics();

    // 重置所有设备的统计信息
    std::shared_lock<std::shared_mutex> devices_lock(devices_mutex_);
    for (const auto& pair : devices_) {
        if (pair.second->GetDataPointManager()) {
            pair.second->GetDataPointManager()->ResetStatistics();
        }
    }
}

// 内部方法实现
void DeviceManager::SetupDeviceCallbacks(std::shared_ptr<DeviceInstance> device) {
    device->SetStatusChangeCallback([this](int device_id, DeviceStatus old_status, DeviceStatus new_status) {
        HandleDeviceStatusChange(device_id, old_status, new_status);
    });

    device->SetDataChangeCallback([this](int device_id, const DataPointValue& value) {
        HandleDeviceDataChange(device_id, value);
    });

    device->SetAlarmCallback([this](int device_id, const std::string& alarm_message) {
        HandleDeviceAlarm(device_id, alarm_message);
    });
}

void DeviceManager::HandleDeviceStatusChange(int device_id, DeviceStatus old_status, DeviceStatus new_status) {
    if (global_status_callback_) {
        std::lock_guard<std::mutex> lock(global_callback_mutex_);
        try {
            global_status_callback_(device_id, old_status, new_status);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("Global status callback exception: %s", e.what());
        }
    }
}

void DeviceManager::HandleDeviceDataChange(int device_id, const DataPointValue& value) {
    if (global_data_callback_) {
        std::lock_guard<std::mutex> lock(global_callback_mutex_);
        try {
            global_data_callback_(device_id, value);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("Global data callback exception: %s", e.what());
        }
    }
}

void DeviceManager::HandleDeviceAlarm(int device_id, const std::string& alarm_message) {
    if (global_alarm_callback_) {
        std::lock_guard<std::mutex> lock(global_callback_mutex_);
        try {
            global_alarm_callback_(device_id, alarm_message);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("Global alarm callback exception: %s", e.what());
        }
    }
}

Result<std::vector<DeviceConfig>> DeviceManager::ParseConfigFile(const std::string& config_file) {
    // 简化实现，实际应该解析 JSON 或 XML 配置文件
    std::vector<DeviceConfig> configs;

    // 示例配置
    DeviceConfig config1(1, "Device 1");
    config1.device_type = "RTU Device";
    config1.comm_type = CommType::RTU;
    config1.comm_config = R"({"device":"/dev/ttyS1","baud":9600,"parity":"N"})";
    config1.point_table_file = "device1_points.json";
    configs.push_back(config1);

    return Result<std::vector<DeviceConfig>>(configs);
}

// GlobalDeviceManager 实现
DeviceManager& GlobalDeviceManager::GetInstance() {
    std::lock_guard<std::mutex> lock(instance_mutex_);

    if (!instance_) {
        instance_ = std::make_unique<DeviceManager>();
    }

    return *instance_;
}

bool GlobalDeviceManager::Initialize() {
    std::lock_guard<std::mutex> lock(instance_mutex_);

    if (!instance_) {
        instance_ = std::make_unique<DeviceManager>();
    }

    return instance_->Initialize().IsSuccess();
}

void GlobalDeviceManager::Shutdown() {
    std::lock_guard<std::mutex> lock(instance_mutex_);

    if (instance_) {
        instance_->Shutdown();
        instance_.reset();
    }
}

} // namespace modbus
