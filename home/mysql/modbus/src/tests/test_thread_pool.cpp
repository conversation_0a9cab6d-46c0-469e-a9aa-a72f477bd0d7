#include "../utils/thread_pool.h"
#include "../utils/logger.h"
#include <iostream>
#include <vector>
#include <chrono>
#include <atomic>

using namespace modbus;

int main() {
    std::cout << "Testing ThreadPool..." << std::endl;
    
    // 初始化日志系统
    Logger::GetInstance().Initialize(LogLevel::INFO, LogTarget::CONSOLE);
    
    // 创建线程池
    ThreadPool pool(4);
    
    std::cout << "Thread pool created with " << pool.GetThreadCount() << " threads" << std::endl;
    
    // 测试基本任务提交
    std::vector<std::future<int>> results;
    
    for (int i = 0; i < 10; ++i) {
        auto result = pool.Submit([i]() {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            WRITE_INFO_LOG("Task %d completed", i);
            return i * i;
        });
        results.push_back(std::move(result));
    }
    
    // 等待所有任务完成并获取结果
    std::cout << "Task results: ";
    for (auto& result : results) {
        std::cout << result.get() << " ";
    }
    std::cout << std::endl;
    
    // 测试定时器
    std::cout << "Testing Timer..." << std::endl;
    
    Timer timer;
    std::atomic<int> counter(0);
    
    timer.Start(500, [&counter]() {
        counter++;
        WRITE_INFO_LOG("Timer tick: %d", counter.load());
    }, true);
    
    // 运行 3 秒
    std::this_thread::sleep_for(std::chrono::seconds(3));
    timer.Stop();
    
    std::cout << "Timer ticked " << counter.load() << " times" << std::endl;
    
    // 测试事件
    std::cout << "Testing Event..." << std::endl;
    
    Event event;
    bool event_received = false;
    
    std::thread waiter([&event, &event_received]() {
        WRITE_INFO_LOG("Waiting for event...");
        event.Wait();
        event_received = true;
        WRITE_INFO_LOG("Event received!");
    });
    
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    event.Set();
    
    waiter.join();
    
    if (event_received) {
        std::cout << "Event test passed" << std::endl;
    } else {
        std::cout << "Event test failed" << std::endl;
    }
    
    // 测试信号量
    std::cout << "Testing Semaphore..." << std::endl;
    
    Semaphore semaphore(2);  // 允许 2 个并发访问
    std::atomic<int> active_count(0);
    std::atomic<int> max_active(0);
    
    std::vector<std::thread> workers;
    for (int i = 0; i < 5; ++i) {
        workers.emplace_back([&semaphore, &active_count, &max_active, i]() {
            semaphore.Wait();
            
            int current = ++active_count;
            int expected = max_active.load();
            while (current > expected && !max_active.compare_exchange_weak(expected, current)) {
                expected = max_active.load();
            }
            
            WRITE_INFO_LOG("Worker %d acquired semaphore (active: %d)", i, current);
            
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
            
            --active_count;
            WRITE_INFO_LOG("Worker %d released semaphore", i);
            
            semaphore.Signal();
        });
    }
    
    for (auto& worker : workers) {
        worker.join();
    }
    
    std::cout << "Maximum concurrent access: " << max_active.load() << " (should be <= 2)" << std::endl;
    
    std::cout << "ThreadPool test completed" << std::endl;
    
    Logger::GetInstance().Shutdown();
    
    return 0;
}
