#include "../utils/logger.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace modbus;

int main() {
    std::cout << "Testing Logger..." << std::endl;
    
    // 初始化日志系统
    Logger::GetInstance().Initialize(LogLevel::DEBUG, LogTarget::BOTH, "test_logger.log");
    
    // 测试不同级别的日志
    LOG_TRACE("This is a trace message");
    LOG_DEBUG("This is a debug message");
    LOG_INFO("This is an info message");
    LOG_WARN("This is a warning message");
    LOG_ERROR("This is an error message");
    
    // 测试格式化日志
    WRITE_INFO_LOG("Formatted message: %s, number: %d, float: %.2f", "test", 42, 3.14);
    
    // 测试多线程日志
    std::vector<std::thread> threads;
    for (int i = 0; i < 5; ++i) {
        threads.emplace_back([i]() {
            for (int j = 0; j < 10; ++j) {
                WRITE_INFO_LOG("Thread %d, message %d", i, j);
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        });
    }
    
    for (auto& t : threads) {
        t.join();
    }
    
    // 刷新日志
    Logger::GetInstance().Flush();
    
    std::cout << "Logger test completed. Check test_logger.log for output." << std::endl;
    
    // 关闭日志系统
    Logger::GetInstance().Shutdown();
    
    return 0;
}
