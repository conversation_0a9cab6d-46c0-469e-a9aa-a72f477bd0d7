#include "../comm/modbus_tcp_comm.h"
#include "../utils/logger.h"
#include <iostream>
#include <cassert>

using namespace modbus;

void TestTcpCommCreation() {
    std::cout << "Testing TCP communication creation..." << std::endl;
    
    TcpCommParam param;
    param.ip = "127.0.0.1";
    param.port = 502;
    param.timeout_ms = 1000;
    
    ModbusTcpComm tcp_comm(param, 1);
    
    // 测试基本属性
    assert(tcp_comm.GetSlaveId() == 1);
    assert(tcp_comm.GetTimeout() == 1000);
    assert(!tcp_comm.IsConnected());
    
    std::cout << "TCP communication creation test passed!" << std::endl;
}

void TestTcpCommInitialization() {
    std::cout << "Testing TCP communication initialization..." << std::endl;
    
    TcpCommParam param;
    param.ip = "127.0.0.1";
    param.port = 502;
    param.timeout_ms = 1000;
    
    ModbusTcpComm tcp_comm(param, 1);
    
    // 测试初始化
    auto init_result = tcp_comm.Initialize();
    std::cout << "Initialization result: " << (init_result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    if (!init_result.IsSuccess()) {
        std::cout << "Error: " << init_result.error_message << std::endl;
    }
    
    std::cout << "TCP communication initialization test completed!" << std::endl;
}

void TestSlaveIdOperations() {
    std::cout << "Testing slave ID operations..." << std::endl;
    
    TcpCommParam param;
    param.ip = "127.0.0.1";
    param.port = 502;
    param.timeout_ms = 1000;
    
    ModbusTcpComm tcp_comm(param, 1);
    tcp_comm.Initialize();
    
    // 测试有效的从站地址
    auto result1 = tcp_comm.SetSlaveId(1);
    assert(result1.IsSuccess());
    assert(tcp_comm.GetSlaveId() == 1);
    
    auto result2 = tcp_comm.SetSlaveId(247);
    assert(result2.IsSuccess());
    assert(tcp_comm.GetSlaveId() == 247);
    
    // 测试无效的从站地址
    auto result3 = tcp_comm.SetSlaveId(0);
    assert(!result3.IsSuccess());
    
    auto result4 = tcp_comm.SetSlaveId(248);
    assert(!result4.IsSuccess());
    
    std::cout << "Slave ID operations test passed!" << std::endl;
}

void TestTimeoutOperations() {
    std::cout << "Testing timeout operations..." << std::endl;
    
    TcpCommParam param;
    param.ip = "127.0.0.1";
    param.port = 502;
    param.timeout_ms = 1000;
    
    ModbusTcpComm tcp_comm(param, 1);
    tcp_comm.Initialize();
    
    // 测试超时设置
    assert(tcp_comm.GetTimeout() == 1000);
    
    tcp_comm.SetTimeout(2000);
    assert(tcp_comm.GetTimeout() == 2000);
    
    tcp_comm.SetTimeout(500);
    assert(tcp_comm.GetTimeout() == 500);
    
    std::cout << "Timeout operations test passed!" << std::endl;
}

void TestCommStats() {
    std::cout << "Testing communication statistics..." << std::endl;
    
    TcpCommParam param;
    param.ip = "127.0.0.1";
    param.port = 502;
    param.timeout_ms = 1000;
    
    ModbusTcpComm tcp_comm(param, 1);
    tcp_comm.Initialize();
    
    // 初始统计应该为零
    int success, error, timeout;
    tcp_comm.GetCommStats(success, error, timeout);
    assert(success == 0);
    assert(error == 0);
    assert(timeout == 0);
    
    // 重置统计
    tcp_comm.ResetCommStats();
    tcp_comm.GetCommStats(success, error, timeout);
    assert(success == 0);
    assert(error == 0);
    assert(timeout == 0);
    
    std::cout << "Communication statistics test passed!" << std::endl;
}

void TestConnectionStatusCallback() {
    std::cout << "Testing connection status callback..." << std::endl;
    
    TcpCommParam param;
    param.ip = "127.0.0.1";
    param.port = 502;
    param.timeout_ms = 1000;
    
    ModbusTcpComm tcp_comm(param, 1);
    
    bool callback_called = false;
    DeviceStatus last_status = DeviceStatus::DISCONNECTED;
    
    tcp_comm.SetConnectionStatusCallback([&](int device_id, DeviceStatus status) {
        callback_called = true;
        last_status = status;
        std::cout << "Status callback: Device " << device_id << " -> " << static_cast<int>(status) << std::endl;
    });
    
    tcp_comm.Initialize();
    
    // 尝试连接（预期会失败，但应该触发回调）
    auto connect_result = tcp_comm.Connect();
    
    std::cout << "Connection status callback test completed!" << std::endl;
}

void TestMultipleConnections() {
    std::cout << "Testing multiple connections..." << std::endl;
    
    TcpCommParam param;
    param.ip = "127.0.0.1";
    param.port = 502;
    param.timeout_ms = 1000;
    
    ModbusTcpComm tcp_comm(param, 1);
    tcp_comm.Initialize();
    
    // 测试添加连接
    auto add_result1 = tcp_comm.AddConnection("*************", 502);
    std::cout << "Add connection 1: " << (add_result1.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    
    auto add_result2 = tcp_comm.AddConnection("*************", 502);
    std::cout << "Add connection 2: " << (add_result2.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    
    // 测试重复添加
    auto add_result3 = tcp_comm.AddConnection("*************", 502);
    assert(!add_result3.IsSuccess()); // 应该失败
    
    // 测试切换连接
    auto switch_result = tcp_comm.SwitchConnection("*************", 502);
    std::cout << "Switch connection: " << (switch_result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    
    // 测试移除连接
    auto remove_result = tcp_comm.RemoveConnection("*************", 502);
    std::cout << "Remove connection: " << (remove_result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    
    // 测试移除不存在的连接
    auto remove_result2 = tcp_comm.RemoveConnection("*************", 502);
    assert(!remove_result2.IsSuccess()); // 应该失败
    
    std::cout << "Multiple connections test completed!" << std::endl;
}

void TestParameterValidation() {
    std::cout << "Testing parameter validation..." << std::endl;
    
    // 测试无效的端口
    TcpCommParam param1;
    param1.ip = "127.0.0.1";
    param1.port = -1;  // 无效端口
    
    ModbusTcpComm tcp_comm1(param1, 1);
    auto init_result1 = tcp_comm1.Initialize();
    // 应该失败或者在连接时失败
    
    // 测试空IP地址
    TcpCommParam param2;
    param2.ip = "";  // 空IP地址
    param2.port = 502;
    
    ModbusTcpComm tcp_comm2(param2, 1);
    auto init_result2 = tcp_comm2.Initialize();
    // 应该失败
    
    std::cout << "Parameter validation test completed!" << std::endl;
}

void TestConnectionInfo() {
    std::cout << "Testing connection info..." << std::endl;
    
    TcpCommParam param;
    param.ip = "127.0.0.1";
    param.port = 502;
    param.timeout_ms = 1000;
    
    ModbusTcpComm tcp_comm(param, 1);
    tcp_comm.Initialize();
    
    // 获取连接信息
    std::string conn_info = tcp_comm.GetConnectionInfo();
    std::cout << "Connection info: " << conn_info << std::endl;
    
    // 测试连接池信息
    std::cout << "Max connections: " << tcp_comm.GetMaxConnections() << std::endl;
    std::cout << "Active connections: " << tcp_comm.GetActiveConnectionCount() << std::endl;
    
    auto active_connections = tcp_comm.GetActiveConnections();
    std::cout << "Active connection list size: " << active_connections.size() << std::endl;
    
    std::cout << "Connection info test completed!" << std::endl;
}

int main() {
    std::cout << "Modbus TCP Communication Test Suite" << std::endl;
    std::cout << "====================================" << std::endl;
    
    // 初始化日志系统
    Logger::GetInstance().Initialize(LogLevel::INFO, LogTarget::CONSOLE);
    
    try {
        TestTcpCommCreation();
        TestTcpCommInitialization();
        TestSlaveIdOperations();
        TestTimeoutOperations();
        TestCommStats();
        TestConnectionStatusCallback();
        TestMultipleConnections();
        TestParameterValidation();
        TestConnectionInfo();
        
        std::cout << std::endl << "All TCP communication tests completed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        Logger::GetInstance().Shutdown();
        return 1;
    }
    
    Logger::GetInstance().Shutdown();
    return 0;
}
