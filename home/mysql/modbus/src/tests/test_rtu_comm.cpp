#include "../comm/modbus_rtu_comm.h"
#include "../utils/logger.h"
#include <iostream>
#include <cassert>

using namespace modbus;

void TestRtuCommCreation() {
    std::cout << "Testing RTU communication creation..." << std::endl;
    
    RtuCommParam param;
    param.device = "/dev/ttyUSB0";
    param.baud = 9600;
    param.parity = 'N';
    param.data_bit = 8;
    param.stop_bit = 1;
    param.timeout_ms = 1000;
    
    ModbusRtuComm rtu_comm(param, 1);
    
    // 测试基本属性
    assert(rtu_comm.GetSlaveId() == 1);
    assert(rtu_comm.GetTimeout() == 1000);
    assert(!rtu_comm.IsConnected());
    
    std::cout << "RTU communication creation test passed!" << std::endl;
}

void TestRtuCommInitialization() {
    std::cout << "Testing RTU communication initialization..." << std::endl;
    
    RtuCommParam param;
    param.device = "/dev/null";  // 使用 /dev/null 避免实际串口依赖
    param.baud = 9600;
    param.parity = 'N';
    param.data_bit = 8;
    param.stop_bit = 1;
    param.timeout_ms = 1000;
    
    ModbusRtuComm rtu_comm(param, 1);
    
    // 测试初始化
    auto init_result = rtu_comm.Initialize();
    // 注意：使用 /dev/null 可能会初始化成功，但连接会失败
    
    std::cout << "Initialization result: " << (init_result.IsSuccess() ? "SUCCESS" : "FAILED") << std::endl;
    if (!init_result.IsSuccess()) {
        std::cout << "Error: " << init_result.error_message << std::endl;
    }
    
    std::cout << "RTU communication initialization test completed!" << std::endl;
}

void TestSlaveIdOperations() {
    std::cout << "Testing slave ID operations..." << std::endl;
    
    RtuCommParam param;
    param.device = "/dev/null";
    param.baud = 9600;
    param.parity = 'N';
    param.data_bit = 8;
    param.stop_bit = 1;
    param.timeout_ms = 1000;
    
    ModbusRtuComm rtu_comm(param, 1);
    rtu_comm.Initialize();
    
    // 测试有效的从站地址
    auto result1 = rtu_comm.SetSlaveId(1);
    assert(result1.IsSuccess());
    assert(rtu_comm.GetSlaveId() == 1);
    
    auto result2 = rtu_comm.SetSlaveId(247);
    assert(result2.IsSuccess());
    assert(rtu_comm.GetSlaveId() == 247);
    
    // 测试无效的从站地址
    auto result3 = rtu_comm.SetSlaveId(0);
    assert(!result3.IsSuccess());
    
    auto result4 = rtu_comm.SetSlaveId(248);
    assert(!result4.IsSuccess());
    
    std::cout << "Slave ID operations test passed!" << std::endl;
}

void TestTimeoutOperations() {
    std::cout << "Testing timeout operations..." << std::endl;
    
    RtuCommParam param;
    param.device = "/dev/null";
    param.baud = 9600;
    param.parity = 'N';
    param.data_bit = 8;
    param.stop_bit = 1;
    param.timeout_ms = 1000;
    
    ModbusRtuComm rtu_comm(param, 1);
    rtu_comm.Initialize();
    
    // 测试超时设置
    assert(rtu_comm.GetTimeout() == 1000);
    
    rtu_comm.SetTimeout(2000);
    assert(rtu_comm.GetTimeout() == 2000);
    
    rtu_comm.SetTimeout(500);
    assert(rtu_comm.GetTimeout() == 500);
    
    std::cout << "Timeout operations test passed!" << std::endl;
}

void TestCommStats() {
    std::cout << "Testing communication statistics..." << std::endl;
    
    RtuCommParam param;
    param.device = "/dev/null";
    param.baud = 9600;
    param.parity = 'N';
    param.data_bit = 8;
    param.stop_bit = 1;
    param.timeout_ms = 1000;
    
    ModbusRtuComm rtu_comm(param, 1);
    rtu_comm.Initialize();
    
    // 初始统计应该为零
    int success, error, timeout;
    rtu_comm.GetCommStats(success, error, timeout);
    assert(success == 0);
    assert(error == 0);
    assert(timeout == 0);
    
    // 重置统计
    rtu_comm.ResetCommStats();
    rtu_comm.GetCommStats(success, error, timeout);
    assert(success == 0);
    assert(error == 0);
    assert(timeout == 0);
    
    std::cout << "Communication statistics test passed!" << std::endl;
}

void TestConnectionStatusCallback() {
    std::cout << "Testing connection status callback..." << std::endl;
    
    RtuCommParam param;
    param.device = "/dev/null";
    param.baud = 9600;
    param.parity = 'N';
    param.data_bit = 8;
    param.stop_bit = 1;
    param.timeout_ms = 1000;
    
    ModbusRtuComm rtu_comm(param, 1);
    
    bool callback_called = false;
    DeviceStatus last_status = DeviceStatus::DISCONNECTED;
    
    rtu_comm.SetConnectionStatusCallback([&](int device_id, DeviceStatus status) {
        callback_called = true;
        last_status = status;
        std::cout << "Status callback: Device " << device_id << " -> " << static_cast<int>(status) << std::endl;
    });
    
    rtu_comm.Initialize();
    
    // 尝试连接（预期会失败，但应该触发回调）
    auto connect_result = rtu_comm.Connect();
    
    std::cout << "Connection status callback test completed!" << std::endl;
}

void TestParameterValidation() {
    std::cout << "Testing parameter validation..." << std::endl;
    
    // 测试无效的波特率
    RtuCommParam param1;
    param1.device = "/dev/null";
    param1.baud = -1;  // 无效波特率
    
    ModbusRtuComm rtu_comm1(param1, 1);
    auto init_result1 = rtu_comm1.Initialize();
    // 应该失败或者在连接时失败
    
    // 测试空设备路径
    RtuCommParam param2;
    param2.device = "";  // 空设备路径
    param2.baud = 9600;
    
    ModbusRtuComm rtu_comm2(param2, 1);
    auto init_result2 = rtu_comm2.Initialize();
    // 应该失败
    
    std::cout << "Parameter validation test completed!" << std::endl;
}

int main() {
    std::cout << "Modbus RTU Communication Test Suite" << std::endl;
    std::cout << "====================================" << std::endl;
    
    // 初始化日志系统
    Logger::GetInstance().Initialize(LogLevel::INFO, LogTarget::CONSOLE);
    
    try {
        TestRtuCommCreation();
        TestRtuCommInitialization();
        TestSlaveIdOperations();
        TestTimeoutOperations();
        TestCommStats();
        TestConnectionStatusCallback();
        TestParameterValidation();
        
        std::cout << std::endl << "All RTU communication tests completed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        Logger::GetInstance().Shutdown();
        return 1;
    }
    
    Logger::GetInstance().Shutdown();
    return 0;
}
