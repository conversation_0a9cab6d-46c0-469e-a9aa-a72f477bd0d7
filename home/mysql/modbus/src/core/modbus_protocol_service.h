#ifndef MODBUS_PROTOCOL_SERVICE_H
#define MODBUS_PROTOCOL_SERVICE_H

#include "../types/modbus_types.h"
#include "../device/modbus_device.h"
#include "../device/device_manager.h"
#include "../redis/redis_manager.h"
#include "../config/config_manager.h"
#include "../utils/thread_pool.h"
#include "../utils/logger.h"
#include <memory>
#include <map>
#include <atomic>
#include <functional>

namespace modbus {

// 协议服务状态
enum class ProtocolServiceStatus {
    STOPPED = 0,
    STARTING = 1,
    RUNNING = 2,
    STOPPING = 3,
    ERROR = 4
};

// 协议服务配置
struct ProtocolServiceConfig {
    std::string service_name = "ModbusProtocolService";
    std::string version = "1.0.0";
    
    // 日志配置
    LogLevel log_level = LogLevel::INFO;
    LogTarget log_target = LogTarget::CONSOLE;
    std::string log_file = "";
    
    // Redis 配置
    bool enable_redis = false;
    RedisManagerConfig redis_config;
    
    // 设备配置
    std::string device_config_file = "";
    bool auto_start_devices = true;
    
    // 监控配置
    bool enable_monitoring = true;
    uint32_t monitoring_interval_ms = 5000;
    
    // 健康检查配置
    bool enable_health_check = true;
    uint32_t health_check_interval_ms = 10000;
    
    ProtocolServiceConfig() = default;
};

// 协议服务统计信息
struct ProtocolServiceStatistics {
    uint64_t uptime_seconds = 0;
    uint32_t total_devices = 0;
    uint32_t running_devices = 0;
    uint32_t connected_devices = 0;
    uint32_t error_devices = 0;
    uint64_t total_scan_count = 0;
    uint64_t total_success_count = 0;
    double overall_success_rate = 0.0;
    bool redis_connected = false;
    uint64_t redis_publish_count = 0;
    uint64_t redis_subscribe_count = 0;
};

// 协议服务回调函数类型
using ProtocolServiceStatusCallback = std::function<void(ProtocolServiceStatus old_status, ProtocolServiceStatus new_status)>;
using ProtocolServiceErrorCallback = std::function<void(const std::string& error_message)>;

// Modbus 协议服务主类
class ModbusProtocolService {
public:
    explicit ModbusProtocolService(const ProtocolServiceConfig& config = ProtocolServiceConfig());
    virtual ~ModbusProtocolService();
    
    // 基本操作
    Result<bool> Initialize();
    Result<bool> Start();
    Result<bool> Stop();
    Result<bool> Restart();
    void Shutdown();
    
    // 状态查询
    ProtocolServiceStatus GetStatus() const { return status_; }
    bool IsRunning() const { return status_ == ProtocolServiceStatus::RUNNING; }
    std::string GetStatusString() const;
    
    // 配置管理
    const ProtocolServiceConfig& GetConfig() const { return config_; }
    Result<bool> UpdateConfig(const ProtocolServiceConfig& new_config);
    Result<bool> LoadConfigFromFile(const std::string& config_file);
    Result<bool> SaveConfigToFile(const std::string& config_file);
    
    // 设备管理
    Result<bool> AddDevice(const DeviceConfig& device_config);
    Result<bool> RemoveDevice(int device_id);
    Result<bool> StartDevice(int device_id);
    Result<bool> StopDevice(int device_id);
    Result<bool> RestartDevice(int device_id);
    
    // 设备查询
    std::vector<int> GetDeviceIds() const;
    Result<DeviceConfig> GetDeviceConfig(int device_id);
    Result<DeviceStatus> GetDeviceStatus(int device_id);
    Result<DeviceStatistics> GetDeviceStatistics(int device_id);
    
    // 数据操作
    Result<DataPointValue> ReadDataPoint(int device_id, const TypeIndex& type_idx);
    Result<bool> WriteDataPoint(int device_id, const TypeIndex& type_idx, double value);
    Result<std::vector<DataPointValue>> ReadDeviceData(int device_id);
    
    // Redis 操作
    Result<bool> PublishData(int device_id, const TypeIndex& type_idx, double value);
    Result<bool> PublishDeviceStatus(int device_id, DeviceStatus status);
    Result<bool> PublishAlarm(int device_id, const std::string& alarm_message);
    
    // 统计信息
    ProtocolServiceStatistics GetStatistics() const;
    void ResetStatistics();
    
    // 回调设置
    void SetStatusCallback(ProtocolServiceStatusCallback callback) { status_callback_ = callback; }
    void SetErrorCallback(ProtocolServiceErrorCallback callback) { error_callback_ = callback; }
    
    // 健康检查
    bool PerformHealthCheck();
    
protected:
    // 内部方法
    Result<bool> InitializeLogging();
    Result<bool> InitializeRedis();
    Result<bool> InitializeDevices();
    void SetupCallbacks();
    
    // 监控线程
    void StartMonitoring();
    void StopMonitoring();
    void MonitoringThread();
    void UpdateStatistics();
    
    // 健康检查线程
    void StartHealthCheck();
    void StopHealthCheck();
    void HealthCheckThread();
    
    // 状态管理
    void SetStatus(ProtocolServiceStatus new_status);
    void HandleError(const std::string& error_message);
    
    // 回调处理
    void HandleDeviceStatusChange(int device_id, DeviceStatus old_status, DeviceStatus new_status);
    void HandleDeviceDataChange(int device_id, const DataPointValue& value);
    void HandleDeviceEvent(int device_id, const DeviceEvent& event);
    
protected:
    ProtocolServiceConfig config_;
    std::atomic<ProtocolServiceStatus> status_;
    uint64_t start_time_;
    
    // 核心组件
    std::unique_ptr<DeviceManager> device_manager_;
    std::unique_ptr<RedisManager> redis_manager_;
    std::unique_ptr<ConfigManager> config_manager_;
    
    // 线程管理
    std::unique_ptr<std::thread> monitoring_thread_;
    std::unique_ptr<std::thread> health_check_thread_;
    std::atomic<bool> monitoring_running_;
    std::atomic<bool> health_check_running_;
    Event stop_event_;
    
    // 统计信息
    mutable ProtocolServiceStatistics statistics_;
    mutable MutexLock stats_mutex_;
    
    // 回调函数
    ProtocolServiceStatusCallback status_callback_;
    ProtocolServiceErrorCallback error_callback_;
    mutable MutexLock callback_mutex_;
};

// 协议服务工厂类
class ProtocolServiceFactory {
public:
    static std::unique_ptr<ModbusProtocolService> CreateService(const ProtocolServiceConfig& config);
    static std::unique_ptr<ModbusProtocolService> CreateServiceFromConfig(const std::string& config_file);
    
    // 预定义配置
    static ProtocolServiceConfig GetDefaultConfig();
    static ProtocolServiceConfig GetProductionConfig();
    static ProtocolServiceConfig GetDevelopmentConfig();
    static ProtocolServiceConfig GetTestConfig();
};

// 全局协议服务单例
class GlobalProtocolService {
public:
    static ModbusProtocolService& GetInstance();
    static bool Initialize(const ProtocolServiceConfig& config);
    static void Shutdown();
    
private:
    static std::unique_ptr<ModbusProtocolService> instance_;
    static MutexLock instance_mutex_;
};

// 协议服务启动器
class ProtocolServiceLauncher {
public:
    static int Run(int argc, char* argv[]);
    
private:
    static void PrintUsage();
    static void PrintVersion();
    static void InstallSignalHandlers();
    static void SignalHandler(int signal);
    
    static std::atomic<bool> shutdown_signal_received_;
};

// 便利宏定义
#define PROTOCOL_SERVICE() GlobalProtocolService::GetInstance()

} // namespace modbus

#endif // MODBUS_PROTOCOL_SERVICE_H
