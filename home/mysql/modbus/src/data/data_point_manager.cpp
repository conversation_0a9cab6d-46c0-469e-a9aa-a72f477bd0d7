#include "data_point_manager.h"
#include "../utils/utils.h"
#include "../config/point_table_loader.h"
#include "../config/modbus_point_parser.h"
#include <fstream>
#include <iostream>

namespace modbus {

// DataPointManager 实现
DataPointManager::DataPointManager(std::shared_ptr<ModbusCommInterface> comm)
    : comm_(comm)
    , stop_event_(true) {  // manual reset event
}

DataPointManager::~DataPointManager() {
    Shutdown();
}

Result<bool> DataPointManager::Initialize() {
    if (!comm_) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Communication interface is null");
    }
    
    WRITE_INFO_LOG("数据点管理器初始化完成");
    return Result<bool>(true);
}

void DataPointManager::Shutdown() {
    Stop();
    
    WriteLock lock(points_lock_);
    data_points_.clear();
    
    WriteLock cache_lock(cache_lock_);
    cached_values_.clear();
    
    WRITE_INFO_LOG("数据点管理器已关闭");
}

Result<bool> DataPointManager::Start() {
    if (is_running_) {
        return Result<bool>(true);
    }
    
    if (!comm_) {
        return Result<bool>(ErrorCode::NOT_INITIALIZED, "Communication interface not set");
    }
    
    is_running_ = true;
    stop_event_.Reset();
    
    if (auto_scan_enabled_) {
        scan_thread_ = std::make_unique<std::thread>(&DataPointManager::ScanThread, this);
    }
    
    WRITE_INFO_LOG("数据点管理器已启动");
    return Result<bool>(true);
}

void DataPointManager::Stop() {
    if (!is_running_) {
        return;
    }
    
    is_running_ = false;
    stop_event_.Set();
    
    if (scan_thread_ && scan_thread_->joinable()) {
        scan_thread_->join();
    }
    scan_thread_.reset();
    
    WRITE_INFO_LOG("数据点管理器已停止");
}

Result<bool> DataPointManager::AddDataPoint(const DataPointEx& point) {
    std::string key = GetPointKey(point.type_idx);
    
    WriteLock lock(points_lock_);
    data_points_[key] = point;
    
    WRITE_DEBUG_LOG("已添加数据点: %s", key.c_str());
    return Result<bool>(true);
}

Result<bool> DataPointManager::RemoveDataPoint(const TypeIndex& type_idx) {
    std::string key = GetPointKey(type_idx);
    
    WriteLock lock(points_lock_);
    auto it = data_points_.find(key);
    if (it == data_points_.end()) {
        return Result<bool>(ErrorCode::DEVICE_NOT_FOUND, "Data point not found");
    }
    
    data_points_.erase(it);
    
    // 同时清除缓存
    WriteLock cache_lock(cache_lock_);
    cached_values_.erase(key);
    
    WRITE_DEBUG_LOG("已移除数据点: %s", key.c_str());
    return Result<bool>(true);
}

Result<bool> DataPointManager::UpdateDataPoint(const DataPointEx& point) {
    std::string key = GetPointKey(point.type_idx);
    
    WriteLock lock(points_lock_);
    auto it = data_points_.find(key);
    if (it == data_points_.end()) {
        return Result<bool>(ErrorCode::DEVICE_NOT_FOUND, "Data point not found");
    }
    
    it->second = point;
    
    WRITE_DEBUG_LOG("Updated data point: %s", key.c_str());
    return Result<bool>(true);
}

Result<DataPointEx> DataPointManager::GetDataPoint(const TypeIndex& type_idx) const {
    std::string key = GetPointKey(type_idx);
    
    ReadLock lock(points_lock_);
    auto it = data_points_.find(key);
    if (it == data_points_.end()) {
        return Result<DataPointEx>(ErrorCode::DEVICE_NOT_FOUND, "Data point not found");
    }
    
    return Result<DataPointEx>(it->second);
}

std::vector<DataPointEx> DataPointManager::GetAllDataPoints() const {
    ReadLock lock(points_lock_);
    
    std::vector<DataPointEx> points;
    points.reserve(data_points_.size());
    
    for (const auto& pair : data_points_) {
        points.push_back(pair.second);
    }
    
    return points;
}

std::vector<DataPointEx> DataPointManager::GetDataPointsByType(DataType data_type) const {
    ReadLock lock(points_lock_);
    
    std::vector<DataPointEx> points;
    
    for (const auto& pair : data_points_) {
        if (pair.second.type_idx.data_type == data_type) {
            points.push_back(pair.second);
        }
    }
    
    return points;
}

std::vector<DataPointEx> DataPointManager::GetReadablePoints() const {
    ReadLock lock(points_lock_);
    
    std::vector<DataPointEx> points;
    
    for (const auto& pair : data_points_) {
        if (IsReadablePoint(pair.second.type_idx.data_type)) {
            points.push_back(pair.second);
        }
    }
    
    return points;
}

std::vector<DataPointEx> DataPointManager::GetWritablePoints() const {
    ReadLock lock(points_lock_);
    
    std::vector<DataPointEx> points;
    
    for (const auto& pair : data_points_) {
        if (IsWritablePoint(pair.second.type_idx.data_type)) {
            points.push_back(pair.second);
        }
    }
    
    return points;
}

Result<DataPointValue> DataPointManager::ReadDataPoint(const TypeIndex& type_idx) {
    auto point_result = GetDataPoint(type_idx);
    if (!point_result.IsSuccess()) {
        return Result<DataPointValue>(point_result.error_code, point_result.error_message);
    }
    
    return ReadSinglePoint(point_result.data);
}

Result<std::vector<DataPointValue>> DataPointManager::ReadDataPoints(const std::vector<TypeIndex>& type_indices) {
    std::vector<DataPointValue> values;
    values.reserve(type_indices.size());
    
    for (const auto& type_idx : type_indices) {
        auto result = ReadDataPoint(type_idx);
        if (result.IsSuccess()) {
            values.push_back(result.data);
        } else {
            // 创建一个无效的数据点值
            DataPointValue invalid_value;
            invalid_value.type_idx = type_idx;
            invalid_value.is_valid = false;
            invalid_value.timestamp = utils::TimeUtils::GetCurrentTimestamp();
            values.push_back(invalid_value);
        }
    }
    
    return Result<std::vector<DataPointValue>>(values);
}

Result<std::vector<DataPointValue>> DataPointManager::ReadAllDataPoints() {
    auto points = GetReadablePoints();
    
    std::vector<TypeIndex> type_indices;
    type_indices.reserve(points.size());
    
    for (const auto& point : points) {
        type_indices.push_back(point.type_idx);
    }
    
    return ReadDataPoints(type_indices);
}

Result<bool> DataPointManager::WriteDataPoint(const TypeIndex& type_idx, double value) {
    auto point_result = GetDataPoint(type_idx);
    if (!point_result.IsSuccess()) {
        return Result<bool>(point_result.error_code, point_result.error_message);
    }
    
    if (!IsWritablePoint(point_result.data.type_idx.data_type)) {
        return Result<bool>(ErrorCode::INVALID_PARAM, "Data point is not writable");
    }
    
    return WriteSinglePoint(point_result.data, value);
}

Result<bool> DataPointManager::WriteDataPoints(const std::map<TypeIndex, double>& values) {
    bool all_success = true;
    std::string error_messages;
    
    for (const auto& pair : values) {
        auto result = WriteDataPoint(pair.first, pair.second);
        if (!result.IsSuccess()) {
            all_success = false;
            if (!error_messages.empty()) {
                error_messages += "; ";
            }
            error_messages += result.error_message;
        }
    }
    
    if (all_success) {
        return Result<bool>(true);
    } else {
        return Result<bool>(ErrorCode::COMM_ERROR, error_messages);
    }
}

void DataPointManager::EnableAutoScan(bool enable) {
    if (auto_scan_enabled_ == enable) {
        return;
    }
    
    auto_scan_enabled_ = enable;
    
    if (enable && is_running_ && !scan_thread_) {
        scan_thread_ = std::make_unique<std::thread>(&DataPointManager::ScanThread, this);
    } else if (!enable && scan_thread_) {
        // 停止扫描线程将在 Stop() 中处理
    }
}

void DataPointManager::SetScanInterval(int interval_ms) {
    scan_interval_ms_ = interval_ms;
}

void DataPointManager::SetGlobalChangeDetection(bool enable, double threshold) {
    WriteLock lock(points_lock_);
    for (auto& pair : data_points_) {
        pair.second.enable_change_detection = enable;
        if (enable) {
            pair.second.change_threshold = threshold;
        }
    }
    WRITE_INFO_LOG("全局变化检测设置: %s, 阈值: %.6f", enable ? "启用" : "禁用", threshold);
}

void DataPointManager::EnableChangeDetectionForAllPoints(bool enable) {
    WriteLock lock(points_lock_);
    for (auto& pair : data_points_) {
        pair.second.enable_change_detection = enable;
    }
    WRITE_INFO_LOG("所有数据点变化检测: %s", enable ? "启用" : "禁用");
}

void DataPointManager::SetChangeThresholdForAllPoints(double threshold) {
    WriteLock lock(points_lock_);
    for (auto& pair : data_points_) {
        pair.second.change_threshold = threshold;
    }
    WRITE_INFO_LOG("所有数据点变化阈值设置为: %.6f", threshold);
}

void DataPointManager::SetDataChangeCallback(DataChangeCallback callback) {
    std::lock_guard<MutexLock> lock(callback_lock_);
    data_change_callback_ = callback;
}

void DataPointManager::SetAlarmCallback(AlarmCallback callback) {
    std::lock_guard<MutexLock> lock(callback_lock_);
    alarm_callback_ = callback;
}

void DataPointManager::SetStatusChangeCallback(StatusChangeCallback callback) {
    std::lock_guard<MutexLock> lock(callback_lock_);
    status_change_callback_ = callback;
}

Result<DataPointValue> DataPointManager::GetCachedValue(const TypeIndex& type_idx) const {
    std::string key = GetPointKey(type_idx);
    
    ReadLock lock(cache_lock_);
    auto it = cached_values_.find(key);
    if (it == cached_values_.end()) {
        return Result<DataPointValue>(ErrorCode::DEVICE_NOT_FOUND, "Cached value not found");
    }
    
    return Result<DataPointValue>(it->second);
}

std::map<TypeIndex, DataPointValue> DataPointManager::GetAllCachedValues() const {
    ReadLock lock(cache_lock_);
    
    std::map<TypeIndex, DataPointValue> values;
    
    for (const auto& pair : cached_values_) {
        values[pair.second.type_idx] = pair.second;
    }
    
    return values;
}

void DataPointManager::ClearCache() {
    WriteLock lock(cache_lock_);
    cached_values_.clear();
}

DataPointManager::Statistics DataPointManager::GetStatistics() const {
    std::lock_guard<MutexLock> lock(stats_lock_);
    
    Statistics stats = stats_;
    
    // 更新实时统计
    ReadLock points_lock(points_lock_);
    stats.total_points = data_points_.size();
    
    int active_count = 0;
    int error_count = 0;
    
    for (const auto& pair : data_points_) {
        if (pair.second.status == PointStatus::NORMAL) {
            active_count++;
        } else if (pair.second.status == PointStatus::ERROR) {
            error_count++;
        }
    }
    
    stats.active_points = active_count;
    stats.error_points = error_count;
    
    if (stats.scan_count > 0) {
        stats.success_rate = (double)stats.success_count / stats.scan_count * 100.0;
    }
    
    return stats;
}

void DataPointManager::ResetStatistics() {
    std::lock_guard<MutexLock> lock(stats_lock_);
    stats_ = Statistics();
}

// 配置管理方法
void DataPointManager::SetFilterEnabled(const TypeIndex& type_idx, bool enabled) {
    auto point_result = GetDataPoint(type_idx);
    if (point_result.IsSuccess()) {
        DataPointEx point = point_result.data;
        point.enable_filter = enabled;
        UpdateDataPoint(point);
    }
}

void DataPointManager::SetChangeDetectionEnabled(const TypeIndex& type_idx, bool enabled) {
    auto point_result = GetDataPoint(type_idx);
    if (point_result.IsSuccess()) {
        DataPointEx point = point_result.data;
        point.enable_change_detection = enabled;
        UpdateDataPoint(point);
    }
}

void DataPointManager::SetAlarmEnabled(const TypeIndex& type_idx, bool enabled) {
    auto point_result = GetDataPoint(type_idx);
    if (point_result.IsSuccess()) {
        DataPointEx point = point_result.data;
        point.enable_alarm = enabled;
        UpdateDataPoint(point);
    }
}

void DataPointManager::SetAlarmLimits(const TypeIndex& type_idx, double low_low, double low, double high, double high_high) {
    auto point_result = GetDataPoint(type_idx);
    if (point_result.IsSuccess()) {
        DataPointEx point = point_result.data;
        point.alarm_low_low = low_low;
        point.alarm_low = low;
        point.alarm_high = high;
        point.alarm_high_high = high_high;
        UpdateDataPoint(point);
    }
}

// 内部方法实现
void DataPointManager::ScanThread() {
    while (is_running_) {
        if (stop_event_.WaitFor(scan_interval_ms_)) {
            break;  // 收到停止信号
        }

        auto points = GetReadablePoints();

        for (auto& point : points) {
            if (!is_running_) break;

            if (point.enable_scan) {
                ProcessDataPoint(point);
            }
        }

        // 更新统计信息
        std::lock_guard<MutexLock> lock(stats_lock_);
        stats_.last_scan_time = utils::TimeUtils::GetCurrentTimestamp();
    }
}

void DataPointManager::ProcessDataPoint(DataPointEx& point) {
    auto result = ReadSinglePoint(point);

    std::lock_guard<MutexLock> lock(stats_lock_);
    stats_.scan_count++;

    if (result.IsSuccess()) {
        stats_.success_count++;

        DataPointValue value = result.data;

        // 应用滤波
        if (point.enable_filter) {
            value.scaled_value = ApplyFilter(point, value.scaled_value);
        }

        // 检查数据变化
        bool changed = CheckDataChange(point, value.scaled_value);

        // 检查报警
        if (point.enable_alarm) {
            CheckAlarms(point, value);
        }

        // 更新缓存
        std::string key = GetPointKey(point.type_idx);
        WriteLock cache_lock(cache_lock_);
        cached_values_[key] = value;

        // 触发数据变化回调
        if (changed && data_change_callback_) {
            std::lock_guard<MutexLock> cb_lock(callback_lock_);
            try {
                data_change_callback_(value);
            } catch (const std::exception& e) {
                WRITE_WARN_LOG("数据变化回调函数异常: %s", e.what());
            }
        }

        // 更新点状态
        UpdatePointStatus(point, PointStatus::NORMAL);

    } else {
        stats_.error_count++;

        // 更新错误信息
        point.error_count++;
        point.last_error_time = utils::TimeUtils::GetCurrentTimestamp();
        point.last_error_msg = result.error_message;

        // 更新点状态
        UpdatePointStatus(point, PointStatus::ERROR);
    }
}

Result<DataPointValue> DataPointManager::ReadSinglePoint(const DataPointEx& point) {
    if (!comm_) {
        return Result<DataPointValue>(ErrorCode::NOT_INITIALIZED, "Communication interface not set");
    }

    DataPointValue value;
    value.type_idx = point.type_idx;
    value.timestamp = utils::TimeUtils::GetCurrentTimestamp();
    value.quality = 0;

    // 设置从站地址
    comm_->SetSlaveId(point.slave_id);

    Result<std::vector<uint16_t>> read_result;

    switch (point.type_idx.data_type) {
        case DataType::YC:  // 遥测 - 读取保持寄存器或输入寄存器
            if (point.func_code == FunctionCode::READ_HOLDING_REGISTERS) {
                read_result = comm_->ReadHoldingRegisters(point.start_addr, point.count);
            } else if (point.func_code == FunctionCode::READ_INPUT_REGISTERS) {
                read_result = comm_->ReadInputRegisters(point.start_addr, point.count);
            } else {
                return Result<DataPointValue>(ErrorCode::INVALID_PARAM, "Invalid function code for YC");
            }
            break;

        case DataType::YX:  // 遥信 - 读取线圈或离散输入
            if (point.func_code == FunctionCode::READ_COILS) {
                auto coil_result = comm_->ReadCoils(point.start_addr, point.count);
                if (coil_result.IsSuccess()) {
                    // 转换为 uint16_t 格式
                    std::vector<uint16_t> reg_data;
                    for (bool coil : coil_result.data) {
                        reg_data.push_back(coil ? 1 : 0);
                    }
                    read_result = Result<std::vector<uint16_t>>(reg_data);
                } else {
                    read_result = Result<std::vector<uint16_t>>(coil_result.error_code, coil_result.error_message);
                }
            } else if (point.func_code == FunctionCode::READ_DISCRETE_INPUTS) {
                auto discrete_result = comm_->ReadDiscreteInputs(point.start_addr, point.count);
                if (discrete_result.IsSuccess()) {
                    // 转换为 uint16_t 格式
                    std::vector<uint16_t> reg_data;
                    for (bool discrete : discrete_result.data) {
                        reg_data.push_back(discrete ? 1 : 0);
                    }
                    read_result = Result<std::vector<uint16_t>>(reg_data);
                } else {
                    read_result = Result<std::vector<uint16_t>>(discrete_result.error_code, discrete_result.error_message);
                }
            } else {
                return Result<DataPointValue>(ErrorCode::INVALID_PARAM, "Invalid function code for YX");
            }
            break;

        default:
            return Result<DataPointValue>(ErrorCode::INVALID_PARAM, "Data type not readable");
    }

    if (!read_result.IsSuccess()) {
        return Result<DataPointValue>(read_result.error_code, read_result.error_message);
    }

    if (read_result.data.empty()) {
        return Result<DataPointValue>(ErrorCode::COMM_ERROR, "No data received");
    }

    // 根据数据类型提取和转换原始值
    auto conversion_result = ConvertRawData(read_result.data, point);
    if (!conversion_result.IsSuccess()) {
        return Result<DataPointValue>(conversion_result.error_code, conversion_result.error_message);
    }

    value.raw_value = conversion_result.data;

    // 应用工程量转换
    value.scaled_value = ApplyScaling(point, value.raw_value);

    value.is_valid = true;

    return Result<DataPointValue>(value);
}

Result<bool> DataPointManager::WriteSinglePoint(const DataPointEx& point, double value) {
    if (!comm_) {
        return Result<bool>(ErrorCode::NOT_INITIALIZED, "Communication interface not set");
    }

    // 设置从站地址
    comm_->SetSlaveId(point.slave_id);

    switch (point.type_idx.data_type) {
        case DataType::YT:  // 遥调 - 写单个寄存器
            {
                uint16_t reg_value = static_cast<uint16_t>(value);
                auto result = comm_->WriteSingleRegister(point.start_addr, reg_value);
                return Result<bool>(result.IsSuccess());
            }
            break;

        case DataType::YK:  // 遥控 - 写单个线圈
            {
                bool coil_value = (value != 0.0);
                auto result = comm_->WriteSingleCoil(point.start_addr, coil_value);
                return Result<bool>(result.IsSuccess());
            }
            break;

        default:
            return Result<bool>(ErrorCode::INVALID_PARAM, "Data type not writable");
    }
}

// 数据类型转换方法
Result<double> DataPointManager::ConvertRawData(const std::vector<uint16_t>& raw_data, const DataPointEx& point) const {
    if (raw_data.empty()) {
        return Result<double>(ErrorCode::INVALID_PARAM, "Empty raw data");
    }

    bool reverse_byte_order = (point.param == 1) && (point.data_type <= 4 || point.data_type == 8 || point.data_type == 9 || point.data_type == 10);

    switch (point.data_type) {
        case 0: // 无符号16位整数
            return Result<double>(static_cast<double>(raw_data[0]));

        case 1: // 有符号16位整数
            return Result<double>(static_cast<double>(static_cast<int16_t>(raw_data[0])));

        case 2: // 单精度浮点数
            if (raw_data.size() < 2) {
                return Result<double>(ErrorCode::INVALID_PARAM, "Insufficient data for float32");
            }
            {
                uint32_t combined;
                if (reverse_byte_order) {
                    combined = (static_cast<uint32_t>(raw_data[1]) << 16) | raw_data[0];
                } else {
                    combined = (static_cast<uint32_t>(raw_data[0]) << 16) | raw_data[1];
                }
                float float_val = *reinterpret_cast<float*>(&combined);
                return Result<double>(static_cast<double>(float_val));
            }

        case 3: // 低字前高字后
            if (raw_data.size() < 2) {
                return Result<double>(ErrorCode::INVALID_PARAM, "Insufficient data for uint32");
            }
            {
                uint32_t combined = (static_cast<uint32_t>(raw_data[1]) << 16) | raw_data[0];
                return Result<double>(static_cast<double>(combined));
            }

        case 4: // 高字前低字后
            if (raw_data.size() < 2) {
                return Result<double>(ErrorCode::INVALID_PARAM, "Insufficient data for uint32");
            }
            {
                uint32_t combined = (static_cast<uint32_t>(raw_data[0]) << 16) | raw_data[1];
                return Result<double>(static_cast<double>(combined));
            }

        case 7: // 双精度浮点数
            if (raw_data.size() < 4) {
                return Result<double>(ErrorCode::INVALID_PARAM, "Insufficient data for double");
            }
            {
                uint64_t combined;
                if (reverse_byte_order) {
                    combined = (static_cast<uint64_t>(raw_data[3]) << 48) |
                              (static_cast<uint64_t>(raw_data[2]) << 32) |
                              (static_cast<uint64_t>(raw_data[1]) << 16) |
                              raw_data[0];
                } else {
                    combined = (static_cast<uint64_t>(raw_data[0]) << 48) |
                              (static_cast<uint64_t>(raw_data[1]) << 32) |
                              (static_cast<uint64_t>(raw_data[2]) << 16) |
                              raw_data[3];
                }
                double double_val = *reinterpret_cast<double*>(&combined);
                return Result<double>(double_val);
            }

        case 8: // BCD
            if (raw_data.size() < 2) {
                return Result<double>(ErrorCode::INVALID_PARAM, "Insufficient data for BCD");
            }
            {
                // 简单BCD解码 (假设2个寄存器包含4位BCD数字)
                uint32_t bcd_value;
                if (reverse_byte_order) {
                    bcd_value = (static_cast<uint32_t>(raw_data[1]) << 16) | raw_data[0];
                } else {
                    bcd_value = (static_cast<uint32_t>(raw_data[0]) << 16) | raw_data[1];
                }

                double result = 0;
                double multiplier = 1;
                while (bcd_value > 0) {
                    int digit = bcd_value & 0xF;
                    if (digit > 9) {
                        return Result<double>(ErrorCode::INVALID_PARAM, "Invalid BCD digit");
                    }
                    result += digit * multiplier;
                    multiplier *= 10;
                    bcd_value >>= 4;
                }
                return Result<double>(result);
            }

        case 9: // 无符号64位整数
            if (raw_data.size() < 4) {
                return Result<double>(ErrorCode::INVALID_PARAM, "Insufficient data for uint64");
            }
            {
                uint64_t combined;
                if (reverse_byte_order) {
                    combined = (static_cast<uint64_t>(raw_data[3]) << 48) |
                              (static_cast<uint64_t>(raw_data[2]) << 32) |
                              (static_cast<uint64_t>(raw_data[1]) << 16) |
                              raw_data[0];
                } else {
                    combined = (static_cast<uint64_t>(raw_data[0]) << 48) |
                              (static_cast<uint64_t>(raw_data[1]) << 32) |
                              (static_cast<uint64_t>(raw_data[2]) << 16) |
                              raw_data[3];
                }
                return Result<double>(static_cast<double>(combined));
            }

        case 10: // 非补码有符号16位整数
            {
                uint16_t raw = raw_data[0];
                // 非补码转换：最高位为符号位，其余为数值
                if (raw & 0x8000) {
                    // 负数：符号位为1
                    int16_t value = -(raw & 0x7FFF);
                    return Result<double>(static_cast<double>(value));
                } else {
                    // 正数：符号位为0
                    return Result<double>(static_cast<double>(raw));
                }
            }

        case 5:  // 有、无符号混合16位整数
        case 6:  // 有、无符号混合32位整数
        case 11: // 非补码有、无符号混合16位整数
            // 这些类型需要按BIT位解析，参数中包含每个位的符号信息
            // 暂时返回原始值，需要在上层处理多个数据点
            return Result<double>(static_cast<double>(raw_data[0]));

        default:
            return Result<double>(ErrorCode::NOT_SUPPORTED, "Unsupported data type: " + std::to_string(point.data_type));
    }
}

// 数据处理方法
double DataPointManager::ApplyScaling(const DataPointEx& point, double raw_value) const {
    return raw_value * point.scale + point.offset;
}

double DataPointManager::ApplyFilter(DataPointEx& point, double value) {
    if (point.filter_buffer.size() >= static_cast<size_t>(point.filter_window)) {
        point.filter_buffer.erase(point.filter_buffer.begin());
    }

    point.filter_buffer.push_back(value);

    if (point.filter_type == "AVERAGE") {
        double sum = 0.0;
        for (double v : point.filter_buffer) {
            sum += v;
        }
        return sum / point.filter_buffer.size();
    }

    // 默认返回原值
    return value;
}

bool DataPointManager::CheckDataChange(DataPointEx& point, double value) {
    if (!point.enable_change_detection) {
        return true;  // 如果未启用变化检测，总是认为有变化
    }

    double diff = std::abs(value - point.last_value);
    bool changed = (diff >= point.change_threshold);

    if (changed) {
        point.last_value = value;
    }

    return changed;
}

void DataPointManager::CheckAlarms(const DataPointEx& point, const DataPointValue& value) {
    if (!point.enable_alarm || !alarm_callback_) {
        return;
    }

    std::string alarm_type;

    if (value.scaled_value <= point.alarm_low_low) {
        alarm_type = "LOW_LOW";
    } else if (value.scaled_value <= point.alarm_low) {
        alarm_type = "LOW";
    } else if (value.scaled_value >= point.alarm_high_high) {
        alarm_type = "HIGH_HIGH";
    } else if (value.scaled_value >= point.alarm_high) {
        alarm_type = "HIGH";
    }

    if (!alarm_type.empty()) {
        std::lock_guard<MutexLock> lock(callback_lock_);
        try {
            alarm_callback_(point, value, alarm_type);
        } catch (const std::exception& e) {
            WRITE_WARN_LOG("Alarm callback exception: %s", e.what());
        }
    }
}

void DataPointManager::UpdatePointStatus(DataPointEx& point, PointStatus new_status) {
    if (point.status != new_status) {
        PointStatus old_status = point.status;
        point.status = new_status;

        if (status_change_callback_) {
            std::lock_guard<MutexLock> lock(callback_lock_);
            try {
                status_change_callback_(point, old_status, new_status);
            } catch (const std::exception& e) {
                WRITE_WARN_LOG("Status change callback exception: %s", e.what());
            }
        }
    }
}

// 工具方法
bool DataPointManager::IsReadablePoint(DataType data_type) const {
    return data_type == DataType::YC || data_type == DataType::YX;
}

bool DataPointManager::IsWritablePoint(DataType data_type) const {
    return data_type == DataType::YT || data_type == DataType::YK;
}

std::string DataPointManager::GetPointKey(const TypeIndex& type_idx) const {
    return utils::StringUtils::Format("%d:%05d", static_cast<int>(type_idx.data_type), type_idx.point_id);
}

// DeviceDataPointManager 实现
DeviceDataPointManager::DeviceDataPointManager(int device_id)
    : device_id_(device_id)
    , device_name_("Device_" + std::to_string(device_id)) {
}

DeviceDataPointManager::~DeviceDataPointManager() {
    Shutdown();
}

Result<bool> DeviceDataPointManager::Initialize(std::shared_ptr<ModbusCommInterface> comm, const std::string& point_file) {
    WRITE_INFO_LOG("设备 %d 调用了原始的Initialize方法 (point_file=%s)", device_id_, point_file.c_str());

    point_manager_ = std::make_shared<DataPointManager>(comm);

    auto init_result = point_manager_->Initialize();
    if (!init_result.IsSuccess()) {
        return init_result;
    }

    // 加载点表文件
    if (!point_file.empty()) {
        PointTableLoader loader;
        auto load_result = loader.LoadPointTable(point_file);
        if (load_result.IsSuccess()) {
            // 将加载的数据点添加到管理器
            for (const auto& point : load_result.data) {
                DataPointEx ex_point(point);
                point_manager_->AddDataPoint(ex_point);
            }
        } else {
            WRITE_WARN_LOG("点表文件加载失败: %s", load_result.error_message.c_str());
        }
    }

    // 设置回调函数
    point_manager_->SetDataChangeCallback([this](const DataPointValue& value) {
        DataChangeHandler(value);
    });

    point_manager_->SetAlarmCallback([this](const DataPointEx& point, const DataPointValue& value, const std::string& alarm_type) {
        AlarmHandler(point, value, alarm_type);
    });

    point_manager_->SetStatusChangeCallback([this](const DataPointEx& point, PointStatus old_status, PointStatus new_status) {
        StatusChangeHandler(point, old_status, new_status);
    });

    WRITE_INFO_LOG("设备 %d 数据点管理器初始化完成", device_id_);
    return Result<bool>(true);
}

Result<bool> DeviceDataPointManager::Initialize(std::shared_ptr<ModbusCommInterface> comm, const std::string& point_file, const DevicePointConfig& embedded_config) {
    WRITE_INFO_LOG("设备 %d 调用了新的Initialize方法 (point_file=%s)", device_id_, point_file.c_str());

    // 先调用原始的初始化方法
    auto init_result = Initialize(comm, point_file);
    if (!init_result.IsSuccess()) {
        return init_result;
    }

    // 处理内嵌配置
    auto embedded_result = ProcessEmbeddedConfig(embedded_config);
    if (!embedded_result.IsSuccess()) {
        WRITE_WARN_LOG("处理内嵌数据点配置失败: %s", embedded_result.error_message.c_str());
        return embedded_result;
    }

    return Result<bool>(true);
}

void DeviceDataPointManager::Shutdown() {
    if (point_manager_) {
        point_manager_->Shutdown();
    }

    WRITE_INFO_LOG("设备 %d 数据点管理器已关闭", device_id_);
}

void DeviceDataPointManager::EnableDataReporting(bool enable) {
    data_reporting_enabled_ = enable;
}

void DeviceDataPointManager::SetReportInterval(int interval_ms) {
    report_interval_ms_ = interval_ms;
}

void DeviceDataPointManager::DataChangeHandler(const DataPointValue& value) {
    if (data_reporting_enabled_) {
        WRITE_DEBUG_LOG("设备 %d 数据变化: %d:%d = %.6f",
                       device_id_, static_cast<int>(value.type_idx.data_type),
                       value.type_idx.point_id, value.scaled_value);
    }
}

void DeviceDataPointManager::AlarmHandler(const DataPointEx& point, const DataPointValue& value, const std::string& alarm_type) {
    WRITE_WARN_LOG("设备 %d 报警: %d:%d = %.6f (%s)",
                   device_id_, static_cast<int>(point.type_idx.data_type),
                   point.type_idx.point_id, value.scaled_value, alarm_type.c_str());
}

Result<bool> DeviceDataPointManager::ProcessEmbeddedConfig(const DevicePointConfig& config) {
    if (!point_manager_) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Point manager not initialized");
    }

    WRITE_INFO_LOG("设备 %d 开始处理内嵌数据点配置: YC=%zu, YX=%zu, YT=%zu, YK=%zu, YS=%zu",
                   device_id_, config.yc_configs.size(), config.yx_configs.size(),
                   config.yt_configs.size(), config.yk_configs.size(), config.ys_configs.size());



    // 使用现有的ModbusPointParser来生成数据点
    ModbusPointParser parser;
    auto points_result = parser.GenerateDataPoints(config, device_id_, device_id_);

    if (!points_result.IsSuccess()) {
        return Result<bool>(ErrorCode::CONFIG_ERROR, "Failed to generate data points: " + points_result.error_message);
    }

    // 添加生成的数据点到管理器
    int total_points = 0;
    for (const auto& point : points_result.data) {
        auto add_result = point_manager_->AddDataPoint(point);
        if (!add_result.IsSuccess()) {
            WRITE_WARN_LOG("添加数据点失败: %s", add_result.error_message.c_str());
        } else {
            total_points++;
        }
    }

    if (total_points > 0) {
        WRITE_INFO_LOG("设备 %d 成功添加了 %d 个内嵌数据点", device_id_, total_points);
    }

    return Result<bool>(true);
}

void DeviceDataPointManager::StatusChangeHandler(const DataPointEx& point, PointStatus old_status, PointStatus new_status) {
    WRITE_INFO_LOG("设备 %d 数据点状态变化: %d:%d 从 %d 变为 %d",
                   device_id_, static_cast<int>(point.type_idx.data_type),
                   point.type_idx.point_id, static_cast<int>(old_status), static_cast<int>(new_status));
}



} // namespace modbus
