#ifndef DATA_POINT_MANAGER_H
#define DATA_POINT_MANAGER_H

#include "../types/modbus_types.h"
#include "../comm/modbus_comm_interface.h"
#include "../utils/thread_pool.h"
#include "../utils/logger.h"
#include <map>
#include <vector>
#include <memory>
#include <functional>
#include <atomic>

namespace modbus {

// 数据点值结构
struct DataPointValue {
    TypeIndex type_idx;
    double raw_value;           // 原始值
    double scaled_value;        // 工程值
    uint64_t timestamp;         // 时间戳
    bool is_valid;              // 数据有效性
    int quality;                // 数据品质
    
    DataPointValue() : raw_value(0.0), scaled_value(0.0), timestamp(0), is_valid(false), quality(0) {}
};

// 数据点状态
enum class PointStatus {
    NORMAL = 0,     // 正常
    OFFLINE = 1,    // 离线
    ERROR = 2,      // 错误
    TIMEOUT = 3,    // 超时
    INVALID = 4     // 无效
};

// 数据点扩展信息
struct DataPointEx : public DataPoint {
    // 采集配置
    int scan_interval_ms = 1000;        // 扫描间隔
    bool enable_scan = true;             // 启用扫描
    uint64_t last_scan_time = 0;         // 最后扫描时间
    
    // 数据处理
    bool enable_filter = false;          // 启用滤波
    std::string filter_type = "AVERAGE"; // 滤波类型
    int filter_window = 5;               // 滤波窗口
    std::vector<double> filter_buffer;   // 滤波缓冲区
    
    // 变化检测
    bool enable_change_detection = true; // 启用变化检测
    double change_threshold = 0.01;      // 变化阈值
    double last_value = 0.0;             // 上次值
    
    // 报警配置
    bool enable_alarm = false;           // 启用报警
    double alarm_low_low = -999999.0;    // 下下限
    double alarm_low = -999999.0;        // 下限
    double alarm_high = 999999.0;        // 上限
    double alarm_high_high = 999999.0;   // 上上限
    
    // 状态信息
    PointStatus status = PointStatus::NORMAL;
    int error_count = 0;                 // 错误计数
    uint64_t last_error_time = 0;        // 最后错误时间
    std::string last_error_msg;          // 最后错误消息
    
    DataPointEx() = default;
    DataPointEx(const DataPoint& base) : DataPoint(base) {}
};

// 数据变化回调函数类型
using DataChangeCallback = std::function<void(const DataPointValue& value)>;
using AlarmCallback = std::function<void(const DataPointEx& point, const DataPointValue& value, const std::string& alarm_type)>;
using StatusChangeCallback = std::function<void(const DataPointEx& point, PointStatus old_status, PointStatus new_status)>;

// 数据点管理器
class DataPointManager {
public:
    explicit DataPointManager(std::shared_ptr<ModbusCommInterface> comm);
    virtual ~DataPointManager();
    
    // 禁止拷贝和赋值
    DataPointManager(const DataPointManager&) = delete;
    DataPointManager& operator=(const DataPointManager&) = delete;
    
    // 初始化和控制
    Result<bool> Initialize();
    void Shutdown();
    Result<bool> Start();
    void Stop();
    bool IsRunning() const { return is_running_; }
    
    // 数据点管理
    Result<bool> AddDataPoint(const DataPointEx& point);
    Result<bool> RemoveDataPoint(const TypeIndex& type_idx);
    Result<bool> UpdateDataPoint(const DataPointEx& point);
    Result<DataPointEx> GetDataPoint(const TypeIndex& type_idx) const;
    std::vector<DataPointEx> GetAllDataPoints() const;
    
    // 按类型获取数据点
    std::vector<DataPointEx> GetDataPointsByType(DataType data_type) const;
    std::vector<DataPointEx> GetReadablePoints() const;  // YC + YX
    std::vector<DataPointEx> GetWritablePoints() const;  // YT + YK
    
    // 数据读取
    Result<DataPointValue> ReadDataPoint(const TypeIndex& type_idx);
    Result<std::vector<DataPointValue>> ReadDataPoints(const std::vector<TypeIndex>& type_indices);
    Result<std::vector<DataPointValue>> ReadAllDataPoints();
    
    // 数据写入
    Result<bool> WriteDataPoint(const TypeIndex& type_idx, double value);
    Result<bool> WriteDataPoints(const std::map<TypeIndex, double>& values);
    
    // 自动扫描控制
    void EnableAutoScan(bool enable = true);
    bool IsAutoScanEnabled() const { return auto_scan_enabled_; }
    void SetScanInterval(int interval_ms);
    int GetScanInterval() const { return scan_interval_ms_; }

    // 变化检测配置
    void SetGlobalChangeDetection(bool enable, double threshold = 0.01);
    void EnableChangeDetectionForAllPoints(bool enable);
    void SetChangeThresholdForAllPoints(double threshold);
    
    // 回调函数设置
    void SetDataChangeCallback(DataChangeCallback callback);
    void SetAlarmCallback(AlarmCallback callback);
    void SetStatusChangeCallback(StatusChangeCallback callback);
    
    // 数据缓存管理
    Result<DataPointValue> GetCachedValue(const TypeIndex& type_idx) const;
    std::map<TypeIndex, DataPointValue> GetAllCachedValues() const;
    void ClearCache();
    
    // 统计信息
    struct Statistics {
        int total_points = 0;
        int active_points = 0;
        int error_points = 0;
        int scan_count = 0;
        int success_count = 0;
        int error_count = 0;
        double success_rate = 0.0;
        uint64_t last_scan_time = 0;
    };
    
    Statistics GetStatistics() const;
    void ResetStatistics();
    
    // 配置管理
    void SetFilterEnabled(const TypeIndex& type_idx, bool enabled);
    void SetChangeDetectionEnabled(const TypeIndex& type_idx, bool enabled);
    void SetAlarmEnabled(const TypeIndex& type_idx, bool enabled);
    void SetAlarmLimits(const TypeIndex& type_idx, double low_low, double low, double high, double high_high);
    
private:
    // 内部方法
    void ScanThread();
    void ProcessDataPoint(DataPointEx& point);
    Result<DataPointValue> ReadSinglePoint(const DataPointEx& point);
    Result<bool> WriteSinglePoint(const DataPointEx& point, double value);
    
    // 数据处理
    Result<double> ConvertRawData(const std::vector<uint16_t>& raw_data, const DataPointEx& point) const;
    double ApplyScaling(const DataPointEx& point, double raw_value) const;
    double ApplyFilter(DataPointEx& point, double value);
    bool CheckDataChange(DataPointEx& point, double value);
    void CheckAlarms(const DataPointEx& point, const DataPointValue& value);
    void UpdatePointStatus(DataPointEx& point, PointStatus new_status);
    
    // 工具方法
    bool IsReadablePoint(DataType data_type) const;
    bool IsWritablePoint(DataType data_type) const;
    std::string GetPointKey(const TypeIndex& type_idx) const;
    
private:
    std::shared_ptr<ModbusCommInterface> comm_;
    
    // 数据点存储
    std::map<std::string, DataPointEx> data_points_;
    std::map<std::string, DataPointValue> cached_values_;
    
    // 线程控制
    std::unique_ptr<std::thread> scan_thread_;
    std::atomic<bool> is_running_{false};
    std::atomic<bool> auto_scan_enabled_{true};
    std::atomic<int> scan_interval_ms_{1000};
    Event stop_event_;
    
    // 回调函数
    DataChangeCallback data_change_callback_;
    AlarmCallback alarm_callback_;
    StatusChangeCallback status_change_callback_;
    
    // 统计信息
    mutable Statistics stats_;
    
    // 线程安全
    mutable ReadWriteLock points_lock_;
    mutable ReadWriteLock cache_lock_;
    mutable MutexLock stats_lock_;
    mutable MutexLock callback_lock_;
};

// 设备数据点管理器 - 管理单个设备的所有数据点
class DeviceDataPointManager {
public:
    explicit DeviceDataPointManager(int device_id);
    virtual ~DeviceDataPointManager();
    
    // 禁止拷贝和赋值
    DeviceDataPointManager(const DeviceDataPointManager&) = delete;
    DeviceDataPointManager& operator=(const DeviceDataPointManager&) = delete;
    
    // 初始化
    Result<bool> Initialize(std::shared_ptr<ModbusCommInterface> comm, const std::string& point_file);
    Result<bool> Initialize(std::shared_ptr<ModbusCommInterface> comm, const std::string& point_file, const DevicePointConfig& embedded_config);
    void Shutdown();
    
    // 获取管理器
    std::shared_ptr<DataPointManager> GetDataPointManager() const { return point_manager_; }
    
    // 设备信息
    int GetDeviceId() const { return device_id_; }
    std::string GetDeviceName() const { return device_name_; }
    void SetDeviceName(const std::string& name) { device_name_ = name; }
    
    // 数据上报
    void EnableDataReporting(bool enable = true);
    bool IsDataReportingEnabled() const { return data_reporting_enabled_; }
    void SetReportInterval(int interval_ms);
    
private:
    void DataChangeHandler(const DataPointValue& value);
    void AlarmHandler(const DataPointEx& point, const DataPointValue& value, const std::string& alarm_type);
    void StatusChangeHandler(const DataPointEx& point, PointStatus old_status, PointStatus new_status);

    // 内嵌配置处理
    Result<bool> ProcessEmbeddedConfig(const DevicePointConfig& config);
    
private:
    int device_id_;
    std::string device_name_;
    std::shared_ptr<DataPointManager> point_manager_;
    std::atomic<bool> data_reporting_enabled_{false};
    std::atomic<int> report_interval_ms_{200};
};

} // namespace modbus

#endif // DATA_POINT_MANAGER_H
