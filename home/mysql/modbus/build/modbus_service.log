[2025-07-31 16:12:06.981] [INFO ] [128037227370304] 正在初始化 Modbus 协议服务 1.0.0
[2025-07-31 16:12:06.981] [WARN ] [128037227370304] Redis publisher created without hiredis support
[2025-07-31 16:12:06.981] [WARN ] [128037227370304] Redis subscriber created without hiredis support
[2025-07-31 16:12:06.981] [INFO ] [128037227370304] Redis 管理器初始化完成
[2025-07-31 16:12:06.981] [INFO ] [128037227370304] 设备管理器初始化完成
[2025-07-31 16:12:06.981] [INFO ] [128037227370304] Creating communication interface for device 1 (type: 0)
[2025-07-31 16:12:06.981] [INFO ] [128037227370304] Data point manager shutdown
[2025-07-31 16:12:06.981] [INFO ] [128037227370304] Device data point manager shutdown for device 1
[2025-07-31 16:12:06.981] [INFO ] [128037227370304] Device 1 (Device 1) shutdown
[2025-07-31 16:12:06.981] [INFO ] [128037227370304] Data point manager shutdown
[2025-07-31 16:12:06.981] [INFO ] [128037227370304] Device data point manager shutdown for device 1
[2025-07-31 16:12:06.981] [INFO ] [128037227370304] Data point manager shutdown
[2025-07-31 16:12:06.981] [WARN ] [128037227370304] Failed to load devices from config: Device 1: Communication interface is null
[2025-07-31 16:12:06.981] [INFO ] [128037227370304] Modbus 协议服务初始化成功
[2025-07-31 16:12:06.981] [INFO ] [128037227370304] 正在启动 Modbus 协议服务
[2025-07-31 16:12:06.981] [WARN ] [128037227370304] Redis 发布器连接失败: Redis not supported - hiredis not available
[2025-07-31 16:12:06.981] [WARN ] [128037227370304] Redis 订阅器启动失败: Redis not supported
[2025-07-31 16:12:06.981] [INFO ] [128037227370304] Redis 管理器已启动
[2025-07-31 16:12:06.981] [INFO ] [128037227370304] 设备管理器已启动
[2025-07-31 16:12:06.981] [INFO ] [128037227370304] Modbus 协议服务启动成功
[2025-07-31 16:12:24.989] [INFO ] [128037227370304] 正在关闭 Modbus 协议服务
[2025-07-31 16:12:24.990] [INFO ] [128037227370304] 设备管理器已关闭
[2025-07-31 16:12:24.990] [INFO ] [128037227370304] Redis 管理器已停止
[2025-07-31 16:12:24.990] [INFO ] [128037227370304] Redis 管理器已关闭
[2025-07-31 16:12:24.990] [INFO ] [128037227370304] Modbus 协议服务关闭完成
[2025-07-31 16:26:36.710] [INFO ] [127015322597184] 正在初始化 Modbus 协议服务 1.0.0
[2025-07-31 16:26:36.710] [WARN ] [127015322597184] Redis 发布器创建时未支持 hiredis
[2025-07-31 16:26:36.710] [WARN ] [127015322597184] Redis 订阅器创建时未支持 hiredis
[2025-07-31 16:26:36.710] [INFO ] [127015322597184] Redis 管理器初始化完成
[2025-07-31 16:26:36.710] [INFO ] [127015322597184] 设备管理器初始化完成
[2025-07-31 16:26:36.710] [INFO ] [127015322597184] 正在为设备 1 创建通信接口 (类型: 0)
[2025-07-31 16:26:36.710] [INFO ] [127015322597184] 数据点管理器已关闭
[2025-07-31 16:26:36.710] [INFO ] [127015322597184] 设备 1 数据点管理器已关闭
[2025-07-31 16:26:36.710] [INFO ] [127015322597184] Device 1 (Device 1) shutdown
[2025-07-31 16:26:36.710] [INFO ] [127015322597184] 数据点管理器已关闭
[2025-07-31 16:26:36.710] [INFO ] [127015322597184] 设备 1 数据点管理器已关闭
[2025-07-31 16:26:36.710] [INFO ] [127015322597184] 数据点管理器已关闭
[2025-07-31 16:26:36.710] [WARN ] [127015322597184] 从配置文件加载设备失败: Device 1: Communication interface is null
[2025-07-31 16:26:36.710] [INFO ] [127015322597184] Modbus 协议服务初始化成功
[2025-07-31 16:26:36.710] [INFO ] [127015322597184] 正在启动 Modbus 协议服务
[2025-07-31 16:26:36.710] [WARN ] [127015322597184] Redis 发布器连接失败: Redis not supported - hiredis not available
[2025-07-31 16:26:36.710] [WARN ] [127015322597184] Redis 订阅器启动失败: Redis not supported
[2025-07-31 16:26:36.710] [INFO ] [127015322597184] Redis 管理器已启动
[2025-07-31 16:26:36.710] [INFO ] [127015322597184] 设备管理器已启动
[2025-07-31 16:26:36.710] [INFO ] [127015322597184] Modbus 协议服务启动成功
[2025-07-31 16:27:06.712] [ERROR] [127015290128064] 服务错误: Health check failed
[2025-07-31 16:27:06.728] [INFO ] [127015322597184] 正在关闭 Modbus 协议服务
[2025-07-31 16:27:06.728] [INFO ] [127015322597184] 设备管理器已关闭
[2025-07-31 16:27:06.728] [INFO ] [127015322597184] Redis 管理器已停止
[2025-07-31 16:27:06.728] [INFO ] [127015322597184] Redis 管理器已关闭
[2025-07-31 16:27:06.728] [INFO ] [127015322597184] Modbus 协议服务关闭完成
[2025-07-31 16:32:12.794] [INFO ] [127267987236672] 正在初始化 Modbus 协议服务 1.0.0
[2025-07-31 16:32:12.794] [WARN ] [127267987236672] Redis 发布器创建时未支持 hiredis
[2025-07-31 16:32:12.794] [WARN ] [127267987236672] Redis 订阅器创建时未支持 hiredis
[2025-07-31 16:32:12.794] [INFO ] [127267987236672] Redis 管理器初始化完成
[2025-07-31 16:32:12.794] [INFO ] [127267987236672] 设备管理器初始化完成
[2025-07-31 16:32:12.794] [INFO ] [127267987236672] 正在为设备 1 创建通信接口 (类型: 0)
[2025-07-31 16:32:12.794] [INFO ] [127267987236672] 数据点管理器已关闭
[2025-07-31 16:32:12.794] [INFO ] [127267987236672] 设备 1 数据点管理器已关闭
[2025-07-31 16:32:12.794] [INFO ] [127267987236672] 设备 1 (Device 1) 已关闭
[2025-07-31 16:32:12.794] [INFO ] [127267987236672] 数据点管理器已关闭
[2025-07-31 16:32:12.794] [INFO ] [127267987236672] 设备 1 数据点管理器已关闭
[2025-07-31 16:32:12.794] [INFO ] [127267987236672] 数据点管理器已关闭
[2025-07-31 16:32:12.794] [WARN ] [127267987236672] 从配置文件加载设备失败: Device 1: Communication interface is null
[2025-07-31 16:32:12.794] [INFO ] [127267987236672] Modbus 协议服务初始化成功
[2025-07-31 16:32:12.794] [INFO ] [127267987236672] 正在启动 Modbus 协议服务
[2025-07-31 16:32:12.794] [WARN ] [127267987236672] Redis 发布器连接失败: Redis not supported - hiredis not available
[2025-07-31 16:32:12.794] [WARN ] [127267987236672] Redis 订阅器启动失败: Redis not supported
[2025-07-31 16:32:12.794] [INFO ] [127267987236672] Redis 管理器已启动
[2025-07-31 16:32:12.794] [INFO ] [127267987236672] 设备管理器已启动
[2025-07-31 16:32:12.794] [INFO ] [127267987236672] Modbus 协议服务启动成功
[2025-07-31 16:32:42.794] [ERROR] [127267955001024] 服务错误: 健康检查失败
[2025-07-31 16:32:42.831] [INFO ] [127267987236672] 正在关闭 Modbus 协议服务
[2025-07-31 16:32:42.831] [INFO ] [127267987236672] 设备管理器已关闭
[2025-07-31 16:32:42.831] [INFO ] [127267987236672] Redis 管理器已停止
[2025-07-31 16:32:42.831] [INFO ] [127267987236672] Redis 管理器已关闭
[2025-07-31 16:32:42.831] [INFO ] [127267987236672] Modbus 协议服务关闭完成
[2025-07-31 16:33:12.053] [INFO ] [136361930757952] 正在初始化 Modbus 协议服务 1.0.0
[2025-07-31 16:33:12.053] [WARN ] [136361930757952] Redis 发布器创建时未支持 hiredis
[2025-07-31 16:33:12.053] [WARN ] [136361930757952] Redis 订阅器创建时未支持 hiredis
[2025-07-31 16:33:12.053] [INFO ] [136361930757952] Redis 管理器初始化完成
[2025-07-31 16:33:12.053] [INFO ] [136361930757952] 设备管理器初始化完成
[2025-07-31 16:33:12.053] [INFO ] [136361930757952] 正在为设备 1 创建通信接口 (类型: 0)
[2025-07-31 16:33:12.053] [INFO ] [136361930757952] 数据点管理器已关闭
[2025-07-31 16:33:12.053] [INFO ] [136361930757952] 设备 1 数据点管理器已关闭
[2025-07-31 16:33:12.053] [INFO ] [136361930757952] 设备 1 (Device 1) 已关闭
[2025-07-31 16:33:12.053] [INFO ] [136361930757952] 数据点管理器已关闭
[2025-07-31 16:33:12.053] [INFO ] [136361930757952] 设备 1 数据点管理器已关闭
[2025-07-31 16:33:12.053] [INFO ] [136361930757952] 数据点管理器已关闭
[2025-07-31 16:33:12.053] [WARN ] [136361930757952] 从配置文件加载设备失败: Device 1: Communication interface is null
[2025-07-31 16:33:12.053] [INFO ] [136361930757952] Modbus 协议服务初始化成功
[2025-07-31 16:33:12.053] [INFO ] [136361930757952] 正在启动 Modbus 协议服务
[2025-07-31 16:33:12.053] [WARN ] [136361930757952] Redis 发布器连接失败: Redis not supported - hiredis not available
[2025-07-31 16:33:12.053] [WARN ] [136361930757952] Redis 订阅器启动失败: Redis not supported
[2025-07-31 16:33:12.053] [INFO ] [136361930757952] Redis 管理器已启动
[2025-07-31 16:33:12.053] [INFO ] [136361930757952] 设备管理器已启动
[2025-07-31 16:33:12.053] [INFO ] [136361930757952] Modbus 协议服务启动成功
[2025-07-31 16:33:24.059] [INFO ] [136361930757952] 正在关闭 Modbus 协议服务
[2025-07-31 16:33:24.060] [INFO ] [136361930757952] 设备管理器已关闭
[2025-07-31 16:33:24.060] [INFO ] [136361930757952] Redis 管理器已停止
[2025-07-31 16:33:24.060] [INFO ] [136361930757952] Redis 管理器已关闭
[2025-07-31 16:33:24.060] [INFO ] [136361930757952] Modbus 协议服务关闭完成
[2025-07-31 16:48:18.705] [INFO ] [134130640656192] 正在初始化 Modbus 协议服务 1.0.0
[2025-07-31 16:48:18.705] [INFO ] [134130640656192] Redis 管理器初始化完成
[2025-07-31 16:48:18.705] [INFO ] [134130640656192] 设备管理器初始化完成
[2025-07-31 16:48:18.705] [INFO ] [134130640656192] 正在为设备 1 创建通信接口 (类型: 0)
[2025-07-31 16:48:18.705] [INFO ] [134130640656192] 数据点管理器已关闭
[2025-07-31 16:48:18.705] [INFO ] [134130640656192] 设备 1 数据点管理器已关闭
[2025-07-31 16:48:18.705] [INFO ] [134130640656192] 设备 1 (Device 1) 已关闭
[2025-07-31 16:48:18.705] [INFO ] [134130640656192] 数据点管理器已关闭
[2025-07-31 16:48:18.705] [INFO ] [134130640656192] 设备 1 数据点管理器已关闭
[2025-07-31 16:48:18.705] [INFO ] [134130640656192] 数据点管理器已关闭
[2025-07-31 16:48:18.705] [WARN ] [134130640656192] 从配置文件加载设备失败: Device 1: Communication interface is null
[2025-07-31 16:48:18.705] [INFO ] [134130640656192] Modbus 协议服务初始化成功
[2025-07-31 16:48:18.705] [INFO ] [134130640656192] 正在启动 Modbus 协议服务
[2025-07-31 16:48:18.705] [ERROR] [134130640656192] 服务错误: Redis publisher error: Connection refused
[2025-07-31 16:48:18.705] [WARN ] [134130640656192] Redis publisher disconnected: Connection refused
[2025-07-31 16:48:18.705] [WARN ] [134130640656192] Redis 发布器连接失败: Connection refused
[2025-07-31 16:48:18.706] [ERROR] [134130640656192] 服务错误: Redis subscriber error: Connection refused
[2025-07-31 16:48:18.706] [WARN ] [134130640656192] Redis subscriber disconnected: Connection refused
[2025-07-31 16:48:18.706] [WARN ] [134130640656192] Redis 订阅器启动失败: Connection refused
[2025-07-31 16:48:18.706] [INFO ] [134130640656192] Redis 管理器已启动
[2025-07-31 16:48:18.706] [INFO ] [134130640656192] 设备管理器已启动
[2025-07-31 16:48:18.706] [INFO ] [134130640656192] Modbus 协议服务启动成功
[2025-07-31 16:48:36.717] [INFO ] [134130640656192] 正在关闭 Modbus 协议服务
[2025-07-31 16:48:36.717] [INFO ] [134130640656192] 设备管理器已关闭
[2025-07-31 16:48:36.717] [INFO ] [134130640656192] Redis 管理器已停止
[2025-07-31 16:48:36.717] [INFO ] [134130640656192] Redis 管理器已关闭
[2025-07-31 16:48:36.717] [INFO ] [134130640656192] Modbus 协议服务关闭完成
[2025-07-31 17:22:38.630] [INFO ] [131414587352896] 正在初始化 Modbus 协议服务 1.0.0
[2025-07-31 17:22:38.630] [INFO ] [131414587352896] Redis 管理器初始化完成
[2025-07-31 17:22:38.630] [INFO ] [131414587352896] 设备管理器初始化完成
[2025-07-31 17:22:38.630] [INFO ] [131414587352896] 正在为设备 1 创建通信接口 (类型: 0)
[2025-07-31 17:22:38.630] [INFO ] [131414587352896] 数据点管理器已关闭
[2025-07-31 17:22:38.630] [INFO ] [131414587352896] 设备 1 数据点管理器已关闭
[2025-07-31 17:22:38.630] [INFO ] [131414587352896] 设备 1 (Device 1) 已关闭
[2025-07-31 17:22:38.630] [INFO ] [131414587352896] 数据点管理器已关闭
[2025-07-31 17:22:38.630] [INFO ] [131414587352896] 设备 1 数据点管理器已关闭
[2025-07-31 17:22:38.630] [INFO ] [131414587352896] 数据点管理器已关闭
[2025-07-31 17:22:38.630] [WARN ] [131414587352896] 从配置文件加载设备失败: Device 1: Communication interface is null
[2025-07-31 17:22:38.630] [INFO ] [131414587352896] Modbus 协议服务初始化成功
[2025-07-31 17:22:38.630] [INFO ] [131414587352896] 正在启动 Modbus 协议服务
[2025-07-31 17:22:38.630] [ERROR] [131414587352896] 服务错误: Redis publisher error: Connection refused
[2025-07-31 17:22:38.630] [WARN ] [131414587352896] Redis publisher disconnected: Connection refused
[2025-07-31 17:22:38.630] [WARN ] [131414587352896] Redis 发布器连接失败: Connection refused
[2025-07-31 17:22:38.630] [ERROR] [131414587352896] 服务错误: Redis subscriber error: Connection refused
[2025-07-31 17:22:38.630] [WARN ] [131414587352896] Redis subscriber disconnected: Connection refused
[2025-07-31 17:22:38.630] [WARN ] [131414587352896] Redis 订阅器启动失败: Connection refused
[2025-07-31 17:22:38.630] [INFO ] [131414587352896] Redis 管理器已启动
[2025-07-31 17:22:38.630] [INFO ] [131414587352896] 设备管理器已启动
[2025-07-31 17:22:38.630] [INFO ] [131414587352896] Modbus 协议服务启动成功
[2025-07-31 17:22:56.638] [INFO ] [131414587352896] 正在关闭 Modbus 协议服务
[2025-07-31 17:22:56.638] [INFO ] [131414587352896] 设备管理器已关闭
[2025-07-31 17:22:56.638] [INFO ] [131414587352896] Redis 管理器已停止
[2025-07-31 17:22:56.638] [INFO ] [131414587352896] Redis 管理器已关闭
[2025-07-31 17:22:56.638] [INFO ] [131414587352896] Modbus 协议服务关闭完成
