<?xml version="1.0" encoding="UTF-8"?>
<!-- Modbus 服务配置文件 -->
<modbus_config>
    <service>
        <name>Modbus Service</name>
        <version>1.0.0</version>
        <log_level>DEBUG</log_level>
        <log_file>modbus_service.log</log_file>
        <report_interval_ms>200</report_interval_ms>
        <max_threads>4</max_threads>
        <redis>
            <ip>************** </ip>
            <port>6379</port>
            <password></password>
            <database>0</database>
            <connection_timeout>3000</connection_timeout>
            <command_timeout>1000</command_timeout>
        </redis>
    </service>
    <devices>
        <device id="1" name="XJ-SVG-1" type="SVG">
            <slave_id>1</slave_id>
            <communication type="RTU">
                <device>/dev/ttyS0</device>
                <baud>9600</baud>
                <parity>N</parity>
                <data_bits>8</data_bits>
                <stop_bits>1</stop_bits>
                <timeout>200</timeout>
            </communication>
            <scan_config>
                <scan_interval_ms>200</scan_interval_ms>
                <enable_auto_scan>true</enable_auto_scan>
                <retry_count>3</retry_count>
                <retry_interval_ms>500</retry_interval_ms>
                <!-- 变化检测配置 -->
                <enable_change_detection>true</enable_change_detection>
                <change_threshold>0.01</change_threshold>
            </scan_config>
            <data_points>
                <YC>
                    <point>03,0000,10,1,0,0</point>
                </YC>
            </data_points>
        </device>
    </devices>

    <advanced>
        <enable_statistics>true</enable_statistics>
        <statistics_interval_ms>5000</statistics_interval_ms>
        <enable_performance_monitor>true</enable_performance_monitor>
        <max_memory_usage_mb>512</max_memory_usage_mb>
        <enable_watchdog>true</enable_watchdog>
        <watchdog_timeout_ms>30000</watchdog_timeout_ms>
    </advanced>
</modbus_config>
