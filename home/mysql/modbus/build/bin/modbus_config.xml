<?xml version="1.0" encoding="UTF-8"?>
<!--
    Modbus 协议服务配置文件
    用于配置Modbus设备采集、Redis数据上报、性能监控等功能
    配置文件版本: 1.0.0
    最后更新: 2025-08-01
-->
<modbus_config>
    <!-- 服务基础配置 -->
    <service>
        <!-- 服务名称，用于日志标识和进程管理 -->
        <name>Modbus Service</name>

        <!-- 服务版本号 -->
        <version>1.0.0</version>

        <!-- 日志级别: TRACE, DEBUG, INFO, WARN, ERROR, FATAL -->
        <log_level>DEBUG</log_level>

        <!-- 日志文件路径，支持相对路径和绝对路径 -->
        <log_file>modbus_service.log</log_file>

        <!-- 数据上报间隔(毫秒)，影响Redis数据发布频率 -->
        <!-- 建议值: 100-1000ms，过小会增加网络负载，过大会增加延时 -->
        <report_interval_ms>200</report_interval_ms>

        <!-- 最大工作线程数，用于并发处理设备通信和数据处理 -->
        <!-- 建议值: CPU核心数的1-2倍 -->
        <max_threads>4</max_threads>

        <!-- Redis 数据库配置 -->
        <redis>
            <!-- Redis 服务器IP地址 -->
            <ip>************** </ip>

            <!-- Redis 服务器端口，默认6379 -->
            <port>6379</port>

            <!-- Redis 密码，为空表示无密码认证 -->
            <password></password>

            <!-- Redis 数据库编号，默认0 -->
            <database>0</database>

            <!-- Redis 连接超时时间(毫秒) -->
            <!-- 建议值: 3000-10000ms -->
            <connection_timeout>3000</connection_timeout>

            <!-- Redis 命令执行超时时间(毫秒) -->
            <!-- 建议值: 1000-5000ms -->
            <command_timeout>1000</command_timeout>
        </redis>
    </service>
    <!-- 设备配置列表 -->
    <devices>
        <!--
            单个设备配置
            id: 设备唯一标识符，用于Redis数据标识
            name: 设备名称，用于日志和监控显示
            type: 设备类型，如SVG、PLC等
        -->
        <device id="1" name="XJ-SVG-1" type="SVG">
            <!-- Modbus 从站ID，范围1-247 -->
            <slave_id>1</slave_id>

            <!-- 通信配置 -->
            <communication type="RTU">
                <!-- 串口设备路径 -->
                <!-- Linux: /dev/ttyS0, /dev/ttyUSB0 等 -->
                <!-- Windows: COM1, COM2 等 -->
                <device>/dev/ttyS0</device>

                <!-- 波特率，常用值: 9600, 19200, 38400, 115200 -->
                <baud>9600</baud>

                <!-- 校验位: N(无), E(偶), O(奇) -->
                <parity>N</parity>

                <!-- 数据位，通常为8 -->
                <data_bits>8</data_bits>

                <!-- 停止位，通常为1 -->
                <stop_bits>1</stop_bits>

                <!-- Modbus 通信超时时间(毫秒) -->
                <!-- 建议值: 100-1000ms，根据网络状况调整 -->
                <timeout>200</timeout>
            </communication>

            <!-- 数据扫描配置 -->
            <scan_config>
                <!-- 扫描间隔(毫秒)，决定数据采集频率 -->
                <!-- 建议值: 100-2000ms，影响系统实时性和负载 -->
                <scan_interval_ms>200</scan_interval_ms>

                <!-- 是否启用自动扫描 -->
                <enable_auto_scan>true</enable_auto_scan>

                <!-- 通信失败重试次数 -->
                <retry_count>3</retry_count>

                <!-- 重试间隔(毫秒) -->
                <retry_interval_ms>500</retry_interval_ms>

                <!-- 数据变化检测配置 -->
                <!-- 启用变化检测可减少不必要的Redis写入，提高性能 -->
                <enable_change_detection>true</enable_change_detection>

                <!-- 变化阈值，数值变化超过此值才认为数据发生变化 -->
                <!-- 用于过滤微小的数值波动 -->
                <change_threshold>0.01</change_threshold>
            </scan_config>

            <!-- 数据点配置 -->
            <data_points>
                <!-- 遥测数据点(YC - 模拟量) -->
                <YC>
                    <!--
                        数据点格式: 功能码,起始地址,数量,从站ID,数据类型,保留字段
                        03: 读保持寄存器
                        0000: 起始地址(十六进制)
                        10: 读取数量(十进制)
                        1: 从站ID
                        0: 数据类型(0=uint16, 1=int16, 2=uint32, 3=int32, 4=float)
                        0: 保留字段
                    -->
                    <point>03,0000,10,1,0,0</point>
                </YC>
            </data_points>
        </device>
    </devices>

    <!-- 高级功能配置 -->
    <advanced>
        <!-- 是否启用统计信息收集 -->
        <enable_statistics>true</enable_statistics>

        <!-- 统计信息更新间隔(毫秒) -->
        <statistics_interval_ms>5000</statistics_interval_ms>

        <!-- 是否启用性能监控 -->
        <!-- 监控CPU使用率、内存使用量、网络延时等 -->
        <enable_performance_monitor>true</enable_performance_monitor>

        <!-- 最大内存使用限制(MB) -->
        <!-- 超过此值会触发告警或自动清理 -->
        <max_memory_usage_mb>512</max_memory_usage_mb>

        <!-- 是否启用健康检查（看门狗功能） -->
        <!-- 禁用看门狗服务，避免自动重启和健康检查 -->
        <enable_health_check>false</enable_health_check>


    </advanced>
</modbus_config>
