[2025-08-01 16:21:51.338] [INFO ] [139074466555712] 正在初始化 Modbus 协议服务 1.0.0
[2025-08-01 16:21:51.338] [INFO ] [139074466555712] Redis 管理器初始化完成
[2025-08-01 16:21:51.338] [INFO ] [139074466555712] 设备管理器初始化完成
[2025-08-01 16:21:51.338] [INFO ] [139074466555712] 正在为设备 1 创建通信接口 (类型: 0)
[2025-08-01 16:21:51.338] [INFO ] [139074466555712] RTU 通信初始化成功，设备 /dev/ttyS0:9600
[2025-08-01 16:21:51.338] [INFO ] [139074466555712] RTU通信接口创建成功: /dev/ttyS0
[2025-08-01 16:21:51.338] [INFO ] [139074466555712] 设备 1 调用了新的Initialize方法 (point_file=)
[2025-08-01 16:21:51.338] [INFO ] [139074466555712] 设备 1 调用了原始的Initialize方法 (point_file=)
[2025-08-01 16:21:51.338] [INFO ] [139074466555712] 数据点管理器初始化完成
[2025-08-01 16:21:51.338] [INFO ] [139074466555712] 设备 1 数据点管理器初始化完成
[2025-08-01 16:21:51.339] [INFO ] [139074466555712] 设备 1 开始处理内嵌数据点配置: YC=1, YX=0, YT=0, YK=0, YS=0
[2025-08-01 16:21:51.339] [INFO ] [139074466555712] 设备 1 成功添加了 10 个内嵌数据点
[2025-08-01 16:21:51.339] [INFO ] [139074466555712] 全局变化检测设置: 启用, 阈值: 0.010000
[2025-08-01 16:21:51.339] [INFO ] [139074466555712] 设备 1 Redis 管理器初始化完成
[2025-08-01 16:21:51.339] [INFO ] [139074466555712] 设备 1 (XJ-SVG-1) 初始化完成
[2025-08-01 16:21:51.339] [INFO ] [139074466555712] Added device 1 (XJ-SVG-1)
[2025-08-01 16:21:51.339] [INFO ] [139074466555712] 设备加载成功: XJ-SVG-1 (ID: 1)
[2025-08-01 16:21:51.339] [INFO ] [139074466555712] Modbus 协议服务初始化成功
[2025-08-01 16:21:51.339] [INFO ] [139074466555712] 正在启动 Modbus 协议服务
[2025-08-01 16:21:51.339] [INFO ] [139074466555712] Redis publisher connected
[2025-08-01 16:21:51.339] [INFO ] [139074466555712] Redis 发布器已连接到 192.168.83.139:6379
[2025-08-01 16:21:51.340] [INFO ] [139074466555712] Redis subscriber connected
[2025-08-01 16:21:51.340] [INFO ] [139074466555712] Redis 订阅器已连接到 192.168.83.139:6379
[2025-08-01 16:21:51.340] [INFO ] [139074466555712] Redis subscriber started
[2025-08-01 16:21:51.340] [INFO ] [139074466555712] Redis 管理器已启动
[2025-08-01 16:21:51.340] [INFO ] [139074466555712] 设备管理器已启动
[2025-08-01 16:21:51.341] [INFO ] [139074466555712] Device 0 status changed to CONNECTED
[2025-08-01 16:21:51.341] [INFO ] [139074466555712] RTU 设备 /dev/ttyS0 连接成功
[2025-08-01 16:21:51.341] [INFO ] [139074466555712] 数据点管理器已启动
[2025-08-01 16:21:51.341] [INFO ] [139074466555712] Subscribed to Redis channel: modbus:device:1:control
[2025-08-01 16:21:51.341] [INFO ] [139074466555712] 设备 1 控制通道订阅成功
[2025-08-01 16:21:51.341] [INFO ] [139074466555712] 设备 1 (XJ-SVG-1) 已启动
[2025-08-01 16:21:51.341] [INFO ] [139074466555712] Modbus 协议服务启动成功
[2025-08-01 16:24:59.459] [INFO ] [139074417436352] 设备 1 数据点状态变化: 1:2 从 0 变为 2
[2025-08-01 16:26:14.644] [INFO ] [139074417436352] 设备 1 数据点状态变化: 1:2 从 0 变为 2
[2025-08-01 16:26:55.049] [INFO ] [139074417436352] 设备 1 数据点状态变化: 1:2 从 0 变为 2
[2025-08-01 16:26:56.051] [INFO ] [139074417436352] 设备 1 数据点状态变化: 1:3 从 0 变为 2
[2025-08-01 16:35:42.084] [INFO ] [139074417436352] 设备 1 数据点状态变化: 1:1 从 0 变为 2
[2025-08-01 16:35:43.086] [INFO ] [139074417436352] 设备 1 数据点状态变化: 1:2 从 0 变为 2
[2025-08-01 16:35:44.087] [INFO ] [139074417436352] 设备 1 数据点状态变化: 1:3 从 0 变为 2
[2025-08-01 16:35:45.089] [INFO ] [139074417436352] 设备 1 数据点状态变化: 1:4 从 0 变为 2
[2025-08-01 16:35:46.091] [INFO ] [139074417436352] 设备 1 数据点状态变化: 1:5 从 0 变为 2
[2025-08-01 16:35:47.092] [INFO ] [139074417436352] 设备 1 数据点状态变化: 1:6 从 0 变为 2
[2025-08-01 16:35:48.094] [INFO ] [139074417436352] 设备 1 数据点状态变化: 1:7 从 0 变为 2
[2025-08-01 16:35:49.096] [INFO ] [139074417436352] 设备 1 数据点状态变化: 1:8 从 0 变为 2
[2025-08-01 16:35:50.098] [INFO ] [139074417436352] 设备 1 数据点状态变化: 1:9 从 0 变为 2
[2025-08-01 16:35:51.099] [INFO ] [139074417436352] 设备 1 数据点状态变化: 1:10 从 0 变为 2
[2025-08-01 16:35:52.302] [INFO ] [139074417436352] 设备 1 数据点状态变化: 1:1 从 0 变为 2
[2025-08-01 16:35:52.585] [WARN ] [139074425829056] RTU 设备 /dev/ttyS0 连接测试失败: Connection timed out
[2025-08-01 16:35:52.585] [INFO ] [139074425829056] Device 0 status changed to ERROR
[2025-08-01 16:35:52.585] [INFO ] [139074320983744] RTU 设备 /dev/ttyS0 自动重连尝试 1/10
[2025-08-01 16:35:52.585] [INFO ] [139074320983744] 正在尝试重连 RTU 设备 /dev/ttyS0
