# CMake generated Testfile for 
# Source directory: /home/<USER>/home/<USER>/modbus
# Build directory: /home/<USER>/home/<USER>/modbus/build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(logger_test "/home/<USER>/home/<USER>/modbus/build/bin/test_logger")
set_tests_properties(logger_test PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/home/<USER>/modbus/CMakeLists.txt;210;add_test;/home/<USER>/home/<USER>/modbus/CMakeLists.txt;0;")
add_test(thread_pool_test "/home/<USER>/home/<USER>/modbus/build/bin/test_thread_pool")
set_tests_properties(thread_pool_test PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/home/<USER>/modbus/CMakeLists.txt;211;add_test;/home/<USER>/home/<USER>/modbus/CMakeLists.txt;0;")
add_test(rtu_comm_test "/home/<USER>/home/<USER>/modbus/build/bin/test_rtu_comm")
set_tests_properties(rtu_comm_test PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/home/<USER>/modbus/CMakeLists.txt;213;add_test;/home/<USER>/home/<USER>/modbus/CMakeLists.txt;0;")
add_test(tcp_comm_test "/home/<USER>/home/<USER>/modbus/build/bin/test_tcp_comm")
set_tests_properties(tcp_comm_test PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/home/<USER>/modbus/CMakeLists.txt;214;add_test;/home/<USER>/home/<USER>/modbus/CMakeLists.txt;0;")
subdirs("src/examples")
subdirs("tests")
