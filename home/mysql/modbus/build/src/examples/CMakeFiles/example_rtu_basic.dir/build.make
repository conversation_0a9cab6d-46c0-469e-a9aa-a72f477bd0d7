# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/home/<USER>/modbus

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/home/<USER>/modbus/build

# Include any dependencies generated for this target.
include src/examples/CMakeFiles/example_rtu_basic.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/examples/CMakeFiles/example_rtu_basic.dir/compiler_depend.make

# Include the progress variables for this target.
include src/examples/CMakeFiles/example_rtu_basic.dir/progress.make

# Include the compile flags for this target's objects.
include src/examples/CMakeFiles/example_rtu_basic.dir/flags.make

src/examples/CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.o: src/examples/CMakeFiles/example_rtu_basic.dir/flags.make
src/examples/CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.o: /home/<USER>/home/<USER>/modbus/src/examples/example_rtu_basic.cpp
src/examples/CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.o: src/examples/CMakeFiles/example_rtu_basic.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/examples/CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.o"
	cd /home/<USER>/home/<USER>/modbus/build/src/examples && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/examples/CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.o -MF CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.o.d -o CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.o -c /home/<USER>/home/<USER>/modbus/src/examples/example_rtu_basic.cpp

src/examples/CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.i"
	cd /home/<USER>/home/<USER>/modbus/build/src/examples && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/examples/example_rtu_basic.cpp > CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.i

src/examples/CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.s"
	cd /home/<USER>/home/<USER>/modbus/build/src/examples && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/examples/example_rtu_basic.cpp -o CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.s

# Object files for target example_rtu_basic
example_rtu_basic_OBJECTS = \
"CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.o"

# External object files for target example_rtu_basic
example_rtu_basic_EXTERNAL_OBJECTS =

bin/example_rtu_basic: src/examples/CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.o
bin/example_rtu_basic: src/examples/CMakeFiles/example_rtu_basic.dir/build.make
bin/example_rtu_basic: lib/libmodbus_protocol.a
bin/example_rtu_basic: /home/<USER>/home/<USER>/modbus/libmodbus_install/lib/libmodbus.so
bin/example_rtu_basic: /usr/lib/x86_64-linux-gnu/libhiredis.so
bin/example_rtu_basic: src/examples/CMakeFiles/example_rtu_basic.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable ../../bin/example_rtu_basic"
	cd /home/<USER>/home/<USER>/modbus/build/src/examples && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/example_rtu_basic.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/examples/CMakeFiles/example_rtu_basic.dir/build: bin/example_rtu_basic
.PHONY : src/examples/CMakeFiles/example_rtu_basic.dir/build

src/examples/CMakeFiles/example_rtu_basic.dir/clean:
	cd /home/<USER>/home/<USER>/modbus/build/src/examples && $(CMAKE_COMMAND) -P CMakeFiles/example_rtu_basic.dir/cmake_clean.cmake
.PHONY : src/examples/CMakeFiles/example_rtu_basic.dir/clean

src/examples/CMakeFiles/example_rtu_basic.dir/depend:
	cd /home/<USER>/home/<USER>/modbus/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/home/<USER>/modbus /home/<USER>/home/<USER>/modbus/src/examples /home/<USER>/home/<USER>/modbus/build /home/<USER>/home/<USER>/modbus/build/src/examples /home/<USER>/home/<USER>/modbus/build/src/examples/CMakeFiles/example_rtu_basic.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/examples/CMakeFiles/example_rtu_basic.dir/depend

