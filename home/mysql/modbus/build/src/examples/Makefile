# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/home/<USER>/modbus

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/home/<USER>/modbus/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/home/<USER>/modbus/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles /home/<USER>/home/<USER>/modbus/build/src/examples//CMakeFiles/progress.marks
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/examples/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/examples/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/examples/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/examples/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/home/<USER>/modbus/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
src/examples/CMakeFiles/example_rtu_basic.dir/rule:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/examples/CMakeFiles/example_rtu_basic.dir/rule
.PHONY : src/examples/CMakeFiles/example_rtu_basic.dir/rule

# Convenience name for target.
example_rtu_basic: src/examples/CMakeFiles/example_rtu_basic.dir/rule
.PHONY : example_rtu_basic

# fast build rule for target.
example_rtu_basic/fast:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_rtu_basic.dir/build.make src/examples/CMakeFiles/example_rtu_basic.dir/build
.PHONY : example_rtu_basic/fast

# Convenience name for target.
src/examples/CMakeFiles/example_tcp_basic.dir/rule:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/examples/CMakeFiles/example_tcp_basic.dir/rule
.PHONY : src/examples/CMakeFiles/example_tcp_basic.dir/rule

# Convenience name for target.
example_tcp_basic: src/examples/CMakeFiles/example_tcp_basic.dir/rule
.PHONY : example_tcp_basic

# fast build rule for target.
example_tcp_basic/fast:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_tcp_basic.dir/build.make src/examples/CMakeFiles/example_tcp_basic.dir/build
.PHONY : example_tcp_basic/fast

# Convenience name for target.
src/examples/CMakeFiles/example_config_parser.dir/rule:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/examples/CMakeFiles/example_config_parser.dir/rule
.PHONY : src/examples/CMakeFiles/example_config_parser.dir/rule

# Convenience name for target.
example_config_parser: src/examples/CMakeFiles/example_config_parser.dir/rule
.PHONY : example_config_parser

# fast build rule for target.
example_config_parser/fast:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_config_parser.dir/build.make src/examples/CMakeFiles/example_config_parser.dir/build
.PHONY : example_config_parser/fast

# Convenience name for target.
src/examples/CMakeFiles/example_redis_publisher.dir/rule:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/examples/CMakeFiles/example_redis_publisher.dir/rule
.PHONY : src/examples/CMakeFiles/example_redis_publisher.dir/rule

# Convenience name for target.
example_redis_publisher: src/examples/CMakeFiles/example_redis_publisher.dir/rule
.PHONY : example_redis_publisher

# fast build rule for target.
example_redis_publisher/fast:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_redis_publisher.dir/build.make src/examples/CMakeFiles/example_redis_publisher.dir/build
.PHONY : example_redis_publisher/fast

# Convenience name for target.
src/examples/CMakeFiles/example_complete_service.dir/rule:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/examples/CMakeFiles/example_complete_service.dir/rule
.PHONY : src/examples/CMakeFiles/example_complete_service.dir/rule

# Convenience name for target.
example_complete_service: src/examples/CMakeFiles/example_complete_service.dir/rule
.PHONY : example_complete_service

# fast build rule for target.
example_complete_service/fast:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_complete_service.dir/build.make src/examples/CMakeFiles/example_complete_service.dir/build
.PHONY : example_complete_service/fast

example_complete_service.o: example_complete_service.cpp.o
.PHONY : example_complete_service.o

# target to build an object file
example_complete_service.cpp.o:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_complete_service.dir/build.make src/examples/CMakeFiles/example_complete_service.dir/example_complete_service.cpp.o
.PHONY : example_complete_service.cpp.o

example_complete_service.i: example_complete_service.cpp.i
.PHONY : example_complete_service.i

# target to preprocess a source file
example_complete_service.cpp.i:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_complete_service.dir/build.make src/examples/CMakeFiles/example_complete_service.dir/example_complete_service.cpp.i
.PHONY : example_complete_service.cpp.i

example_complete_service.s: example_complete_service.cpp.s
.PHONY : example_complete_service.s

# target to generate assembly for a file
example_complete_service.cpp.s:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_complete_service.dir/build.make src/examples/CMakeFiles/example_complete_service.dir/example_complete_service.cpp.s
.PHONY : example_complete_service.cpp.s

example_config_parser.o: example_config_parser.cpp.o
.PHONY : example_config_parser.o

# target to build an object file
example_config_parser.cpp.o:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_config_parser.dir/build.make src/examples/CMakeFiles/example_config_parser.dir/example_config_parser.cpp.o
.PHONY : example_config_parser.cpp.o

example_config_parser.i: example_config_parser.cpp.i
.PHONY : example_config_parser.i

# target to preprocess a source file
example_config_parser.cpp.i:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_config_parser.dir/build.make src/examples/CMakeFiles/example_config_parser.dir/example_config_parser.cpp.i
.PHONY : example_config_parser.cpp.i

example_config_parser.s: example_config_parser.cpp.s
.PHONY : example_config_parser.s

# target to generate assembly for a file
example_config_parser.cpp.s:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_config_parser.dir/build.make src/examples/CMakeFiles/example_config_parser.dir/example_config_parser.cpp.s
.PHONY : example_config_parser.cpp.s

example_redis_publisher.o: example_redis_publisher.cpp.o
.PHONY : example_redis_publisher.o

# target to build an object file
example_redis_publisher.cpp.o:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_redis_publisher.dir/build.make src/examples/CMakeFiles/example_redis_publisher.dir/example_redis_publisher.cpp.o
.PHONY : example_redis_publisher.cpp.o

example_redis_publisher.i: example_redis_publisher.cpp.i
.PHONY : example_redis_publisher.i

# target to preprocess a source file
example_redis_publisher.cpp.i:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_redis_publisher.dir/build.make src/examples/CMakeFiles/example_redis_publisher.dir/example_redis_publisher.cpp.i
.PHONY : example_redis_publisher.cpp.i

example_redis_publisher.s: example_redis_publisher.cpp.s
.PHONY : example_redis_publisher.s

# target to generate assembly for a file
example_redis_publisher.cpp.s:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_redis_publisher.dir/build.make src/examples/CMakeFiles/example_redis_publisher.dir/example_redis_publisher.cpp.s
.PHONY : example_redis_publisher.cpp.s

example_rtu_basic.o: example_rtu_basic.cpp.o
.PHONY : example_rtu_basic.o

# target to build an object file
example_rtu_basic.cpp.o:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_rtu_basic.dir/build.make src/examples/CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.o
.PHONY : example_rtu_basic.cpp.o

example_rtu_basic.i: example_rtu_basic.cpp.i
.PHONY : example_rtu_basic.i

# target to preprocess a source file
example_rtu_basic.cpp.i:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_rtu_basic.dir/build.make src/examples/CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.i
.PHONY : example_rtu_basic.cpp.i

example_rtu_basic.s: example_rtu_basic.cpp.s
.PHONY : example_rtu_basic.s

# target to generate assembly for a file
example_rtu_basic.cpp.s:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_rtu_basic.dir/build.make src/examples/CMakeFiles/example_rtu_basic.dir/example_rtu_basic.cpp.s
.PHONY : example_rtu_basic.cpp.s

example_tcp_basic.o: example_tcp_basic.cpp.o
.PHONY : example_tcp_basic.o

# target to build an object file
example_tcp_basic.cpp.o:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_tcp_basic.dir/build.make src/examples/CMakeFiles/example_tcp_basic.dir/example_tcp_basic.cpp.o
.PHONY : example_tcp_basic.cpp.o

example_tcp_basic.i: example_tcp_basic.cpp.i
.PHONY : example_tcp_basic.i

# target to preprocess a source file
example_tcp_basic.cpp.i:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_tcp_basic.dir/build.make src/examples/CMakeFiles/example_tcp_basic.dir/example_tcp_basic.cpp.i
.PHONY : example_tcp_basic.cpp.i

example_tcp_basic.s: example_tcp_basic.cpp.s
.PHONY : example_tcp_basic.s

# target to generate assembly for a file
example_tcp_basic.cpp.s:
	cd /home/<USER>/home/<USER>/modbus/build && $(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_tcp_basic.dir/build.make src/examples/CMakeFiles/example_tcp_basic.dir/example_tcp_basic.cpp.s
.PHONY : example_tcp_basic.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... example_complete_service"
	@echo "... example_config_parser"
	@echo "... example_redis_publisher"
	@echo "... example_rtu_basic"
	@echo "... example_tcp_basic"
	@echo "... example_complete_service.o"
	@echo "... example_complete_service.i"
	@echo "... example_complete_service.s"
	@echo "... example_config_parser.o"
	@echo "... example_config_parser.i"
	@echo "... example_config_parser.s"
	@echo "... example_redis_publisher.o"
	@echo "... example_redis_publisher.i"
	@echo "... example_redis_publisher.s"
	@echo "... example_rtu_basic.o"
	@echo "... example_rtu_basic.i"
	@echo "... example_rtu_basic.s"
	@echo "... example_tcp_basic.o"
	@echo "... example_tcp_basic.i"
	@echo "... example_tcp_basic.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/home/<USER>/modbus/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

