# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/home/<USER>/modbus

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/home/<USER>/modbus/build

# Include any dependencies generated for this target.
include CMakeFiles/test_tcp_comm.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_tcp_comm.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_tcp_comm.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_tcp_comm.dir/flags.make

CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.o: CMakeFiles/test_tcp_comm.dir/flags.make
CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.o: /home/<USER>/home/<USER>/modbus/src/tests/test_tcp_comm.cpp
CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.o: CMakeFiles/test_tcp_comm.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.o -MF CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.o.d -o CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.o -c /home/<USER>/home/<USER>/modbus/src/tests/test_tcp_comm.cpp

CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/tests/test_tcp_comm.cpp > CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.i

CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/tests/test_tcp_comm.cpp -o CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.s

# Object files for target test_tcp_comm
test_tcp_comm_OBJECTS = \
"CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.o"

# External object files for target test_tcp_comm
test_tcp_comm_EXTERNAL_OBJECTS =

bin/test_tcp_comm: CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.o
bin/test_tcp_comm: CMakeFiles/test_tcp_comm.dir/build.make
bin/test_tcp_comm: lib/libmodbus_protocol.a
bin/test_tcp_comm: /home/<USER>/home/<USER>/modbus/libmodbus_install/lib/libmodbus.so
bin/test_tcp_comm: /usr/lib/x86_64-linux-gnu/libhiredis.so
bin/test_tcp_comm: CMakeFiles/test_tcp_comm.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable bin/test_tcp_comm"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_tcp_comm.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_tcp_comm.dir/build: bin/test_tcp_comm
.PHONY : CMakeFiles/test_tcp_comm.dir/build

CMakeFiles/test_tcp_comm.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_tcp_comm.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_tcp_comm.dir/clean

CMakeFiles/test_tcp_comm.dir/depend:
	cd /home/<USER>/home/<USER>/modbus/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/home/<USER>/modbus /home/<USER>/home/<USER>/modbus /home/<USER>/home/<USER>/modbus/build /home/<USER>/home/<USER>/modbus/build /home/<USER>/home/<USER>/modbus/build/CMakeFiles/test_tcp_comm.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/test_tcp_comm.dir/depend

