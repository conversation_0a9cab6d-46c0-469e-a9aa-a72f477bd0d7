/usr/bin/c++ -fPIC  -Wall -Wextra -Wpedantic -shared -Wl,-soname,libmodbus_protocol.so -o lib/libmodbus_protocol.so CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.o CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.o  -Wl,-rpath,/home/<USER>/home/<USER>/modbus/libmodbus_install/lib: /home/<USER>/home/<USER>/modbus/libmodbus_install/lib/libmodbus.so /usr/lib/x86_64-linux-gnu/libhiredis.so 
