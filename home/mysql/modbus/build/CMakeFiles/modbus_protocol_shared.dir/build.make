# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/home/<USER>/modbus

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/home/<USER>/modbus/build

# Include any dependencies generated for this target.
include CMakeFiles/modbus_protocol_shared.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/modbus_protocol_shared.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/modbus_protocol_shared.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/modbus_protocol_shared.dir/flags.make

CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.o: /home/<USER>/home/<USER>/modbus/src/utils/logger.cpp
CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.o -c /home/<USER>/home/<USER>/modbus/src/utils/logger.cpp

CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/utils/logger.cpp > CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/utils/logger.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.o: /home/<USER>/home/<USER>/modbus/src/utils/thread_pool.cpp
CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.o -c /home/<USER>/home/<USER>/modbus/src/utils/thread_pool.cpp

CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/utils/thread_pool.cpp > CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/utils/thread_pool.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.o: /home/<USER>/home/<USER>/modbus/src/utils/utils.cpp
CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.o -c /home/<USER>/home/<USER>/modbus/src/utils/utils.cpp

CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/utils/utils.cpp > CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/utils/utils.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.o: /home/<USER>/home/<USER>/modbus/src/comm/modbus_comm_base.cpp
CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.o -c /home/<USER>/home/<USER>/modbus/src/comm/modbus_comm_base.cpp

CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/comm/modbus_comm_base.cpp > CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/comm/modbus_comm_base.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.o: /home/<USER>/home/<USER>/modbus/src/comm/modbus_rtu_comm.cpp
CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.o -c /home/<USER>/home/<USER>/modbus/src/comm/modbus_rtu_comm.cpp

CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/comm/modbus_rtu_comm.cpp > CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/comm/modbus_rtu_comm.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.o: /home/<USER>/home/<USER>/modbus/src/comm/modbus_tcp_comm.cpp
CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.o -c /home/<USER>/home/<USER>/modbus/src/comm/modbus_tcp_comm.cpp

CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/comm/modbus_tcp_comm.cpp > CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/comm/modbus_tcp_comm.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.o: /home/<USER>/home/<USER>/modbus/src/config/config_manager.cpp
CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.o -c /home/<USER>/home/<USER>/modbus/src/config/config_manager.cpp

CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/config/config_manager.cpp > CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/config/config_manager.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.o: /home/<USER>/home/<USER>/modbus/src/config/xml_config_parser.cpp
CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.o -c /home/<USER>/home/<USER>/modbus/src/config/xml_config_parser.cpp

CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/config/xml_config_parser.cpp > CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/config/xml_config_parser.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.o: /home/<USER>/home/<USER>/modbus/src/config/point_table_loader.cpp
CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.o -c /home/<USER>/home/<USER>/modbus/src/config/point_table_loader.cpp

CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/config/point_table_loader.cpp > CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/config/point_table_loader.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.o: /home/<USER>/home/<USER>/modbus/src/config/modbus_point_parser.cpp
CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.o -c /home/<USER>/home/<USER>/modbus/src/config/modbus_point_parser.cpp

CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/config/modbus_point_parser.cpp > CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/config/modbus_point_parser.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.o: /home/<USER>/home/<USER>/modbus/src/redis/redis_publisher.cpp
CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.o -c /home/<USER>/home/<USER>/modbus/src/redis/redis_publisher.cpp

CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/redis/redis_publisher.cpp > CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/redis/redis_publisher.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.o: /home/<USER>/home/<USER>/modbus/src/redis/redis_subscriber.cpp
CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.o -c /home/<USER>/home/<USER>/modbus/src/redis/redis_subscriber.cpp

CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/redis/redis_subscriber.cpp > CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/redis/redis_subscriber.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.o: /home/<USER>/home/<USER>/modbus/src/redis/redis_manager.cpp
CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.o -c /home/<USER>/home/<USER>/modbus/src/redis/redis_manager.cpp

CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/redis/redis_manager.cpp > CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/redis/redis_manager.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.o: /home/<USER>/home/<USER>/modbus/src/redis/redis_message_handler.cpp
CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.o -c /home/<USER>/home/<USER>/modbus/src/redis/redis_message_handler.cpp

CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/redis/redis_message_handler.cpp > CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/redis/redis_message_handler.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.o: /home/<USER>/home/<USER>/modbus/src/data/data_point_manager.cpp
CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.o -c /home/<USER>/home/<USER>/modbus/src/data/data_point_manager.cpp

CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/data/data_point_manager.cpp > CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/data/data_point_manager.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.o: /home/<USER>/home/<USER>/modbus/src/device/device_manager.cpp
CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.o -c /home/<USER>/home/<USER>/modbus/src/device/device_manager.cpp

CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/device/device_manager.cpp > CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/device/device_manager.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.s

CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.o: CMakeFiles/modbus_protocol_shared.dir/flags.make
CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.o: /home/<USER>/home/<USER>/modbus/src/service/modbus_service.cpp
CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.o: CMakeFiles/modbus_protocol_shared.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.o -MF CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.o.d -o CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.o -c /home/<USER>/home/<USER>/modbus/src/service/modbus_service.cpp

CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/home/<USER>/modbus/src/service/modbus_service.cpp > CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.i

CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/home/<USER>/modbus/src/service/modbus_service.cpp -o CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.s

# Object files for target modbus_protocol_shared
modbus_protocol_shared_OBJECTS = \
"CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.o" \
"CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.o"

# External object files for target modbus_protocol_shared
modbus_protocol_shared_EXTERNAL_OBJECTS =

lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.o
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/build.make
lib/libmodbus_protocol.so: /home/<USER>/home/<USER>/modbus/libmodbus_install/lib/libmodbus.so
lib/libmodbus_protocol.so: /usr/lib/x86_64-linux-gnu/libhiredis.so
lib/libmodbus_protocol.so: CMakeFiles/modbus_protocol_shared.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Linking CXX shared library lib/libmodbus_protocol.so"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/modbus_protocol_shared.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/modbus_protocol_shared.dir/build: lib/libmodbus_protocol.so
.PHONY : CMakeFiles/modbus_protocol_shared.dir/build

CMakeFiles/modbus_protocol_shared.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/modbus_protocol_shared.dir/cmake_clean.cmake
.PHONY : CMakeFiles/modbus_protocol_shared.dir/clean

CMakeFiles/modbus_protocol_shared.dir/depend:
	cd /home/<USER>/home/<USER>/modbus/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/home/<USER>/modbus /home/<USER>/home/<USER>/modbus /home/<USER>/home/<USER>/modbus/build /home/<USER>/home/<USER>/modbus/build /home/<USER>/home/<USER>/modbus/build/CMakeFiles/modbus_protocol_shared.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/modbus_protocol_shared.dir/depend

