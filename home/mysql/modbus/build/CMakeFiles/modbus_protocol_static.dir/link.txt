/usr/bin/ar qc lib/libmodbus_protocol.a CMakeFiles/modbus_protocol_static.dir/src/utils/logger.cpp.o CMakeFiles/modbus_protocol_static.dir/src/utils/thread_pool.cpp.o CMakeFiles/modbus_protocol_static.dir/src/utils/utils.cpp.o CMakeFiles/modbus_protocol_static.dir/src/comm/modbus_comm_base.cpp.o CMakeFiles/modbus_protocol_static.dir/src/comm/modbus_rtu_comm.cpp.o CMakeFiles/modbus_protocol_static.dir/src/comm/modbus_tcp_comm.cpp.o CMakeFiles/modbus_protocol_static.dir/src/config/config_manager.cpp.o CMakeFiles/modbus_protocol_static.dir/src/config/xml_config_parser.cpp.o CMakeFiles/modbus_protocol_static.dir/src/config/point_table_loader.cpp.o CMakeFiles/modbus_protocol_static.dir/src/config/modbus_point_parser.cpp.o CMakeFiles/modbus_protocol_static.dir/src/redis/redis_publisher.cpp.o CMakeFiles/modbus_protocol_static.dir/src/redis/redis_subscriber.cpp.o CMakeFiles/modbus_protocol_static.dir/src/redis/redis_manager.cpp.o CMakeFiles/modbus_protocol_static.dir/src/redis/redis_message_handler.cpp.o CMakeFiles/modbus_protocol_static.dir/src/data/data_point_manager.cpp.o CMakeFiles/modbus_protocol_static.dir/src/device/device_manager.cpp.o CMakeFiles/modbus_protocol_static.dir/src/service/modbus_service.cpp.o
/usr/bin/ranlib lib/libmodbus_protocol.a
