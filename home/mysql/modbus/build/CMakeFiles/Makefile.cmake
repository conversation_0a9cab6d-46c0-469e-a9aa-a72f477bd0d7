# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/home/<USER>/home/<USER>/modbus/CMakeLists.txt"
  "CMakeFiles/3.28.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.28.3/CMakeSystem.cmake"
  "/home/<USER>/home/<USER>/modbus/modbus_protocol.pc.in"
  "/home/<USER>/home/<USER>/modbus/src/examples/CMakeLists.txt"
  "/home/<USER>/home/<USER>/modbus/tests/CMakeLists.txt"
  "/usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.28/Modules/CheckCXXSourceCompiles.cmake"
  "/usr/share/cmake-3.28/Modules/CheckIncludeFileCXX.cmake"
  "/usr/share/cmake-3.28/Modules/CheckLibraryExists.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.28/Modules/FindPkgConfig.cmake"
  "/usr/share/cmake-3.28/Modules/FindThreads.cmake"
  "/usr/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "modbus_protocol.pc"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "src/examples/CMakeFiles/CMakeDirectoryInformation.cmake"
  "tests/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/modbus_protocol_static.dir/DependInfo.cmake"
  "CMakeFiles/modbus_service.dir/DependInfo.cmake"
  "CMakeFiles/modbus_protocol_shared.dir/DependInfo.cmake"
  "CMakeFiles/test_logger.dir/DependInfo.cmake"
  "CMakeFiles/test_thread_pool.dir/DependInfo.cmake"
  "CMakeFiles/test_rtu_comm.dir/DependInfo.cmake"
  "CMakeFiles/test_tcp_comm.dir/DependInfo.cmake"
  "src/examples/CMakeFiles/example_rtu_basic.dir/DependInfo.cmake"
  "src/examples/CMakeFiles/example_tcp_basic.dir/DependInfo.cmake"
  "src/examples/CMakeFiles/example_config_parser.dir/DependInfo.cmake"
  "src/examples/CMakeFiles/example_redis_publisher.dir/DependInfo.cmake"
  "src/examples/CMakeFiles/example_complete_service.dir/DependInfo.cmake"
  "tests/CMakeFiles/test_basic.dir/DependInfo.cmake"
  )
