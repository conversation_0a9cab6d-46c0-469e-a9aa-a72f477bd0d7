# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/home/<USER>/modbus

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/home/<USER>/modbus/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/modbus_protocol_static.dir/all
all: CMakeFiles/modbus_service.dir/all
all: CMakeFiles/modbus_protocol_shared.dir/all
all: CMakeFiles/test_logger.dir/all
all: CMakeFiles/test_thread_pool.dir/all
all: CMakeFiles/test_rtu_comm.dir/all
all: CMakeFiles/test_tcp_comm.dir/all
all: src/examples/all
all: tests/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: src/examples/preinstall
preinstall: tests/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/modbus_protocol_static.dir/clean
clean: CMakeFiles/modbus_service.dir/clean
clean: CMakeFiles/modbus_protocol_shared.dir/clean
clean: CMakeFiles/test_logger.dir/clean
clean: CMakeFiles/test_thread_pool.dir/clean
clean: CMakeFiles/test_rtu_comm.dir/clean
clean: CMakeFiles/test_tcp_comm.dir/clean
clean: src/examples/clean
clean: tests/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory src/examples

# Recursive "all" directory target.
src/examples/all: src/examples/CMakeFiles/example_rtu_basic.dir/all
src/examples/all: src/examples/CMakeFiles/example_tcp_basic.dir/all
src/examples/all: src/examples/CMakeFiles/example_config_parser.dir/all
src/examples/all: src/examples/CMakeFiles/example_redis_publisher.dir/all
src/examples/all: src/examples/CMakeFiles/example_complete_service.dir/all
.PHONY : src/examples/all

# Recursive "preinstall" directory target.
src/examples/preinstall:
.PHONY : src/examples/preinstall

# Recursive "clean" directory target.
src/examples/clean: src/examples/CMakeFiles/example_rtu_basic.dir/clean
src/examples/clean: src/examples/CMakeFiles/example_tcp_basic.dir/clean
src/examples/clean: src/examples/CMakeFiles/example_config_parser.dir/clean
src/examples/clean: src/examples/CMakeFiles/example_redis_publisher.dir/clean
src/examples/clean: src/examples/CMakeFiles/example_complete_service.dir/clean
.PHONY : src/examples/clean

#=============================================================================
# Directory level rules for directory tests

# Recursive "all" directory target.
tests/all: tests/CMakeFiles/test_basic.dir/all
.PHONY : tests/all

# Recursive "preinstall" directory target.
tests/preinstall:
.PHONY : tests/preinstall

# Recursive "clean" directory target.
tests/clean: tests/CMakeFiles/test_basic.dir/clean
.PHONY : tests/clean

#=============================================================================
# Target rules for target CMakeFiles/modbus_protocol_static.dir

# All Build rule for target.
CMakeFiles/modbus_protocol_static.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46 "Built target modbus_protocol_static"
.PHONY : CMakeFiles/modbus_protocol_static.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/modbus_protocol_static.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 18
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/modbus_protocol_static.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 0
.PHONY : CMakeFiles/modbus_protocol_static.dir/rule

# Convenience name for target.
modbus_protocol_static: CMakeFiles/modbus_protocol_static.dir/rule
.PHONY : modbus_protocol_static

# clean rule for target.
CMakeFiles/modbus_protocol_static.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/clean
.PHONY : CMakeFiles/modbus_protocol_static.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/modbus_service.dir

# All Build rule for target.
CMakeFiles/modbus_service.dir/all: CMakeFiles/modbus_protocol_static.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_service.dir/build.make CMakeFiles/modbus_service.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_service.dir/build.make CMakeFiles/modbus_service.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=47,48 "Built target modbus_service"
.PHONY : CMakeFiles/modbus_service.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/modbus_service.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/modbus_service.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 0
.PHONY : CMakeFiles/modbus_service.dir/rule

# Convenience name for target.
modbus_service: CMakeFiles/modbus_service.dir/rule
.PHONY : modbus_service

# clean rule for target.
CMakeFiles/modbus_service.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_service.dir/build.make CMakeFiles/modbus_service.dir/clean
.PHONY : CMakeFiles/modbus_service.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/modbus_protocol_shared.dir

# All Build rule for target.
CMakeFiles/modbus_protocol_shared.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28 "Built target modbus_protocol_shared"
.PHONY : CMakeFiles/modbus_protocol_shared.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/modbus_protocol_shared.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 18
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/modbus_protocol_shared.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 0
.PHONY : CMakeFiles/modbus_protocol_shared.dir/rule

# Convenience name for target.
modbus_protocol_shared: CMakeFiles/modbus_protocol_shared.dir/rule
.PHONY : modbus_protocol_shared

# clean rule for target.
CMakeFiles/modbus_protocol_shared.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/clean
.PHONY : CMakeFiles/modbus_protocol_shared.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_logger.dir

# All Build rule for target.
CMakeFiles/test_logger.dir/all: CMakeFiles/modbus_protocol_static.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_logger.dir/build.make CMakeFiles/test_logger.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_logger.dir/build.make CMakeFiles/test_logger.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=51,52 "Built target test_logger"
.PHONY : CMakeFiles/test_logger.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_logger.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_logger.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 0
.PHONY : CMakeFiles/test_logger.dir/rule

# Convenience name for target.
test_logger: CMakeFiles/test_logger.dir/rule
.PHONY : test_logger

# clean rule for target.
CMakeFiles/test_logger.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_logger.dir/build.make CMakeFiles/test_logger.dir/clean
.PHONY : CMakeFiles/test_logger.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_thread_pool.dir

# All Build rule for target.
CMakeFiles/test_thread_pool.dir/all: CMakeFiles/modbus_protocol_static.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_thread_pool.dir/build.make CMakeFiles/test_thread_pool.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_thread_pool.dir/build.make CMakeFiles/test_thread_pool.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=57,58 "Built target test_thread_pool"
.PHONY : CMakeFiles/test_thread_pool.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_thread_pool.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_thread_pool.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 0
.PHONY : CMakeFiles/test_thread_pool.dir/rule

# Convenience name for target.
test_thread_pool: CMakeFiles/test_thread_pool.dir/rule
.PHONY : test_thread_pool

# clean rule for target.
CMakeFiles/test_thread_pool.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_thread_pool.dir/build.make CMakeFiles/test_thread_pool.dir/clean
.PHONY : CMakeFiles/test_thread_pool.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_rtu_comm.dir

# All Build rule for target.
CMakeFiles/test_rtu_comm.dir/all: CMakeFiles/modbus_protocol_static.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_rtu_comm.dir/build.make CMakeFiles/test_rtu_comm.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_rtu_comm.dir/build.make CMakeFiles/test_rtu_comm.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=53,54 "Built target test_rtu_comm"
.PHONY : CMakeFiles/test_rtu_comm.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_rtu_comm.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_rtu_comm.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 0
.PHONY : CMakeFiles/test_rtu_comm.dir/rule

# Convenience name for target.
test_rtu_comm: CMakeFiles/test_rtu_comm.dir/rule
.PHONY : test_rtu_comm

# clean rule for target.
CMakeFiles/test_rtu_comm.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_rtu_comm.dir/build.make CMakeFiles/test_rtu_comm.dir/clean
.PHONY : CMakeFiles/test_rtu_comm.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_tcp_comm.dir

# All Build rule for target.
CMakeFiles/test_tcp_comm.dir/all: CMakeFiles/modbus_protocol_static.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_tcp_comm.dir/build.make CMakeFiles/test_tcp_comm.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_tcp_comm.dir/build.make CMakeFiles/test_tcp_comm.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=55,56 "Built target test_tcp_comm"
.PHONY : CMakeFiles/test_tcp_comm.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_tcp_comm.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_tcp_comm.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 0
.PHONY : CMakeFiles/test_tcp_comm.dir/rule

# Convenience name for target.
test_tcp_comm: CMakeFiles/test_tcp_comm.dir/rule
.PHONY : test_tcp_comm

# clean rule for target.
CMakeFiles/test_tcp_comm.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_tcp_comm.dir/build.make CMakeFiles/test_tcp_comm.dir/clean
.PHONY : CMakeFiles/test_tcp_comm.dir/clean

#=============================================================================
# Target rules for target src/examples/CMakeFiles/example_rtu_basic.dir

# All Build rule for target.
src/examples/CMakeFiles/example_rtu_basic.dir/all: CMakeFiles/modbus_protocol_static.dir/all
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_rtu_basic.dir/build.make src/examples/CMakeFiles/example_rtu_basic.dir/depend
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_rtu_basic.dir/build.make src/examples/CMakeFiles/example_rtu_basic.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=7,8 "Built target example_rtu_basic"
.PHONY : src/examples/CMakeFiles/example_rtu_basic.dir/all

# Build rule for subdir invocation for target.
src/examples/CMakeFiles/example_rtu_basic.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/examples/CMakeFiles/example_rtu_basic.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 0
.PHONY : src/examples/CMakeFiles/example_rtu_basic.dir/rule

# Convenience name for target.
example_rtu_basic: src/examples/CMakeFiles/example_rtu_basic.dir/rule
.PHONY : example_rtu_basic

# clean rule for target.
src/examples/CMakeFiles/example_rtu_basic.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_rtu_basic.dir/build.make src/examples/CMakeFiles/example_rtu_basic.dir/clean
.PHONY : src/examples/CMakeFiles/example_rtu_basic.dir/clean

#=============================================================================
# Target rules for target src/examples/CMakeFiles/example_tcp_basic.dir

# All Build rule for target.
src/examples/CMakeFiles/example_tcp_basic.dir/all: CMakeFiles/modbus_protocol_static.dir/all
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_tcp_basic.dir/build.make src/examples/CMakeFiles/example_tcp_basic.dir/depend
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_tcp_basic.dir/build.make src/examples/CMakeFiles/example_tcp_basic.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=9,10 "Built target example_tcp_basic"
.PHONY : src/examples/CMakeFiles/example_tcp_basic.dir/all

# Build rule for subdir invocation for target.
src/examples/CMakeFiles/example_tcp_basic.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/examples/CMakeFiles/example_tcp_basic.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 0
.PHONY : src/examples/CMakeFiles/example_tcp_basic.dir/rule

# Convenience name for target.
example_tcp_basic: src/examples/CMakeFiles/example_tcp_basic.dir/rule
.PHONY : example_tcp_basic

# clean rule for target.
src/examples/CMakeFiles/example_tcp_basic.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_tcp_basic.dir/build.make src/examples/CMakeFiles/example_tcp_basic.dir/clean
.PHONY : src/examples/CMakeFiles/example_tcp_basic.dir/clean

#=============================================================================
# Target rules for target src/examples/CMakeFiles/example_config_parser.dir

# All Build rule for target.
src/examples/CMakeFiles/example_config_parser.dir/all: CMakeFiles/modbus_protocol_static.dir/all
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_config_parser.dir/build.make src/examples/CMakeFiles/example_config_parser.dir/depend
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_config_parser.dir/build.make src/examples/CMakeFiles/example_config_parser.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=3,4 "Built target example_config_parser"
.PHONY : src/examples/CMakeFiles/example_config_parser.dir/all

# Build rule for subdir invocation for target.
src/examples/CMakeFiles/example_config_parser.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/examples/CMakeFiles/example_config_parser.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 0
.PHONY : src/examples/CMakeFiles/example_config_parser.dir/rule

# Convenience name for target.
example_config_parser: src/examples/CMakeFiles/example_config_parser.dir/rule
.PHONY : example_config_parser

# clean rule for target.
src/examples/CMakeFiles/example_config_parser.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_config_parser.dir/build.make src/examples/CMakeFiles/example_config_parser.dir/clean
.PHONY : src/examples/CMakeFiles/example_config_parser.dir/clean

#=============================================================================
# Target rules for target src/examples/CMakeFiles/example_redis_publisher.dir

# All Build rule for target.
src/examples/CMakeFiles/example_redis_publisher.dir/all: CMakeFiles/modbus_protocol_static.dir/all
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_redis_publisher.dir/build.make src/examples/CMakeFiles/example_redis_publisher.dir/depend
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_redis_publisher.dir/build.make src/examples/CMakeFiles/example_redis_publisher.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=5,6 "Built target example_redis_publisher"
.PHONY : src/examples/CMakeFiles/example_redis_publisher.dir/all

# Build rule for subdir invocation for target.
src/examples/CMakeFiles/example_redis_publisher.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/examples/CMakeFiles/example_redis_publisher.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 0
.PHONY : src/examples/CMakeFiles/example_redis_publisher.dir/rule

# Convenience name for target.
example_redis_publisher: src/examples/CMakeFiles/example_redis_publisher.dir/rule
.PHONY : example_redis_publisher

# clean rule for target.
src/examples/CMakeFiles/example_redis_publisher.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_redis_publisher.dir/build.make src/examples/CMakeFiles/example_redis_publisher.dir/clean
.PHONY : src/examples/CMakeFiles/example_redis_publisher.dir/clean

#=============================================================================
# Target rules for target src/examples/CMakeFiles/example_complete_service.dir

# All Build rule for target.
src/examples/CMakeFiles/example_complete_service.dir/all: CMakeFiles/modbus_protocol_static.dir/all
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_complete_service.dir/build.make src/examples/CMakeFiles/example_complete_service.dir/depend
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_complete_service.dir/build.make src/examples/CMakeFiles/example_complete_service.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=1,2 "Built target example_complete_service"
.PHONY : src/examples/CMakeFiles/example_complete_service.dir/all

# Build rule for subdir invocation for target.
src/examples/CMakeFiles/example_complete_service.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/examples/CMakeFiles/example_complete_service.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 0
.PHONY : src/examples/CMakeFiles/example_complete_service.dir/rule

# Convenience name for target.
example_complete_service: src/examples/CMakeFiles/example_complete_service.dir/rule
.PHONY : example_complete_service

# clean rule for target.
src/examples/CMakeFiles/example_complete_service.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_complete_service.dir/build.make src/examples/CMakeFiles/example_complete_service.dir/clean
.PHONY : src/examples/CMakeFiles/example_complete_service.dir/clean

#=============================================================================
# Target rules for target tests/CMakeFiles/test_basic.dir

# All Build rule for target.
tests/CMakeFiles/test_basic.dir/all: CMakeFiles/modbus_protocol_static.dir/all
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/test_basic.dir/build.make tests/CMakeFiles/test_basic.dir/depend
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/test_basic.dir/build.make tests/CMakeFiles/test_basic.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/home/<USER>/modbus/build/CMakeFiles --progress-num=49,50 "Built target test_basic"
.PHONY : tests/CMakeFiles/test_basic.dir/all

# Build rule for subdir invocation for target.
tests/CMakeFiles/test_basic.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests/CMakeFiles/test_basic.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 0
.PHONY : tests/CMakeFiles/test_basic.dir/rule

# Convenience name for target.
test_basic: tests/CMakeFiles/test_basic.dir/rule
.PHONY : test_basic

# clean rule for target.
tests/CMakeFiles/test_basic.dir/clean:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/test_basic.dir/build.make tests/CMakeFiles/test_basic.dir/clean
.PHONY : tests/CMakeFiles/test_basic.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

