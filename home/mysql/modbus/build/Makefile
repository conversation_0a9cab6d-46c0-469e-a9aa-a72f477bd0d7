# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/home/<USER>/modbus

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/home/<USER>/modbus/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles /home/<USER>/home/<USER>/modbus/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/home/<USER>/modbus/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named modbus_protocol_static

# Build rule for target.
modbus_protocol_static: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 modbus_protocol_static
.PHONY : modbus_protocol_static

# fast build rule for target.
modbus_protocol_static/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/build
.PHONY : modbus_protocol_static/fast

#=============================================================================
# Target rules for targets named modbus_service

# Build rule for target.
modbus_service: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 modbus_service
.PHONY : modbus_service

# fast build rule for target.
modbus_service/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_service.dir/build.make CMakeFiles/modbus_service.dir/build
.PHONY : modbus_service/fast

#=============================================================================
# Target rules for targets named modbus_protocol_shared

# Build rule for target.
modbus_protocol_shared: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 modbus_protocol_shared
.PHONY : modbus_protocol_shared

# fast build rule for target.
modbus_protocol_shared/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/build
.PHONY : modbus_protocol_shared/fast

#=============================================================================
# Target rules for targets named test_logger

# Build rule for target.
test_logger: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_logger
.PHONY : test_logger

# fast build rule for target.
test_logger/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_logger.dir/build.make CMakeFiles/test_logger.dir/build
.PHONY : test_logger/fast

#=============================================================================
# Target rules for targets named test_thread_pool

# Build rule for target.
test_thread_pool: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_thread_pool
.PHONY : test_thread_pool

# fast build rule for target.
test_thread_pool/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_thread_pool.dir/build.make CMakeFiles/test_thread_pool.dir/build
.PHONY : test_thread_pool/fast

#=============================================================================
# Target rules for targets named test_rtu_comm

# Build rule for target.
test_rtu_comm: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_rtu_comm
.PHONY : test_rtu_comm

# fast build rule for target.
test_rtu_comm/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_rtu_comm.dir/build.make CMakeFiles/test_rtu_comm.dir/build
.PHONY : test_rtu_comm/fast

#=============================================================================
# Target rules for targets named test_tcp_comm

# Build rule for target.
test_tcp_comm: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_tcp_comm
.PHONY : test_tcp_comm

# fast build rule for target.
test_tcp_comm/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_tcp_comm.dir/build.make CMakeFiles/test_tcp_comm.dir/build
.PHONY : test_tcp_comm/fast

#=============================================================================
# Target rules for targets named example_rtu_basic

# Build rule for target.
example_rtu_basic: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 example_rtu_basic
.PHONY : example_rtu_basic

# fast build rule for target.
example_rtu_basic/fast:
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_rtu_basic.dir/build.make src/examples/CMakeFiles/example_rtu_basic.dir/build
.PHONY : example_rtu_basic/fast

#=============================================================================
# Target rules for targets named example_tcp_basic

# Build rule for target.
example_tcp_basic: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 example_tcp_basic
.PHONY : example_tcp_basic

# fast build rule for target.
example_tcp_basic/fast:
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_tcp_basic.dir/build.make src/examples/CMakeFiles/example_tcp_basic.dir/build
.PHONY : example_tcp_basic/fast

#=============================================================================
# Target rules for targets named example_config_parser

# Build rule for target.
example_config_parser: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 example_config_parser
.PHONY : example_config_parser

# fast build rule for target.
example_config_parser/fast:
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_config_parser.dir/build.make src/examples/CMakeFiles/example_config_parser.dir/build
.PHONY : example_config_parser/fast

#=============================================================================
# Target rules for targets named example_redis_publisher

# Build rule for target.
example_redis_publisher: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 example_redis_publisher
.PHONY : example_redis_publisher

# fast build rule for target.
example_redis_publisher/fast:
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_redis_publisher.dir/build.make src/examples/CMakeFiles/example_redis_publisher.dir/build
.PHONY : example_redis_publisher/fast

#=============================================================================
# Target rules for targets named example_complete_service

# Build rule for target.
example_complete_service: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 example_complete_service
.PHONY : example_complete_service

# fast build rule for target.
example_complete_service/fast:
	$(MAKE) $(MAKESILENT) -f src/examples/CMakeFiles/example_complete_service.dir/build.make src/examples/CMakeFiles/example_complete_service.dir/build
.PHONY : example_complete_service/fast

#=============================================================================
# Target rules for targets named test_basic

# Build rule for target.
test_basic: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_basic
.PHONY : test_basic

# fast build rule for target.
test_basic/fast:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/test_basic.dir/build.make tests/CMakeFiles/test_basic.dir/build
.PHONY : test_basic/fast

src/comm/modbus_comm_base.o: src/comm/modbus_comm_base.cpp.o
.PHONY : src/comm/modbus_comm_base.o

# target to build an object file
src/comm/modbus_comm_base.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/comm/modbus_comm_base.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.o
.PHONY : src/comm/modbus_comm_base.cpp.o

src/comm/modbus_comm_base.i: src/comm/modbus_comm_base.cpp.i
.PHONY : src/comm/modbus_comm_base.i

# target to preprocess a source file
src/comm/modbus_comm_base.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/comm/modbus_comm_base.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.i
.PHONY : src/comm/modbus_comm_base.cpp.i

src/comm/modbus_comm_base.s: src/comm/modbus_comm_base.cpp.s
.PHONY : src/comm/modbus_comm_base.s

# target to generate assembly for a file
src/comm/modbus_comm_base.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/comm/modbus_comm_base.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_comm_base.cpp.s
.PHONY : src/comm/modbus_comm_base.cpp.s

src/comm/modbus_rtu_comm.o: src/comm/modbus_rtu_comm.cpp.o
.PHONY : src/comm/modbus_rtu_comm.o

# target to build an object file
src/comm/modbus_rtu_comm.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/comm/modbus_rtu_comm.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.o
.PHONY : src/comm/modbus_rtu_comm.cpp.o

src/comm/modbus_rtu_comm.i: src/comm/modbus_rtu_comm.cpp.i
.PHONY : src/comm/modbus_rtu_comm.i

# target to preprocess a source file
src/comm/modbus_rtu_comm.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/comm/modbus_rtu_comm.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.i
.PHONY : src/comm/modbus_rtu_comm.cpp.i

src/comm/modbus_rtu_comm.s: src/comm/modbus_rtu_comm.cpp.s
.PHONY : src/comm/modbus_rtu_comm.s

# target to generate assembly for a file
src/comm/modbus_rtu_comm.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/comm/modbus_rtu_comm.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_rtu_comm.cpp.s
.PHONY : src/comm/modbus_rtu_comm.cpp.s

src/comm/modbus_tcp_comm.o: src/comm/modbus_tcp_comm.cpp.o
.PHONY : src/comm/modbus_tcp_comm.o

# target to build an object file
src/comm/modbus_tcp_comm.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/comm/modbus_tcp_comm.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.o
.PHONY : src/comm/modbus_tcp_comm.cpp.o

src/comm/modbus_tcp_comm.i: src/comm/modbus_tcp_comm.cpp.i
.PHONY : src/comm/modbus_tcp_comm.i

# target to preprocess a source file
src/comm/modbus_tcp_comm.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/comm/modbus_tcp_comm.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.i
.PHONY : src/comm/modbus_tcp_comm.cpp.i

src/comm/modbus_tcp_comm.s: src/comm/modbus_tcp_comm.cpp.s
.PHONY : src/comm/modbus_tcp_comm.s

# target to generate assembly for a file
src/comm/modbus_tcp_comm.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/comm/modbus_tcp_comm.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/comm/modbus_tcp_comm.cpp.s
.PHONY : src/comm/modbus_tcp_comm.cpp.s

src/config/config_manager.o: src/config/config_manager.cpp.o
.PHONY : src/config/config_manager.o

# target to build an object file
src/config/config_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/config/config_manager.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.o
.PHONY : src/config/config_manager.cpp.o

src/config/config_manager.i: src/config/config_manager.cpp.i
.PHONY : src/config/config_manager.i

# target to preprocess a source file
src/config/config_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/config/config_manager.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.i
.PHONY : src/config/config_manager.cpp.i

src/config/config_manager.s: src/config/config_manager.cpp.s
.PHONY : src/config/config_manager.s

# target to generate assembly for a file
src/config/config_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/config/config_manager.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/config/config_manager.cpp.s
.PHONY : src/config/config_manager.cpp.s

src/config/modbus_point_parser.o: src/config/modbus_point_parser.cpp.o
.PHONY : src/config/modbus_point_parser.o

# target to build an object file
src/config/modbus_point_parser.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/config/modbus_point_parser.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.o
.PHONY : src/config/modbus_point_parser.cpp.o

src/config/modbus_point_parser.i: src/config/modbus_point_parser.cpp.i
.PHONY : src/config/modbus_point_parser.i

# target to preprocess a source file
src/config/modbus_point_parser.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/config/modbus_point_parser.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.i
.PHONY : src/config/modbus_point_parser.cpp.i

src/config/modbus_point_parser.s: src/config/modbus_point_parser.cpp.s
.PHONY : src/config/modbus_point_parser.s

# target to generate assembly for a file
src/config/modbus_point_parser.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/config/modbus_point_parser.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/config/modbus_point_parser.cpp.s
.PHONY : src/config/modbus_point_parser.cpp.s

src/config/point_table_loader.o: src/config/point_table_loader.cpp.o
.PHONY : src/config/point_table_loader.o

# target to build an object file
src/config/point_table_loader.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/config/point_table_loader.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.o
.PHONY : src/config/point_table_loader.cpp.o

src/config/point_table_loader.i: src/config/point_table_loader.cpp.i
.PHONY : src/config/point_table_loader.i

# target to preprocess a source file
src/config/point_table_loader.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/config/point_table_loader.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.i
.PHONY : src/config/point_table_loader.cpp.i

src/config/point_table_loader.s: src/config/point_table_loader.cpp.s
.PHONY : src/config/point_table_loader.s

# target to generate assembly for a file
src/config/point_table_loader.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/config/point_table_loader.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/config/point_table_loader.cpp.s
.PHONY : src/config/point_table_loader.cpp.s

src/config/xml_config_parser.o: src/config/xml_config_parser.cpp.o
.PHONY : src/config/xml_config_parser.o

# target to build an object file
src/config/xml_config_parser.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/config/xml_config_parser.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.o
.PHONY : src/config/xml_config_parser.cpp.o

src/config/xml_config_parser.i: src/config/xml_config_parser.cpp.i
.PHONY : src/config/xml_config_parser.i

# target to preprocess a source file
src/config/xml_config_parser.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/config/xml_config_parser.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.i
.PHONY : src/config/xml_config_parser.cpp.i

src/config/xml_config_parser.s: src/config/xml_config_parser.cpp.s
.PHONY : src/config/xml_config_parser.s

# target to generate assembly for a file
src/config/xml_config_parser.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/config/xml_config_parser.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/config/xml_config_parser.cpp.s
.PHONY : src/config/xml_config_parser.cpp.s

src/data/data_point_manager.o: src/data/data_point_manager.cpp.o
.PHONY : src/data/data_point_manager.o

# target to build an object file
src/data/data_point_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/data/data_point_manager.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.o
.PHONY : src/data/data_point_manager.cpp.o

src/data/data_point_manager.i: src/data/data_point_manager.cpp.i
.PHONY : src/data/data_point_manager.i

# target to preprocess a source file
src/data/data_point_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/data/data_point_manager.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.i
.PHONY : src/data/data_point_manager.cpp.i

src/data/data_point_manager.s: src/data/data_point_manager.cpp.s
.PHONY : src/data/data_point_manager.s

# target to generate assembly for a file
src/data/data_point_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/data/data_point_manager.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/data/data_point_manager.cpp.s
.PHONY : src/data/data_point_manager.cpp.s

src/device/device_manager.o: src/device/device_manager.cpp.o
.PHONY : src/device/device_manager.o

# target to build an object file
src/device/device_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/device/device_manager.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.o
.PHONY : src/device/device_manager.cpp.o

src/device/device_manager.i: src/device/device_manager.cpp.i
.PHONY : src/device/device_manager.i

# target to preprocess a source file
src/device/device_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/device/device_manager.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.i
.PHONY : src/device/device_manager.cpp.i

src/device/device_manager.s: src/device/device_manager.cpp.s
.PHONY : src/device/device_manager.s

# target to generate assembly for a file
src/device/device_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/device/device_manager.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/device/device_manager.cpp.s
.PHONY : src/device/device_manager.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_service.dir/build.make CMakeFiles/modbus_service.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_service.dir/build.make CMakeFiles/modbus_service.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_service.dir/build.make CMakeFiles/modbus_service.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/redis/redis_manager.o: src/redis/redis_manager.cpp.o
.PHONY : src/redis/redis_manager.o

# target to build an object file
src/redis/redis_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/redis/redis_manager.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.o
.PHONY : src/redis/redis_manager.cpp.o

src/redis/redis_manager.i: src/redis/redis_manager.cpp.i
.PHONY : src/redis/redis_manager.i

# target to preprocess a source file
src/redis/redis_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/redis/redis_manager.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.i
.PHONY : src/redis/redis_manager.cpp.i

src/redis/redis_manager.s: src/redis/redis_manager.cpp.s
.PHONY : src/redis/redis_manager.s

# target to generate assembly for a file
src/redis/redis_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/redis/redis_manager.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_manager.cpp.s
.PHONY : src/redis/redis_manager.cpp.s

src/redis/redis_message_handler.o: src/redis/redis_message_handler.cpp.o
.PHONY : src/redis/redis_message_handler.o

# target to build an object file
src/redis/redis_message_handler.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/redis/redis_message_handler.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.o
.PHONY : src/redis/redis_message_handler.cpp.o

src/redis/redis_message_handler.i: src/redis/redis_message_handler.cpp.i
.PHONY : src/redis/redis_message_handler.i

# target to preprocess a source file
src/redis/redis_message_handler.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/redis/redis_message_handler.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.i
.PHONY : src/redis/redis_message_handler.cpp.i

src/redis/redis_message_handler.s: src/redis/redis_message_handler.cpp.s
.PHONY : src/redis/redis_message_handler.s

# target to generate assembly for a file
src/redis/redis_message_handler.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/redis/redis_message_handler.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_message_handler.cpp.s
.PHONY : src/redis/redis_message_handler.cpp.s

src/redis/redis_publisher.o: src/redis/redis_publisher.cpp.o
.PHONY : src/redis/redis_publisher.o

# target to build an object file
src/redis/redis_publisher.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/redis/redis_publisher.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.o
.PHONY : src/redis/redis_publisher.cpp.o

src/redis/redis_publisher.i: src/redis/redis_publisher.cpp.i
.PHONY : src/redis/redis_publisher.i

# target to preprocess a source file
src/redis/redis_publisher.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/redis/redis_publisher.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.i
.PHONY : src/redis/redis_publisher.cpp.i

src/redis/redis_publisher.s: src/redis/redis_publisher.cpp.s
.PHONY : src/redis/redis_publisher.s

# target to generate assembly for a file
src/redis/redis_publisher.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/redis/redis_publisher.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_publisher.cpp.s
.PHONY : src/redis/redis_publisher.cpp.s

src/redis/redis_subscriber.o: src/redis/redis_subscriber.cpp.o
.PHONY : src/redis/redis_subscriber.o

# target to build an object file
src/redis/redis_subscriber.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/redis/redis_subscriber.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.o
.PHONY : src/redis/redis_subscriber.cpp.o

src/redis/redis_subscriber.i: src/redis/redis_subscriber.cpp.i
.PHONY : src/redis/redis_subscriber.i

# target to preprocess a source file
src/redis/redis_subscriber.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/redis/redis_subscriber.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.i
.PHONY : src/redis/redis_subscriber.cpp.i

src/redis/redis_subscriber.s: src/redis/redis_subscriber.cpp.s
.PHONY : src/redis/redis_subscriber.s

# target to generate assembly for a file
src/redis/redis_subscriber.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/redis/redis_subscriber.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/redis/redis_subscriber.cpp.s
.PHONY : src/redis/redis_subscriber.cpp.s

src/service/modbus_service.o: src/service/modbus_service.cpp.o
.PHONY : src/service/modbus_service.o

# target to build an object file
src/service/modbus_service.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/service/modbus_service.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.o
.PHONY : src/service/modbus_service.cpp.o

src/service/modbus_service.i: src/service/modbus_service.cpp.i
.PHONY : src/service/modbus_service.i

# target to preprocess a source file
src/service/modbus_service.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/service/modbus_service.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.i
.PHONY : src/service/modbus_service.cpp.i

src/service/modbus_service.s: src/service/modbus_service.cpp.s
.PHONY : src/service/modbus_service.s

# target to generate assembly for a file
src/service/modbus_service.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/service/modbus_service.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/service/modbus_service.cpp.s
.PHONY : src/service/modbus_service.cpp.s

src/tests/test_logger.o: src/tests/test_logger.cpp.o
.PHONY : src/tests/test_logger.o

# target to build an object file
src/tests/test_logger.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_logger.dir/build.make CMakeFiles/test_logger.dir/src/tests/test_logger.cpp.o
.PHONY : src/tests/test_logger.cpp.o

src/tests/test_logger.i: src/tests/test_logger.cpp.i
.PHONY : src/tests/test_logger.i

# target to preprocess a source file
src/tests/test_logger.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_logger.dir/build.make CMakeFiles/test_logger.dir/src/tests/test_logger.cpp.i
.PHONY : src/tests/test_logger.cpp.i

src/tests/test_logger.s: src/tests/test_logger.cpp.s
.PHONY : src/tests/test_logger.s

# target to generate assembly for a file
src/tests/test_logger.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_logger.dir/build.make CMakeFiles/test_logger.dir/src/tests/test_logger.cpp.s
.PHONY : src/tests/test_logger.cpp.s

src/tests/test_rtu_comm.o: src/tests/test_rtu_comm.cpp.o
.PHONY : src/tests/test_rtu_comm.o

# target to build an object file
src/tests/test_rtu_comm.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_rtu_comm.dir/build.make CMakeFiles/test_rtu_comm.dir/src/tests/test_rtu_comm.cpp.o
.PHONY : src/tests/test_rtu_comm.cpp.o

src/tests/test_rtu_comm.i: src/tests/test_rtu_comm.cpp.i
.PHONY : src/tests/test_rtu_comm.i

# target to preprocess a source file
src/tests/test_rtu_comm.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_rtu_comm.dir/build.make CMakeFiles/test_rtu_comm.dir/src/tests/test_rtu_comm.cpp.i
.PHONY : src/tests/test_rtu_comm.cpp.i

src/tests/test_rtu_comm.s: src/tests/test_rtu_comm.cpp.s
.PHONY : src/tests/test_rtu_comm.s

# target to generate assembly for a file
src/tests/test_rtu_comm.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_rtu_comm.dir/build.make CMakeFiles/test_rtu_comm.dir/src/tests/test_rtu_comm.cpp.s
.PHONY : src/tests/test_rtu_comm.cpp.s

src/tests/test_tcp_comm.o: src/tests/test_tcp_comm.cpp.o
.PHONY : src/tests/test_tcp_comm.o

# target to build an object file
src/tests/test_tcp_comm.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_tcp_comm.dir/build.make CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.o
.PHONY : src/tests/test_tcp_comm.cpp.o

src/tests/test_tcp_comm.i: src/tests/test_tcp_comm.cpp.i
.PHONY : src/tests/test_tcp_comm.i

# target to preprocess a source file
src/tests/test_tcp_comm.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_tcp_comm.dir/build.make CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.i
.PHONY : src/tests/test_tcp_comm.cpp.i

src/tests/test_tcp_comm.s: src/tests/test_tcp_comm.cpp.s
.PHONY : src/tests/test_tcp_comm.s

# target to generate assembly for a file
src/tests/test_tcp_comm.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_tcp_comm.dir/build.make CMakeFiles/test_tcp_comm.dir/src/tests/test_tcp_comm.cpp.s
.PHONY : src/tests/test_tcp_comm.cpp.s

src/tests/test_thread_pool.o: src/tests/test_thread_pool.cpp.o
.PHONY : src/tests/test_thread_pool.o

# target to build an object file
src/tests/test_thread_pool.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_thread_pool.dir/build.make CMakeFiles/test_thread_pool.dir/src/tests/test_thread_pool.cpp.o
.PHONY : src/tests/test_thread_pool.cpp.o

src/tests/test_thread_pool.i: src/tests/test_thread_pool.cpp.i
.PHONY : src/tests/test_thread_pool.i

# target to preprocess a source file
src/tests/test_thread_pool.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_thread_pool.dir/build.make CMakeFiles/test_thread_pool.dir/src/tests/test_thread_pool.cpp.i
.PHONY : src/tests/test_thread_pool.cpp.i

src/tests/test_thread_pool.s: src/tests/test_thread_pool.cpp.s
.PHONY : src/tests/test_thread_pool.s

# target to generate assembly for a file
src/tests/test_thread_pool.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_thread_pool.dir/build.make CMakeFiles/test_thread_pool.dir/src/tests/test_thread_pool.cpp.s
.PHONY : src/tests/test_thread_pool.cpp.s

src/utils/logger.o: src/utils/logger.cpp.o
.PHONY : src/utils/logger.o

# target to build an object file
src/utils/logger.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/utils/logger.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.o
.PHONY : src/utils/logger.cpp.o

src/utils/logger.i: src/utils/logger.cpp.i
.PHONY : src/utils/logger.i

# target to preprocess a source file
src/utils/logger.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/utils/logger.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.i
.PHONY : src/utils/logger.cpp.i

src/utils/logger.s: src/utils/logger.cpp.s
.PHONY : src/utils/logger.s

# target to generate assembly for a file
src/utils/logger.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/utils/logger.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/utils/logger.cpp.s
.PHONY : src/utils/logger.cpp.s

src/utils/thread_pool.o: src/utils/thread_pool.cpp.o
.PHONY : src/utils/thread_pool.o

# target to build an object file
src/utils/thread_pool.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/utils/thread_pool.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.o
.PHONY : src/utils/thread_pool.cpp.o

src/utils/thread_pool.i: src/utils/thread_pool.cpp.i
.PHONY : src/utils/thread_pool.i

# target to preprocess a source file
src/utils/thread_pool.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/utils/thread_pool.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.i
.PHONY : src/utils/thread_pool.cpp.i

src/utils/thread_pool.s: src/utils/thread_pool.cpp.s
.PHONY : src/utils/thread_pool.s

# target to generate assembly for a file
src/utils/thread_pool.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/utils/thread_pool.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/utils/thread_pool.cpp.s
.PHONY : src/utils/thread_pool.cpp.s

src/utils/utils.o: src/utils/utils.cpp.o
.PHONY : src/utils/utils.o

# target to build an object file
src/utils/utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/utils/utils.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.o
.PHONY : src/utils/utils.cpp.o

src/utils/utils.i: src/utils/utils.cpp.i
.PHONY : src/utils/utils.i

# target to preprocess a source file
src/utils/utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/utils/utils.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.i
.PHONY : src/utils/utils.cpp.i

src/utils/utils.s: src/utils/utils.cpp.s
.PHONY : src/utils/utils.s

# target to generate assembly for a file
src/utils/utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_static.dir/build.make CMakeFiles/modbus_protocol_static.dir/src/utils/utils.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/modbus_protocol_shared.dir/build.make CMakeFiles/modbus_protocol_shared.dir/src/utils/utils.cpp.s
.PHONY : src/utils/utils.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... example_complete_service"
	@echo "... example_config_parser"
	@echo "... example_redis_publisher"
	@echo "... example_rtu_basic"
	@echo "... example_tcp_basic"
	@echo "... modbus_protocol_shared"
	@echo "... modbus_protocol_static"
	@echo "... modbus_service"
	@echo "... test_basic"
	@echo "... test_logger"
	@echo "... test_rtu_comm"
	@echo "... test_tcp_comm"
	@echo "... test_thread_pool"
	@echo "... src/comm/modbus_comm_base.o"
	@echo "... src/comm/modbus_comm_base.i"
	@echo "... src/comm/modbus_comm_base.s"
	@echo "... src/comm/modbus_rtu_comm.o"
	@echo "... src/comm/modbus_rtu_comm.i"
	@echo "... src/comm/modbus_rtu_comm.s"
	@echo "... src/comm/modbus_tcp_comm.o"
	@echo "... src/comm/modbus_tcp_comm.i"
	@echo "... src/comm/modbus_tcp_comm.s"
	@echo "... src/config/config_manager.o"
	@echo "... src/config/config_manager.i"
	@echo "... src/config/config_manager.s"
	@echo "... src/config/modbus_point_parser.o"
	@echo "... src/config/modbus_point_parser.i"
	@echo "... src/config/modbus_point_parser.s"
	@echo "... src/config/point_table_loader.o"
	@echo "... src/config/point_table_loader.i"
	@echo "... src/config/point_table_loader.s"
	@echo "... src/config/xml_config_parser.o"
	@echo "... src/config/xml_config_parser.i"
	@echo "... src/config/xml_config_parser.s"
	@echo "... src/data/data_point_manager.o"
	@echo "... src/data/data_point_manager.i"
	@echo "... src/data/data_point_manager.s"
	@echo "... src/device/device_manager.o"
	@echo "... src/device/device_manager.i"
	@echo "... src/device/device_manager.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/redis/redis_manager.o"
	@echo "... src/redis/redis_manager.i"
	@echo "... src/redis/redis_manager.s"
	@echo "... src/redis/redis_message_handler.o"
	@echo "... src/redis/redis_message_handler.i"
	@echo "... src/redis/redis_message_handler.s"
	@echo "... src/redis/redis_publisher.o"
	@echo "... src/redis/redis_publisher.i"
	@echo "... src/redis/redis_publisher.s"
	@echo "... src/redis/redis_subscriber.o"
	@echo "... src/redis/redis_subscriber.i"
	@echo "... src/redis/redis_subscriber.s"
	@echo "... src/service/modbus_service.o"
	@echo "... src/service/modbus_service.i"
	@echo "... src/service/modbus_service.s"
	@echo "... src/tests/test_logger.o"
	@echo "... src/tests/test_logger.i"
	@echo "... src/tests/test_logger.s"
	@echo "... src/tests/test_rtu_comm.o"
	@echo "... src/tests/test_rtu_comm.i"
	@echo "... src/tests/test_rtu_comm.s"
	@echo "... src/tests/test_tcp_comm.o"
	@echo "... src/tests/test_tcp_comm.i"
	@echo "... src/tests/test_tcp_comm.s"
	@echo "... src/tests/test_thread_pool.o"
	@echo "... src/tests/test_thread_pool.i"
	@echo "... src/tests/test_thread_pool.s"
	@echo "... src/utils/logger.o"
	@echo "... src/utils/logger.i"
	@echo "... src/utils/logger.s"
	@echo "... src/utils/thread_pool.o"
	@echo "... src/utils/thread_pool.i"
	@echo "... src/utils/thread_pool.s"
	@echo "... src/utils/utils.o"
	@echo "... src/utils/utils.i"
	@echo "... src/utils/utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

