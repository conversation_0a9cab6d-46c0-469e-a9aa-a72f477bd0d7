=============================================
Migration notes from the 2.0 series (for 3.0)
=============================================

The 3.0 release use a brand new API and this document covers only the general
changes:

- the structure modbus_param_t is gone and is replaced by a new opaque and
dynamically allocated structure modbus_t.

- the slave argument is no more an argument of the Modbus functions, you need to
  call modbus_set_slave first.

- the public header file is smaller so some internal defines aren't accessible
  anymore.

- all function and constants are respectively prefixed by modbus_ or MODBUS_.

- the POSIX error conventions are used (if an error occured, -1 or NULL is
  returned and errno is set accordingly).

- coil status and discretes inputs are just bits and force/preset actions have
  been renamed to write actions.

We hope you'll enjoy the new API to accept the migration burden!

=============================================
Migration notes from the 1.2 series (for 2.0)
=============================================

Init
====

modbus_init_tcp requires a third new argument, the port number.

modbus_init_tcp(modbus_param_t *mb_param, char *ip_address, int port)

Set the port to MODBUS_TCP_DEFAULT_PORT to use the default one
(502). It's convenient to use a port number greater than or equal to
1024 because it's not necessary to be root to use this port number.


Pointers of data
================

The coil and input status are now stored in an array of type uint8_t
(in 1.2.X series, array of type int was used).  So now, you need to
pass a pointer of type uint8_t to use read_coil_status(), for example.

The holding and input registers are now stored in an array of type
uint16_t.

These changes reduce the memory consumption.
